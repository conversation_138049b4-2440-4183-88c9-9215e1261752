variables:
- group: deployment-variables
steps:
- powershell: |
    # Get PR ID
    $header = @{ Authorization = "Bearer $(System.AccessToken)"  }
    $pullRequestURL = "$(System.TeamFoundationCollectionUri)$(System.TeamProject)/_apis/git/repositories/$(Build.Repository.Name)/pullrequests/?searchCriteria.status=3&top=50&searchCriteria.targetRefName=refs/heads/$(Build.SourceBranchName)&api-version=6.1-preview"
    $pullRequests = Invoke-RestMethod -Uri $pullRequestURL -Method Get -Headers $header -ContentType application/json
    $prId = $pullRequests.value[0].pullRequestId
    Write-Host "Last PR completed to $(Build.SourceBranchName) is: $prId"
    
    # Extract source branch name
    $sourceBranchName = $pullRequests.value[0].sourceRefName -replace "refs/heads/"
    Write-Host "Source branch name is: $($sourceBranchName)"

    # Get commit ID
    $commitUrl = "$(System.TeamFoundationCollectionUri)$(System.TeamProject)/_apis/git/repositories/$(Build.Repository.Name)/pullrequests/$prId/commits?api-version=6.1-preview"
    $commitResponse = Invoke-RestMethod -Uri $commitUrl -Method Get -Headers $header -ContentType application/json
    $sourceBranchCommitID = $commitResponse.value[0].commitId
    Write-Host "Commit ID for the last PR: $sourceBranchCommitID"

    $url = "$(System.TeamFoundationCollectionUri)$(System.TeamProject)/_apis/git/repositories/$(Build.Repository.Name)/commits?api-version=6.1-preview"
    $commit = Invoke-RestMethod -Uri $url -Method Get -Headers $header -ContentType application/json
    $currentBranchCommitID = $commit.value[1].commitId
    Write-Host "Last commit ID: $currentBranchCommitID"

    if ($sourceBranchCommitID -ne $currentBranchCommitID) {
      Write-Host "Source branch commit ID ($sourceBranchCommitID) is not equal to the current branch commit ID ($currentBranchCommitID). Exiting pipeline."
      return
    }

  displayName: 'Check Commit IDs and End Pipeline if Not Equal'
- powershell: |
    $securePassword = ConvertTo-SecureString -String "$(Password)" -AsPlainText -Force
    $BSTR = [System.Runtime.InteropServices.Marshal]::SecureStringToBSTR($securePassword)
    $PlainPassword = [System.Runtime.InteropServices.Marshal]::PtrToStringAuto($BSTR)
    Write-Host "##vso[task.setvariable variable=username;]$(Username)"
    Write-Host "##vso[task.setvariable variable=plainPassword;]$PlainPassword"
  displayName: 'Generating Credentials'
  condition: ne(variables['sourceBranchCommitID'], variables['currentBranchCommitID'])
- powershell: |
    $securePassword = ConvertTo-SecureString -String "$(plainPassword)" -AsPlainText -Force
    $credentials = New-Object System.Management.Automation.PSCredential ("$(username)", $securePassword)
    $session = New-PSSession -ComputerName $(ServerName) -Credential $credentials
    Invoke-Command -Session $session -ScriptBlock { docker ps -a -q --filter ancestor=$(Build.Repository.Name):$sourceBranchName | ForEach-Object { docker stop $_; docker rm $_ } }
    Invoke-Command -Session $session -ScriptBlock {
      $images = docker images | Where-Object { $_ -match "$sourceBranchName" }
      if ($images) {
        $images | ForEach-Object { 
          $id = $_.Split(" ")[2]
          docker rmi -f $id
        }
      }
    }
  displayName: 'Delete all images containing old branch tag'
  condition: ne(variables['sourceBranchCommitID'], variables['currentBranchCommitID'])
