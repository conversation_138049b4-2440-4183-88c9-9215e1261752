﻿using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

#pragma warning disable CA1814 // Prefer jagged arrays over multidimensional

namespace SSO.Database.Migrations
{
    /// <inheritdoc />
    public partial class AddedWorkTimeRole : Migration
    {
        /// <inheritdoc />
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.AddColumn<Guid>(
                name: "WorkTimeRoleId",
                table: "Users",
                type: "uniqueidentifier",
                nullable: true);

            migrationBuilder.CreateIndex(
                name: "IX_Users_WorkTimeRoleId",
                table: "Users",
                column: "WorkTimeRoleId");

            migrationBuilder.AddForeignKey(
                name: "FK_Users_Roles_WorkTimeRoleId",
                table: "Users",
                column: "WorkTimeRoleId",
                principalTable: "Roles",
                principalColumn: "Id");
        }

        /// <inheritdoc />
        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropForeignKey(
                name: "FK_Users_Roles_WorkTimeRoleId",
                table: "Users");

            migrationBuilder.DropIndex(
                name: "IX_Users_WorkTimeRoleId",
                table: "Users");

            migrationBuilder.DropColumn(
                name: "WorkTimeRoleId",
                table: "Users");
        }
    }
}
