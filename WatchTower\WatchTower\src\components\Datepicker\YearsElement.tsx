import styled, { css } from "styled-components";
import { ViewMode } from "../../models/Enums/ViewMode";

const ContainerYear = styled.div`
  display: flex;
  justify-content: center;
  align-items: center;
  background-color: var(--datepicker-view-buttons-color);
  padding: 0.5em;
  cursor: pointer;
`;

const ContainerYears = styled.div<{ active: ViewMode }>`
  display: none;
  grid-template-columns: repeat(4, 1fr);
  grid-template-rows: repeat(3, 1fr);
  gap: 0.3em;
  padding: 0.5em;
  border: solid 0.2em;
  border-color: var(--datepicker-view-buttons-color);
  background-color: var(--datepicker-view-border);
  border-radius: 1em;
  width: 15em;

  ${ContainerYear}:hover {
    background-color: var(--datepicker-view-buttons-hover-color);
  }
  ${({ active }) =>
    active === ViewMode.YearsView &&
    `
    display: grid;
  `}
`;

interface YearsElementProps {
  handleYearsClick: (selectedYear: number) => void;
  selectedGroupIndex: number;
  active: ViewMode;
}

const YearsElement: React.FC<YearsElementProps> = ({
  handleYearsClick,
  selectedGroupIndex,
  active,
}) => {
  const startYear = 1900;
  const endYear = 2100;
  const groupSize = 12;

  const years: number[] = [];
  for (let year = startYear; year <= endYear; year++) {
    years.push(year);
  }

  const startYearIndex = selectedGroupIndex * groupSize;
  const endYearIndex = (selectedGroupIndex + 1) * groupSize;

  const yearGroups = years.slice(startYearIndex, endYearIndex);

  const populateYears = (yearGroup: number[]) => {
    return yearGroup.map((year) => (
      <ContainerYear
        key={`years-${year}`}
        onClick={() => handleYearsClick(year)}
      >
        {year}
      </ContainerYear>
    ));
  };

  return (
    <ContainerYears data-testid="years-container" active={active}>
      {populateYears(yearGroups)}
    </ContainerYears>
  );
};

export default YearsElement;
