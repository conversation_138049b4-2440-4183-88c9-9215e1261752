﻿using Gateway.Common.Extenstions.EndpointExtensions;
using Gateway.Common.Redis.Models;
using Gateway.Common.Redis.Services;
using Gateway.Common.Redis.Services.Interfaces;
using Gateway.Common.Requests.Companies;
using Gateway.Connections;
using Gateway.Connections.Handlers;
using Gateway.Connections.Interfaces;
using Gateway.Extenstions.MediatorExtensions;
using Gateway.Requests.UserRegistration;
using Gateway.Requests.UserRegistration.Companies;
using WorkTimeApi.Common;
using WorkTimeApi.Common.DTOs.Companies;
using WorkTimeApi.Common.Requests.Roles;

namespace Gateway.EndpointDefinitions.Companies
{
    public class CompaniesEndpointDefinitions : IEndpointDefinition
    {
        public void DefineEndpoints(WebApplication app)
        {
            app.AuthenticatedGet<GetCompaniesRequest>("/companies")
               .AuthenticatedGet<GetSenderaCompanyRequest>("/company")
               .AuthenticatedGet<GetSenderaCompaniesRequest>("/sendera-companies")
               .AuthenticatedPost<JoinCompanyRequest>("/join-company")
               .AuthenticatedGet<GetCompanyByEmailAndBulstatRequest>("/company/{email}/{bulstat}")
               .AuthenticatedPost<RequestJoinCompany>("join-company-request")
               .AuthenticatedPost<CreateNewCompanyRequest>("/company")
               .AuthenticatedPost<AddEmployeeCompanyRequest>("/add-employee-company")
               .AuthenticatedPost<WorkTimeApi.Common.Requests.Companies.ShareCompanyRequest>("/company/share")
               .AuthenticatedPut<WorkTimeApi.Common.Requests.Companies.EditCompanyRequest>("/company")
               .AuthenticatedPut<CompanyInvitationRequest>("/company/invitation", DefaultPermissions.Attendances.Read)
               .AuthenticatedPost<ChangeEmployeeRoleRequest>("/company/user/roles");
        }

        public void DefineServices(IServiceCollection services)
        {
            services
                .AddHttpClient<IWorkTimeApiConnection, WorkTimeApiConnection>()
                .AddHttpMessageHandler<RequestIdHandler>()
                .AddHttpMessageHandler<UserIdHandler>()
                .AddHttpMessageHandler<AddOriginHeaderHandler>()
                .AddHttpMessageHandler<AddOriginIdHeaderHandler>();

            services.AddSingleton<IHttpContextAccessor, HttpContextAccessor>();

            services.AddSingleton<IRedisService<RedisCompanyDTO>>(sp =>
            {
                var configuration = sp.GetRequiredService<IConfiguration>();
                var redisKeyPrefix = "company";
                var redisKeyAllPrefix = "companies:all";

                return new RedisService<RedisCompanyDTO>(configuration, redisKeyPrefix, redisKeyAllPrefix);
            });
        }
    }
}