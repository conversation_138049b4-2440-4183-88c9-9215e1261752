﻿using MediatR;
using WorkTimeApi.Common.Requests.Notifications;
using WorkTimeApi.Services.Interfaces.Notifications;

namespace WorkTimeApi.Handlers.Notifications.EmployeeNotifications
{
    public class MarkNotificationAsReadHandler : IRequestHandler<ReadNotificationRequest, IResult>
    {
        private readonly INotificationsService _notificationsService;

        public MarkNotificationAsReadHandler(INotificationsService notificationsService)
        {
            _notificationsService = notificationsService;
        }

        public async Task<IResult> Handle(ReadNotificationRequest request, CancellationToken cancellationToken)
        {
            return Results.Ok(await _notificationsService.MarkNotificationAsRead(request.NotificationId));
        }
    }
}
