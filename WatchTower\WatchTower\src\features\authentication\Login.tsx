import { <PERSON><PERSON><PERSON>, MouseE<PERSON>, useContext, useState } from "react";
import Button from "../../components/Inputs/Button";
import { LoginUserDTO } from "../../models/DTOs/LoginUserDTO";
import { useNavigate, useParams } from "react-router-dom";
import { login } from "../../services/authentication/authenticationService";
import Form from "../../components/Form/Form";
import { AuthContext } from "./AuthContext";
import Alert from "../../components/Inputs/Alert";
import styled from "styled-components";
import Container from "../../components/Container";
import Checkbox from "../../components/Inputs/Checkbox";
import { isValidEmail } from "../../services/emailService";
import EmailBox from "../../components/Inputs/EmailBox";
import PasswordBox from "../../components/Inputs/PasswordBox";
import { LOCAL_STORAGE_HAS_SIGNED_IN } from "../../constants/local-storage-constants";
import { EmployeeDTO } from "../../models/DTOs/payrolls/EmployeeDTO";

const LoginButton = styled(Button)`
  width: 100%;
`;

const FooterContainer = styled(Container)`
  display: flex;
  margin-top: 0.4rem;
  padding-left: 1rem;
  padding-right: 1rem;
  width: 100%;
  position: relative;
`;

const RememberMeCheckbox = styled(Checkbox)`
  position: absolute;
  left: 1rem;
`;

const ForgottenPasswordButton = styled(Button)`
  position: absolute;
  right: 3.125rem;
  background: none;
  color: black;
  font-size: 1rem;
  font-weight: 400;
  padding: 0;

  &:hover {
    color: var(--button-forgotten-password-color-hover);
  }
`;

interface Props {
  returnAfterLogin?: string;
}

const Login = ({ returnAfterLogin }: Props) => {
  const { setUser } = useContext(AuthContext);
  const [rememberMeChecked, setRememberMeChecked] = useState(true);
  const [showErrorMessage, setShowErrorMessage] = useState<boolean>(false);
  const { email } = useParams();
  const [currentUser, setCurrentUser] = useState({
    email: email,
  } as LoginUserDTO);
  const [isLoignBtnActive, setIsLoginBtnActive] = useState(false);
  const navigate = useNavigate();

  const handleEmailChange = (e: ChangeEvent<HTMLInputElement>) => {
    setCurrentUser({
      ...currentUser,
      email: e.currentTarget.value,
    });
    setShowErrorMessage(false);

    setIsLoginBtnActive(
      isValidEmail(e.currentTarget.value) && currentUser.password.length > 0
    );
  };

  const handlePasswordChange = (e: ChangeEvent<HTMLInputElement>) => {
    setCurrentUser({
      ...currentUser,
      password: e.currentTarget.value,
    });
    setShowErrorMessage(false);

    setIsLoginBtnActive(
      isValidEmail(currentUser.email) && e.currentTarget.value.length > 0
    );
  };

  const handleLoginSubmit = async (e: MouseEvent<HTMLButtonElement>) => {
    e.preventDefault();

    const response = await login(currentUser, rememberMeChecked);

    if (response.ok) {
      const employeeDTO = (await response.json()) as EmployeeDTO;
      setUser({
        email: currentUser.email,
        hasSignedIn: employeeDTO.hasSignedIn,
      });
      localStorage.setItem(
        LOCAL_STORAGE_HAS_SIGNED_IN,
        employeeDTO.hasSignedIn.toString()
      );

      navigate(returnAfterLogin ?? "/");
    }

    setShowErrorMessage(true);
  };

  const onRememberMeClicked = () => {
    setRememberMeChecked(!rememberMeChecked);
  };

  const handleForgottenPasswordClicked = () => {
    navigate(`/auth/forgotten-password/${currentUser.email ?? ""}`);
  };

  return (
    <Form>
      <EmailBox
        name="email"
        handleChange={handleEmailChange}
        label="E-mail"
        placeholder="Fill your email"
        value={currentUser.email}
      />
      <PasswordBox
        name="password"
        handleChange={handlePasswordChange}
        label="Password"
        placeholder="Fill your password"
        type="password"
        value={currentUser.password}
      />
      {showErrorMessage && <Alert message="strWrongCredentials" type="error" />}
      <LoginButton
        onClick={handleLoginSubmit}
        label="Login"
        disabled={!isLoignBtnActive}
      />
      <FooterContainer>
        <RememberMeCheckbox
          isChecked={rememberMeChecked}
          handleChange={onRememberMeClicked}
          label="Remember me"
          name="rememberMe"
        />
        <ForgottenPasswordButton
          label="Forgotten password?"
          onClick={handleForgottenPasswordClicked}
        />
      </FooterContainer>
    </Form>
  );
};

export default Login;
