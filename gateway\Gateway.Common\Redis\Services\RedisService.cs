﻿using Gateway.Common.Redis.Services.Interfaces;
using Microsoft.Extensions.Configuration;
using System.Text.Json.Serialization;
using System.Text.Json;
using StackExchange.Redis;
using Gateway.Common.Redis.Models;

namespace Gateway.Common.Redis.Services
{
    public class RedisService<T> : IRedisService<T>
        where T : class, IRedisRecord
    {
        private readonly IDatabase _db;

        private readonly ISubscriber _subscriber;

        private static string _redisKeyPrefix;

        private static string _redisKeyAllPrefix;

        public RedisService(IConfiguration configuration, string redisKeyPrefix, string redisKeyAllPrefix)
        {
            _redisKeyPrefix = redisKeyPrefix.ToLowerInvariant();
            _redisKeyAllPrefix = redisKeyAllPrefix.ToLowerInvariant();

            var redis = ConnectionMultiplexer.Connect(configuration.GetConnectionString("Redis") ?? throw new Exception("Invalid connection Redis string"));
            _db = redis.GetDatabase();
            _subscriber = redis.GetSubscriber();

            _subscriber.Subscribe(RedisChannel.Literal($"{_redisKeyPrefix}:added"), (channel, message) =>
            {
                var record = JsonSerializer.Deserialize<T>(message);
                if (record != null && OnAdded != null)
                    OnAdded(record);
            });

            _subscriber.Subscribe(RedisChannel.Literal($"{_redisKeyPrefix}:removed"), (channel, message) =>
            {
                var record = JsonSerializer.Deserialize<T>(message);
                if (record != null && OnRemoved != null)
                    OnRemoved(record);
            });
        }

        public async Task AddAsync(List<T> records)
        {
            var existingIdsJson = await _db.StringGetAsync(_redisKeyAllPrefix);
            var existingIds = string.IsNullOrEmpty(existingIdsJson)
                ? new List<string>()
                : JsonSerializer.Deserialize<List<string>>(existingIdsJson!) ?? new List<string>();

            foreach (var record in records)
            {
                var ids = record.GetKeys().Select(k => k.ToLowerInvariant());
                foreach (var id in ids)
                {
                    if (id is null)
                        continue;

                    var options = new JsonSerializerOptions
                    {
                        ReferenceHandler = ReferenceHandler.IgnoreCycles,
                        MaxDepth = 0
                    };

                    var json = JsonSerializer.Serialize(record, options);

                    await _db.StringSetAsync(GetKey(id), json);

                    if (!id.Contains(":") && !existingIds.Contains(id))
                        existingIds.Add(id);

                    await _subscriber
                        .PublishAsync(RedisChannel.Literal($"{typeof(T).Name.ToLowerInvariant()}:added"), json);
                }
            }

            await _db.StringSetAsync(_redisKeyAllPrefix, JsonSerializer.Serialize(existingIds));
        }

        public List<T> GetAll()
        {
            var idsJson = _db.StringGet(_redisKeyAllPrefix);
            if (string.IsNullOrEmpty(idsJson))
                return new List<T>();

            var ids = JsonSerializer.Deserialize<List<string>>(idsJson!) ?? new List<string>();
            var records = new List<T>();

            foreach (var id in ids)
            {
                var json = _db.StringGet(GetKey(id));
                if (!string.IsNullOrEmpty(json))
                {
                    var record = JsonSerializer.Deserialize<T>(json!);
                    if (record != null)
                        records.Add(record);
                }
            }

            return records;
        }

        public T? GetByKey(string id)
        {
            var json = _db.StringGet(GetKey(id));
            return string.IsNullOrEmpty(json)
                ? null
                : JsonSerializer.Deserialize<T>(json);
        }

        public T? GetByKey(int key) => GetByKey(key.ToString());

        public async Task<bool> RemoveByKeysAsync(List<string> keys)
        {
            foreach (var key in keys)
            {
                var recordKey = GetKey(key);
                var existing = await _db.StringGetAsync(recordKey);

                if (!existing.HasValue)
                    return false;

                await _db.KeyDeleteAsync(recordKey);

                var idsJson = await _db.StringGetAsync(_redisKeyAllPrefix);
                var ids = string.IsNullOrEmpty(idsJson)
                    ? new List<string>()
                    : JsonSerializer.Deserialize<List<string>>(idsJson!) ?? new List<string>();

                var id = key.Contains(":") ? key.Split(':')[1] : key;

                ids.Remove(id);

                await _db.StringSetAsync(_redisKeyAllPrefix, JsonSerializer.Serialize(ids));

                await _subscriber.PublishAsync(
                    RedisChannel.Literal($"{_redisKeyPrefix}:removed"),
                    existing
                );
            }

            return true;
        }

        public Action<T>? OnAdded { get; set; }

        public Action<T>? OnRemoved { get; set; }

        private string GetKey(string id)
        {
            return $"{_redisKeyPrefix}:{id}".ToLowerInvariant();
        }

        public List<T> GetManyByKey(string key)
        {
            var server = _db.Multiplexer.GetServer(_db.Multiplexer.GetEndPoints().First());
            var pattern = $"{_redisKeyPrefix}:{key.ToLowerInvariant()}:*";

            var keys = server.Keys(pattern: pattern).ToArray();
            var results = new List<T>();

            foreach (var redisKey in keys)
            {
                var json = _db.StringGet(redisKey);
                if (!string.IsNullOrEmpty(json))
                {
                    var record = JsonSerializer.Deserialize<T>(json!);
                    if (record != null)
                        results.Add(record);
                }
            }

            return results;
        }
    }
}
