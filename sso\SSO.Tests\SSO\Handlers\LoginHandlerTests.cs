﻿using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Http.HttpResults;
using SSO.Handlers;
using SSO.Common.Requests;

namespace SSO.Tests.SSO.Handlers
{
    public class LoginHandlerTests
    {
        //[Fact]
        //public async Task LoginHandlerReturnsOkWithBasicToken()
        //{
        //    // Arrange
        //    var loginHandler = new LoginHandler();
        //    var loginRequest = new LoginRequest
        //    {
        //        HttpRequest = new DefaultHttpContext().Request,
        //        Username = "TestUsername",
        //        Password = "TestPassword"
        //    };
        //    var cancellationToken = new CancellationToken();

        //    // Act
        //    var response = await loginHandler.Handle(loginRequest, cancellationToken);
        //    var okResponse = (Ok<string>)response;

        //    // Assert
        //    var expectedResult = "VGVzdFVzZXJuYW1lOlRlc3RQYXNzd29yZA==";
        //    Assert.NotNull(okResponse);
        //    Assert.Equal(200, okResponse.StatusCode);
        //    Assert.Equal(expectedResult, okResponse.Value);
        //    Assert.Equal($"Basic {expectedResult}", loginRequest.HttpRequest.HttpContext.Response.Headers["Authentication"].FirstOrDefault());
        //}
    }
}
