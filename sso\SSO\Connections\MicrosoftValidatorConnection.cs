﻿using Azure.Core;
using SSO.Connections.Interfaces;

namespace SSO.Connections
{
    public class MicrosoftValidatorConnection : IMicrosoftValidatorConnection
    {
        private readonly HttpClient _httpClient;
        private const string MICROSOFT_API_URL = "https://graph.microsoft.com/v1.0/me/";

        public MicrosoftValidatorConnection(HttpClient httpClient)
        {
            _httpClient = httpClient;
        }

        public Task<HttpResponseMessage> ValidateMicrosoftTokenAsync(string token)
        {
            _httpClient.DefaultRequestHeaders.Add("Authorization", $"Bearer {token}");

            return _httpClient.GetAsync(MICROSOFT_API_URL);
        }
    }
}
