import React from "react";
import { render, screen, fireEvent } from "@testing-library/react";
import { DropdownContext } from "../Context";
import Dropdown from "../Dropdown";
import "@testing-library/jest-dom";

describe("Dropdown Component", () => {
  it("renders children correctly", () => {
    render(
      <Dropdown>
        <div data-testid="child-component">Test Child</div>
      </Dropdown>
    );

    const child = screen.getByTestId("child-component");
    expect(child).toBeInTheDocument();
    expect(child.textContent).toBe("Test Child");
  });

  it("provides the expected context values", () => {
    const TestComponent: React.FC = () => {
      return (
        <DropdownContext.Consumer>
          {({ isOpen, setIsOpen }) => (
            <div>
              <div data-testid="isOpen">{isOpen ? "Open" : "Closed"}</div>
              <button data-testid="toggle" onClick={() => setIsOpen(!isOpen)}>
                Toggle
              </button>
            </div>
          )}
        </DropdownContext.Consumer>
      );
    };

    render(
      <Dropdown>
        <TestComponent />
      </Dropdown>
    );

    expect(screen.getByTestId("isOpen").textContent).toBe("Closed");

    const button = screen.getByTestId("toggle");
    fireEvent.click(button);
    expect(screen.getByTestId("isOpen").textContent).toBe("Open");
  });
});
