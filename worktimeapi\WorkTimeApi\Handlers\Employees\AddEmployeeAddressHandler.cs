﻿using AutoMapper;
using MediatR;
using WorkTimeApi.Common.Requests.Employees;
using WorkTimeApi.Services.Interfaces.Addresses;

namespace WorkTimeApi.Handlers.Employees
{    public class AddEmployeeAddressHandler(IAddressesService addressesService, IMapper mapper) : IRequestHandler<AddEmployeeAddressRequest, IResult>
    {
        public async Task<IResult> Handle(AddEmployeeAddressRequest request, CancellationToken cancellationToken)
        {
            var result = await addressesService.AddEmployeeAddressAsync(request.Address);

            return Results.Ok(result);
        }
    }
}
