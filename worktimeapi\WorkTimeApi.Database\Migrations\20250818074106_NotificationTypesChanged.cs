﻿using System;
using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

#pragma warning disable CA1814 // Prefer jagged arrays over multidimensional

namespace WorkTimeApi.Database.Migrations
{
    /// <inheritdoc />
    public partial class NotificationTypesChanged : Migration
    {
        /// <inheritdoc />
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DeleteData(
                table: "NotificationTypes",
                keyColumn: "Id",
                keyValue: new Guid("034c7d5f-8c37-3b81-ff68-133eb0ffad22"));

            migrationBuilder.DeleteData(
                table: "NotificationTypes",
                keyColumn: "Id",
                keyValue: new Guid("128ee027-ea26-db74-5183-c41bb0218a01"));

            migrationBuilder.DeleteData(
                table: "NotificationTypes",
                keyColumn: "Id",
                keyValue: new Guid("19c800ba-d860-0e29-2dc0-d90dfede8057"));

            migrationBuilder.DeleteData(
                table: "NotificationTypes",
                keyColumn: "Id",
                keyValue: new Guid("26efe49f-52fa-2c72-f3f7-e9000797dff6"));

            migrationBuilder.DeleteData(
                table: "NotificationTypes",
                keyColumn: "Id",
                keyValue: new Guid("48832005-691d-3784-b996-16a563b7d138"));

            migrationBuilder.DeleteData(
                table: "NotificationTypes",
                keyColumn: "Id",
                keyValue: new Guid("666de694-7dda-1e4c-fa68-6e827822aeff"));

            migrationBuilder.DeleteData(
                table: "NotificationTypes",
                keyColumn: "Id",
                keyValue: new Guid("9c125211-363c-a12a-c657-60dff07cb6b4"));

            migrationBuilder.DeleteData(
                table: "NotificationTypes",
                keyColumn: "Id",
                keyValue: new Guid("a7844298-9ec2-98cb-7949-b3196367962b"));

            migrationBuilder.DeleteData(
                table: "NotificationTypes",
                keyColumn: "Id",
                keyValue: new Guid("a9627558-76fa-1f6f-2dd5-526ac0404221"));

            migrationBuilder.DeleteData(
                table: "NotificationTypes",
                keyColumn: "Id",
                keyValue: new Guid("a99a9df8-95f4-91b2-eff6-68ce79c4dbb6"));

            migrationBuilder.DeleteData(
                table: "NotificationTypes",
                keyColumn: "Id",
                keyValue: new Guid("c651caf8-2883-5f13-4725-ba460ed3e174"));

            migrationBuilder.DeleteData(
                table: "NotificationTypes",
                keyColumn: "Id",
                keyValue: new Guid("e3830b70-9f86-66e3-8c11-fa06ae781adc"));

            migrationBuilder.DeleteData(
                table: "NotificationTypes",
                keyColumn: "Id",
                keyValue: new Guid("e4d2dc50-0547-5ad6-ee39-ce6bbd2067b7"));

            migrationBuilder.DeleteData(
                table: "NotificationTypes",
                keyColumn: "Id",
                keyValue: new Guid("f96e9129-7291-e8a1-11af-baf3af65d531"));

            migrationBuilder.AddColumn<string>(
                name: "CreatorName",
                table: "Notifications",
                type: "nvarchar(max)",
                nullable: true);

            migrationBuilder.InsertData(
                table: "NotificationGroup",
                columns: new[] { "Id", "Name" },
                values: new object[,]
                {
                    { new Guid("2c4561b6-c2bd-6458-af90-c556d22b4655"), "PendingEmployees" },
                    { new Guid("7947c82b-5460-51c0-667e-e4170b25ae41"), "Hospitals" },
                    { new Guid("7f77574c-2b87-de3d-00dc-e84ef6cfce05"), "Payrolls" }
                });

            migrationBuilder.InsertData(
                table: "NotificationTypes",
                columns: new[] { "Id", "Name", "NotificationGroupId", "PermissionId" },
                values: new object[,]
                {
                    { new Guid("03a98271-716e-ce58-3c54-773be993dee5"), "Notifications.Absences.Edit.Push", new Guid("b40cf8f3-e598-0cf0-804f-92cfad87239d"), null },
                    { new Guid("04c06892-**************-8324f6e6d564"), "Notifications.Employees.Edited.ByAdmin.Push", new Guid("946c62eb-1e53-54c5-f93b-2b78a77c8b1b"), null },
                    { new Guid("06218af6-c8f1-ce1b-1d43-34570ac6ab3b"), "Notifications.Employees.EditedAddresses.ByEmployee.Push", new Guid("946c62eb-1e53-54c5-f93b-2b78a77c8b1b"), new Guid("7dd10ffd-497d-c5ee-a3e4-42ca56a5480c") },
                    { new Guid("1373a24e-4ccc-320b-7091-6bce15e1615b"), "Notifications.Employees.Edited.ByAdmin.Email", new Guid("946c62eb-1e53-54c5-f93b-2b78a77c8b1b"), null },
                    { new Guid("1431c03d-744b-82e1-12ac-805b5659fac7"), "Notifications.Absences.Edited.ConfirmedAbsence.ByEmployee.Push", new Guid("b40cf8f3-e598-0cf0-804f-92cfad87239d"), new Guid("8aefd196-c542-90b2-5eb5-a4bad7ee97af") },
                    { new Guid("19e958b7-081e-eeb0-0b9f-fc1ec10f5a39"), "Notifications.Employees.Edited.Addresses.ByAdmin.Email", new Guid("946c62eb-1e53-54c5-f93b-2b78a77c8b1b"), null },
                    { new Guid("1b8a783b-95be-69d0-8f59-a46750a5a63c"), "Notifications.Employees.Edited.ByEmployee.Push", new Guid("946c62eb-1e53-54c5-f93b-2b78a77c8b1b"), new Guid("7dd10ffd-497d-c5ee-a3e4-42ca56a5480c") },
                    { new Guid("2a689750-83d2-5dbc-956e-************"), "Notifications.Absences.Delete.Push", new Guid("b40cf8f3-e598-0cf0-804f-92cfad87239d"), null },
                    { new Guid("34a4523b-970a-4140-8114-225011961b55"), "Notifications.Absences.DeletedRequest.ByEmployee.Push", new Guid("b40cf8f3-e598-0cf0-804f-92cfad87239d"), new Guid("8aefd196-c542-90b2-5eb5-a4bad7ee97af") },
                    { new Guid("38d3c8ae-ed20-2802-2363-995189d5409c"), "Notifications.Absences.Declined.Email", new Guid("b40cf8f3-e598-0cf0-804f-92cfad87239d"), null },
                    { new Guid("3a45a3b6-7585-8dd0-5b3b-391d60fa2a64"), "Notifications.Employees.Edited.ByEmployee.Email", new Guid("946c62eb-1e53-54c5-f93b-2b78a77c8b1b"), new Guid("7dd10ffd-497d-c5ee-a3e4-42ca56a5480c") },
                    { new Guid("451b852c-5825-787c-704b-178c7bd97113"), "Notifications.Absences.Added.ByEmployee.Email", new Guid("b40cf8f3-e598-0cf0-804f-92cfad87239d"), new Guid("8aefd196-c542-90b2-5eb5-a4bad7ee97af") },
                    { new Guid("4982385d-8b3e-9b99-f430-afcf6fd02389"), "Notifications.Absences.Edited.ByAdmin.Email", new Guid("b40cf8f3-e598-0cf0-804f-92cfad87239d"), null },
                    { new Guid("4ca67be1-3a53-058e-46d4-2cb8e0c8ab72"), "Notifications.Employees.Edit.Confirmed.Email", new Guid("946c62eb-1e53-54c5-f93b-2b78a77c8b1b"), null },
                    { new Guid("54e545c7-b585-e4f1-2bae-c205c0b7c195"), "Notifications.Absences.DeleteDeclined.Email", new Guid("b40cf8f3-e598-0cf0-804f-92cfad87239d"), null },
                    { new Guid("6591c6de-ce93-9ab1-9097-f8b772f4c1de"), "Notifications.Absences.Edited.ByEmployee.Push", new Guid("b40cf8f3-e598-0cf0-804f-92cfad87239d"), new Guid("8aefd196-c542-90b2-5eb5-a4bad7ee97af") },
                    { new Guid("67fa76e7-3e4e-14a3-d238-035ea9d10233"), "Notifications.Employees.EditedAddresses.ByEmployee.Email", new Guid("946c62eb-1e53-54c5-f93b-2b78a77c8b1b"), new Guid("7dd10ffd-497d-c5ee-a3e4-42ca56a5480c") },
                    { new Guid("6d7222b3-7629-a272-53eb-************"), "Notifications.Absences.Delete.Email", new Guid("b40cf8f3-e598-0cf0-804f-92cfad87239d"), null },
                    { new Guid("6dff4722-9457-9033-5e68-48d854c10370"), "Notifications.Absences.Approved.Push", new Guid("b40cf8f3-e598-0cf0-804f-92cfad87239d"), null },
                    { new Guid("6f7c729a-0636-bfe0-7812-9d81851e797b"), "Notifications.Absences.DeleteDeclined.Push", new Guid("b40cf8f3-e598-0cf0-804f-92cfad87239d"), null },
                    { new Guid("7c8412be-e8c5-0d8b-7fe9-e697621d6365"), "Notifications.Employees.Edit.Declined.Email", new Guid("946c62eb-1e53-54c5-f93b-2b78a77c8b1b"), null },
                    { new Guid("8293b974-a911-05c1-0462-fb531535f5c3"), "Notifications.Employees.Edit.Declined.Push", new Guid("946c62eb-1e53-54c5-f93b-2b78a77c8b1b"), null },
                    { new Guid("8f195e70-9079-2d77-4f6c-0468040b455f"), "Notifications.Absences.Edited.ConfirmedAbsence.Declined.ByEmployee.Push", new Guid("b40cf8f3-e598-0cf0-804f-92cfad87239d"), null },
                    { new Guid("90b87bab-85b7-bcc7-695d-7632d969b06e"), "Notifications.Absences.Approved.Email", new Guid("b40cf8f3-e598-0cf0-804f-92cfad87239d"), null },
                    { new Guid("9f0e6bf0-6f51-ffa3-95d5-f2625d6b87aa"), "Notifications.Absences.Edited.ConfirmedAbsence.ByEmployee.Email", new Guid("b40cf8f3-e598-0cf0-804f-92cfad87239d"), new Guid("8aefd196-c542-90b2-5eb5-a4bad7ee97af") },
                    { new Guid("a6d06499-d7d3-6327-1a13-ee1afe8e8116"), "Notifications.Absences.Declined.Push", new Guid("b40cf8f3-e598-0cf0-804f-92cfad87239d"), null },
                    { new Guid("a9189178-160a-a2e7-a87c-c452edf858be"), "Notifications.Absences.Added.ByEmployee.Push", new Guid("b40cf8f3-e598-0cf0-804f-92cfad87239d"), new Guid("8aefd196-c542-90b2-5eb5-a4bad7ee97af") },
                    { new Guid("abaa7e68-77a6-5f93-81a9-81fb9e361883"), "Notifications.Absences.Delete.ByAdmin.Email", new Guid("b40cf8f3-e598-0cf0-804f-92cfad87239d"), null },
                    { new Guid("b76a122d-416e-c2c7-7e6d-cadf4d5fcd02"), "Notifications.Employees.Edited.Addresses.ByAdmin.Push", new Guid("946c62eb-1e53-54c5-f93b-2b78a77c8b1b"), null },
                    { new Guid("b9fe2882-d6b8-799f-60bc-c3b0345bf41e"), "Notifications.Absences.DeletedRequest.ByEmployee.Email", new Guid("b40cf8f3-e598-0cf0-804f-92cfad87239d"), new Guid("8aefd196-c542-90b2-5eb5-a4bad7ee97af") },
                    { new Guid("be6a3311-63f8-8e3d-0d2f-7f6a54425f2d"), "Notifications.Absences.Edited.ByEmployee.Email", new Guid("b40cf8f3-e598-0cf0-804f-92cfad87239d"), new Guid("8aefd196-c542-90b2-5eb5-a4bad7ee97af") },
                    { new Guid("c0514a7a-4700-2770-519f-534fc91fb6e5"), "Notifications.Absences.Edit.Email", new Guid("b40cf8f3-e598-0cf0-804f-92cfad87239d"), null },
                    { new Guid("d4e61dad-e700-ad2c-79f0-8bdd2b54a424"), "Notifications.Employees.Edit.Confirmed.Push", new Guid("946c62eb-1e53-54c5-f93b-2b78a77c8b1b"), null },
                    { new Guid("d6346c76-d00f-3ea3-f9fb-d0b972b6d2d6"), "Notifications.Absences.Edited.ByAdmin.Push", new Guid("b40cf8f3-e598-0cf0-804f-92cfad87239d"), null },
                    { new Guid("f7b1d4a4-798e-3505-dddd-9daa7305ff83"), "Notifications.Absences.Edited.ConfirmedAbsence.Declined.ByEmployee.Email", new Guid("b40cf8f3-e598-0cf0-804f-92cfad87239d"), null },
                    { new Guid("fcb0e21e-dfdb-86e5-67fe-bd298ac65fa6"), "Notifications.Absences.Delete.ByAdmin.Push", new Guid("b40cf8f3-e598-0cf0-804f-92cfad87239d"), null },
                    { new Guid("0f0369da-dd3f-8b7a-84d9-9d9e102297e5"), "Notifications.PendingEmployees.Updated.Push", new Guid("2c4561b6-c2bd-6458-af90-c556d22b4655"), new Guid("b1815361-f2d2-d704-4129-f7ad010513ea") },
                    { new Guid("447a0534-33f2-f6c1-ffbf-b27471fe8b47"), "Notifications.Hospital.Added.ByEmployee.Email", new Guid("7947c82b-5460-51c0-667e-e4170b25ae41"), new Guid("8aefd196-c542-90b2-5eb5-a4bad7ee97af") },
                    { new Guid("6f86873c-8fab-bdeb-fa26-6ecc2d1e0c79"), "Notifications.Hospital.Approved.Push", new Guid("7947c82b-5460-51c0-667e-e4170b25ae41"), null },
                    { new Guid("a68c994b-1d18-101c-08cf-9227f7b00d1f"), "Notifications.Payrolls.Added.Push", new Guid("7f77574c-2b87-de3d-00dc-e84ef6cfce05"), new Guid("b1815361-f2d2-d704-4129-f7ad010513ea") },
                    { new Guid("e9303874-e095-4efb-fd58-e35231d91466"), "Notifications.Hospital.Approved.Email", new Guid("7947c82b-5460-51c0-667e-e4170b25ae41"), null },
                    { new Guid("f163e4f9-2c47-f710-b8dd-f57fef127e68"), "Notifications.Hospital.Added.ByEmployee.Push", new Guid("7947c82b-5460-51c0-667e-e4170b25ae41"), new Guid("8aefd196-c542-90b2-5eb5-a4bad7ee97af") }
                });
        }

        /// <inheritdoc />
        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DeleteData(
                table: "NotificationTypes",
                keyColumn: "Id",
                keyValue: new Guid("03a98271-716e-ce58-3c54-773be993dee5"));

            migrationBuilder.DeleteData(
                table: "NotificationTypes",
                keyColumn: "Id",
                keyValue: new Guid("04c06892-**************-8324f6e6d564"));

            migrationBuilder.DeleteData(
                table: "NotificationTypes",
                keyColumn: "Id",
                keyValue: new Guid("06218af6-c8f1-ce1b-1d43-34570ac6ab3b"));

            migrationBuilder.DeleteData(
                table: "NotificationTypes",
                keyColumn: "Id",
                keyValue: new Guid("0f0369da-dd3f-8b7a-84d9-9d9e102297e5"));

            migrationBuilder.DeleteData(
                table: "NotificationTypes",
                keyColumn: "Id",
                keyValue: new Guid("1373a24e-4ccc-320b-7091-6bce15e1615b"));

            migrationBuilder.DeleteData(
                table: "NotificationTypes",
                keyColumn: "Id",
                keyValue: new Guid("1431c03d-744b-82e1-12ac-805b5659fac7"));

            migrationBuilder.DeleteData(
                table: "NotificationTypes",
                keyColumn: "Id",
                keyValue: new Guid("19e958b7-081e-eeb0-0b9f-fc1ec10f5a39"));

            migrationBuilder.DeleteData(
                table: "NotificationTypes",
                keyColumn: "Id",
                keyValue: new Guid("1b8a783b-95be-69d0-8f59-a46750a5a63c"));

            migrationBuilder.DeleteData(
                table: "NotificationTypes",
                keyColumn: "Id",
                keyValue: new Guid("2a689750-83d2-5dbc-956e-************"));

            migrationBuilder.DeleteData(
                table: "NotificationTypes",
                keyColumn: "Id",
                keyValue: new Guid("34a4523b-970a-4140-8114-225011961b55"));

            migrationBuilder.DeleteData(
                table: "NotificationTypes",
                keyColumn: "Id",
                keyValue: new Guid("38d3c8ae-ed20-2802-2363-995189d5409c"));

            migrationBuilder.DeleteData(
                table: "NotificationTypes",
                keyColumn: "Id",
                keyValue: new Guid("3a45a3b6-7585-8dd0-5b3b-391d60fa2a64"));

            migrationBuilder.DeleteData(
                table: "NotificationTypes",
                keyColumn: "Id",
                keyValue: new Guid("447a0534-33f2-f6c1-ffbf-b27471fe8b47"));

            migrationBuilder.DeleteData(
                table: "NotificationTypes",
                keyColumn: "Id",
                keyValue: new Guid("451b852c-5825-787c-704b-178c7bd97113"));

            migrationBuilder.DeleteData(
                table: "NotificationTypes",
                keyColumn: "Id",
                keyValue: new Guid("4982385d-8b3e-9b99-f430-afcf6fd02389"));

            migrationBuilder.DeleteData(
                table: "NotificationTypes",
                keyColumn: "Id",
                keyValue: new Guid("4ca67be1-3a53-058e-46d4-2cb8e0c8ab72"));

            migrationBuilder.DeleteData(
                table: "NotificationTypes",
                keyColumn: "Id",
                keyValue: new Guid("54e545c7-b585-e4f1-2bae-c205c0b7c195"));

            migrationBuilder.DeleteData(
                table: "NotificationTypes",
                keyColumn: "Id",
                keyValue: new Guid("6591c6de-ce93-9ab1-9097-f8b772f4c1de"));

            migrationBuilder.DeleteData(
                table: "NotificationTypes",
                keyColumn: "Id",
                keyValue: new Guid("67fa76e7-3e4e-14a3-d238-035ea9d10233"));

            migrationBuilder.DeleteData(
                table: "NotificationTypes",
                keyColumn: "Id",
                keyValue: new Guid("6d7222b3-7629-a272-53eb-************"));

            migrationBuilder.DeleteData(
                table: "NotificationTypes",
                keyColumn: "Id",
                keyValue: new Guid("6dff4722-9457-9033-5e68-48d854c10370"));

            migrationBuilder.DeleteData(
                table: "NotificationTypes",
                keyColumn: "Id",
                keyValue: new Guid("6f7c729a-0636-bfe0-7812-9d81851e797b"));

            migrationBuilder.DeleteData(
                table: "NotificationTypes",
                keyColumn: "Id",
                keyValue: new Guid("6f86873c-8fab-bdeb-fa26-6ecc2d1e0c79"));

            migrationBuilder.DeleteData(
                table: "NotificationTypes",
                keyColumn: "Id",
                keyValue: new Guid("7c8412be-e8c5-0d8b-7fe9-e697621d6365"));

            migrationBuilder.DeleteData(
                table: "NotificationTypes",
                keyColumn: "Id",
                keyValue: new Guid("8293b974-a911-05c1-0462-fb531535f5c3"));

            migrationBuilder.DeleteData(
                table: "NotificationTypes",
                keyColumn: "Id",
                keyValue: new Guid("8f195e70-9079-2d77-4f6c-0468040b455f"));

            migrationBuilder.DeleteData(
                table: "NotificationTypes",
                keyColumn: "Id",
                keyValue: new Guid("90b87bab-85b7-bcc7-695d-7632d969b06e"));

            migrationBuilder.DeleteData(
                table: "NotificationTypes",
                keyColumn: "Id",
                keyValue: new Guid("9f0e6bf0-6f51-ffa3-95d5-f2625d6b87aa"));

            migrationBuilder.DeleteData(
                table: "NotificationTypes",
                keyColumn: "Id",
                keyValue: new Guid("a68c994b-1d18-101c-08cf-9227f7b00d1f"));

            migrationBuilder.DeleteData(
                table: "NotificationTypes",
                keyColumn: "Id",
                keyValue: new Guid("a6d06499-d7d3-6327-1a13-ee1afe8e8116"));

            migrationBuilder.DeleteData(
                table: "NotificationTypes",
                keyColumn: "Id",
                keyValue: new Guid("a9189178-160a-a2e7-a87c-c452edf858be"));

            migrationBuilder.DeleteData(
                table: "NotificationTypes",
                keyColumn: "Id",
                keyValue: new Guid("abaa7e68-77a6-5f93-81a9-81fb9e361883"));

            migrationBuilder.DeleteData(
                table: "NotificationTypes",
                keyColumn: "Id",
                keyValue: new Guid("b76a122d-416e-c2c7-7e6d-cadf4d5fcd02"));

            migrationBuilder.DeleteData(
                table: "NotificationTypes",
                keyColumn: "Id",
                keyValue: new Guid("b9fe2882-d6b8-799f-60bc-c3b0345bf41e"));

            migrationBuilder.DeleteData(
                table: "NotificationTypes",
                keyColumn: "Id",
                keyValue: new Guid("be6a3311-63f8-8e3d-0d2f-7f6a54425f2d"));

            migrationBuilder.DeleteData(
                table: "NotificationTypes",
                keyColumn: "Id",
                keyValue: new Guid("c0514a7a-4700-2770-519f-534fc91fb6e5"));

            migrationBuilder.DeleteData(
                table: "NotificationTypes",
                keyColumn: "Id",
                keyValue: new Guid("d4e61dad-e700-ad2c-79f0-8bdd2b54a424"));

            migrationBuilder.DeleteData(
                table: "NotificationTypes",
                keyColumn: "Id",
                keyValue: new Guid("d6346c76-d00f-3ea3-f9fb-d0b972b6d2d6"));

            migrationBuilder.DeleteData(
                table: "NotificationTypes",
                keyColumn: "Id",
                keyValue: new Guid("e9303874-e095-4efb-fd58-e35231d91466"));

            migrationBuilder.DeleteData(
                table: "NotificationTypes",
                keyColumn: "Id",
                keyValue: new Guid("f163e4f9-2c47-f710-b8dd-f57fef127e68"));

            migrationBuilder.DeleteData(
                table: "NotificationTypes",
                keyColumn: "Id",
                keyValue: new Guid("f7b1d4a4-798e-3505-dddd-9daa7305ff83"));

            migrationBuilder.DeleteData(
                table: "NotificationTypes",
                keyColumn: "Id",
                keyValue: new Guid("fcb0e21e-dfdb-86e5-67fe-bd298ac65fa6"));

            migrationBuilder.DeleteData(
                table: "NotificationGroup",
                keyColumn: "Id",
                keyValue: new Guid("2c4561b6-c2bd-6458-af90-c556d22b4655"));

            migrationBuilder.DeleteData(
                table: "NotificationGroup",
                keyColumn: "Id",
                keyValue: new Guid("7947c82b-5460-51c0-667e-e4170b25ae41"));

            migrationBuilder.DeleteData(
                table: "NotificationGroup",
                keyColumn: "Id",
                keyValue: new Guid("7f77574c-2b87-de3d-00dc-e84ef6cfce05"));

            migrationBuilder.DropColumn(
                name: "CreatorName",
                table: "Notifications");

            migrationBuilder.InsertData(
                table: "NotificationTypes",
                columns: new[] { "Id", "Name", "NotificationGroupId", "PermissionId" },
                values: new object[,]
                {
                    { new Guid("034c7d5f-8c37-3b81-ff68-133eb0ffad22"), "Notifications.Absences.Added.Push", new Guid("b40cf8f3-e598-0cf0-804f-92cfad87239d"), new Guid("8aefd196-c542-90b2-5eb5-a4bad7ee97af") },
                    { new Guid("128ee027-ea26-db74-5183-c41bb0218a01"), "Notifications.Employees.AddressesUpdated.Email", new Guid("946c62eb-1e53-54c5-f93b-2b78a77c8b1b"), new Guid("7dd10ffd-497d-c5ee-a3e4-42ca56a5480c") },
                    { new Guid("19c800ba-d860-0e29-2dc0-d90dfede8057"), "Notifications.Absences.Added.Email", new Guid("b40cf8f3-e598-0cf0-804f-92cfad87239d"), new Guid("8aefd196-c542-90b2-5eb5-a4bad7ee97af") },
                    { new Guid("26efe49f-52fa-2c72-f3f7-e9000797dff6"), "Notifications.Employees.EditedByAdmin.Email", new Guid("946c62eb-1e53-54c5-f93b-2b78a77c8b1b"), null },
                    { new Guid("48832005-691d-3784-b996-16a563b7d138"), "Notifications.Absences.EditedByAdmin.Push", new Guid("b40cf8f3-e598-0cf0-804f-92cfad87239d"), null },
                    { new Guid("666de694-7dda-1e4c-fa68-6e827822aeff"), "Notifications.Employees.Updated.Push", new Guid("946c62eb-1e53-54c5-f93b-2b78a77c8b1b"), new Guid("7dd10ffd-497d-c5ee-a3e4-42ca56a5480c") },
                    { new Guid("9c125211-363c-a12a-c657-60dff07cb6b4"), "Notifications.Employees.AddressesEditedByAdmin.Email", new Guid("946c62eb-1e53-54c5-f93b-2b78a77c8b1b"), null },
                    { new Guid("a7844298-9ec2-98cb-7949-b3196367962b"), "Notifications.Employees.Updated.Email", new Guid("946c62eb-1e53-54c5-f93b-2b78a77c8b1b"), new Guid("7dd10ffd-497d-c5ee-a3e4-42ca56a5480c") },
                    { new Guid("a9627558-76fa-1f6f-2dd5-526ac0404221"), "Notifications.Absences.Updated.Push", new Guid("b40cf8f3-e598-0cf0-804f-92cfad87239d"), new Guid("8aefd196-c542-90b2-5eb5-a4bad7ee97af") },
                    { new Guid("a99a9df8-95f4-91b2-eff6-68ce79c4dbb6"), "Notifications.Employees.AddressesUpdated.Push", new Guid("946c62eb-1e53-54c5-f93b-2b78a77c8b1b"), new Guid("7dd10ffd-497d-c5ee-a3e4-42ca56a5480c") },
                    { new Guid("c651caf8-2883-5f13-4725-ba460ed3e174"), "Notifications.Employees.EditedByAdmin.Push", new Guid("946c62eb-1e53-54c5-f93b-2b78a77c8b1b"), null },
                    { new Guid("e3830b70-9f86-66e3-8c11-fa06ae781adc"), "Notifications.Employees.AddressesEditedByAdmin.Push", new Guid("946c62eb-1e53-54c5-f93b-2b78a77c8b1b"), null },
                    { new Guid("e4d2dc50-0547-5ad6-ee39-ce6bbd2067b7"), "Notifications.Absences.EditedByAdmin.Email", new Guid("b40cf8f3-e598-0cf0-804f-92cfad87239d"), null },
                    { new Guid("f96e9129-7291-e8a1-11af-baf3af65d531"), "Notifications.Absences.Updated.Email", new Guid("b40cf8f3-e598-0cf0-804f-92cfad87239d"), new Guid("8aefd196-c542-90b2-5eb5-a4bad7ee97af") }
                });
        }
    }
}
