import { configureStore, ThunkAction, Action } from "@reduxjs/toolkit";
import { reducer as watchHealthChecks } from "../features/watchers/watchersActions";

export const store = configureStore({
  reducer: {
    watchHealthChecks: watchHealthChecks,
  },
});

export type AppDispatch = typeof store.dispatch;
export type RootState = ReturnType<typeof store.getState>;
export type AppThunk<ReturnType, ActionType> = ThunkAction<
  ReturnType,
  RootState,
  unknown,
  Action<ActionType>
>;
