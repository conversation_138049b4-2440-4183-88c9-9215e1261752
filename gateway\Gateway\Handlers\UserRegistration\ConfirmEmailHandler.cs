﻿using AutoMapper;
using Gateway.Connections.Interfaces;
using Gateway.Requests.UserRegistration;
using MediatR;
using Microinvest.TransferFiles.Tools.Models.Users;
using System.Net;

namespace Gateway.Handlers.UserRegistration
{
	public class ConfirmEmailHandler : IRequestHandler<ConfirmEmailRequest, IResult>
	{
		private readonly IUserRegistrationConnection _userRegistrationConnection;
		private readonly IMapper _mapper;

		public ConfirmEmailHandler(IUserRegistrationConnection userRegistrationConnection, IMapper mapper)
		{
			_userRegistrationConnection = userRegistrationConnection;
			_mapper = mapper;
		}

		public async Task<IResult> Handle(ConfirmEmailRequest request, CancellationToken cancellationToken)
		{
			var response = await _userRegistrationConnection.ConfirmEmailAsync(_mapper.Map<ConfirmEmailDTO>(request));

			if (response.StatusCode != HttpStatusCode.OK)
				return Results.BadRequest();

			return Results.Text(await response.Content.ReadAsStringAsync(cancellationToken));
		}
	}
}
