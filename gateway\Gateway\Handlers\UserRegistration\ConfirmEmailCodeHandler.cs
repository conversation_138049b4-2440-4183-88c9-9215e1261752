using Gateway.Connections.Interfaces;
using MediatR;
using WorkTimeApi.Common.Requests.Users;

namespace Gateway.Handlers.UserRegistration
{
    public class ConfirmEmailCodeHandler(IWorkTimeApiConnection workTimeApiConnection, IUserRegistrationConnection userRegistrationConnection, IConfiguration configuration) : IRequestHandler<ConfirmEmailCodeRequest, IResult>
    {
        public async Task<IResult> Handle(ConfirmEmailCodeRequest request, CancellationToken cancellationToken)
        {
            var workTimeResponse = await workTimeApiConnection.ConfirmEmailByCodeAsync(request.Email, request.Code);

            if (workTimeResponse is null || !workTimeResponse.IsSuccessStatusCode)
                return Results.BadRequest("Invalid confirmation code");

            var workTimeUrl = configuration["WorkTimeUrl"];
            var urResponse = await userRegistrationConnection.ConfirmEmailByCodeAsync(request.Email, request.Code);

            return urResponse is null || !urResponse.IsSuccessStatusCode
                ? Results.BadRequest("Failed to update user registration")
                : Results.Redirect($"{workTimeUrl}email-confirmed-by-code");
        }
    }
}
