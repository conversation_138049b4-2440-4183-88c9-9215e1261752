﻿using Gateway.Common.Results;
using Gateway.Connections.Interfaces;
using MediatR;
using WorkTimeApi.Common.Requests.Nomenclatures;

namespace Gateway.Handlers.Nomenclatures
{
	public class TerminationReasonHandler : IRequestHandler<GetTerminationReasonRequest, IResult>
	{
		private readonly IWorkTimeApiConnection _workTimeApiConnection;

		public TerminationReasonHandler(IWorkTimeApiConnection workTimeApiConnection)
		{
			_workTimeApiConnection = workTimeApiConnection;
		}

		public async Task<IResult> Handle(GetTerminationReasonRequest request, CancellationToken cancellationToken)
		{
			var terminationReason = await _workTimeApiConnection.GetTerminationReasonAsync();

			return new HttpResponseMessageResult(terminationReason);
		}
	}
}
