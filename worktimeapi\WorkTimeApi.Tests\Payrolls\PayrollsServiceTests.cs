using AutoMapper;
using Moq;
using WorkTimeApi.Common.Enums;
using WorkTimeApi.Common.Requests.Payrolls;
using WorkTimeApi.Database.Models;
using WorkTimeApi.Database.Repositories.Interfaces.Payrolls;
using WorkTimeApi.Mappers;
using WorkTimeApi.Services.Payrolls;

namespace WorkTimeApi.Tests.Payrolls;

public class PayrollsServiceTests
{
    private static (PayrollsService service, Mock<IPayrollsRepository> repo) CreateService()
    {
        var mapperConfig = new MapperConfiguration(cfg => cfg.AddProfile<MapperProfile>());
        IMapper mapper = mapperConfig.CreateMapper();

        var repo = new Mock<IPayrollsRepository>(MockBehavior.Strict);
        var service = new PayrollsService(repo.Object, mapper);
        return (service, repo);
    }

    [Fact]
    public async Task LoadLightPayrollsAsync_Uses_Last_Annex_By_FromDate_For_StructureLevel()
    {
        var (service, repo) = CreateService();

        var companyId = Guid.NewGuid();
        var structureLevelMain = new StructureLevel { Id = Guid.NewGuid(), Name = "Main", Type = StructureLevelType.Department, CompanyId = companyId };
        var structureLevelAnnex1 = new StructureLevel { Id = Guid.NewGuid(), Name = "Annex1", Type = StructureLevelType.Department, CompanyId = companyId };
        var structureLevelAnnex2 = new StructureLevel { Id = Guid.NewGuid(), Name = "Annex2", Type = StructureLevelType.Department, CompanyId = companyId };

        var company = new Company { Id = companyId, Name = "Comp", Bulstat = "B" };
        var employee = new Employee { Id = Guid.NewGuid(), CompanyId = companyId, Company = company, UserId = Guid.NewGuid(), User = new User() };

        var payroll = new Payroll
        {
            Id = Guid.NewGuid(),
            CompanyId = companyId,
            Company = company,
            EmployeeId = employee.Id,
            Employee = employee,
            StructureLevelId = structureLevelMain.Id,
            StructureLevel = structureLevelMain
        };

        var annexOld = new AnnexPayroll
        {
            Id = Guid.NewGuid(),
            PayrollId = payroll.Id,
            MainPayroll = payroll,
            FromDate = new DateTime(2024, 1, 1),
            StructureLevelId = structureLevelAnnex1.Id,
            StructureLevel = structureLevelAnnex1
        };

        var annexNew = new AnnexPayroll
        {
            Id = Guid.NewGuid(),
            PayrollId = payroll.Id,
            MainPayroll = payroll,
            FromDate = new DateTime(2024, 6, 1),
            StructureLevelId = structureLevelAnnex2.Id,
            StructureLevel = structureLevelAnnex2
        };

        payroll.AnnexPayrolls = new List<AnnexPayroll> { annexOld, annexNew };

        repo.Setup(r => r.GetPayrollsAsync(companyId))
            .ReturnsAsync(new List<Payroll> { payroll });

        var result = await service.LoadLightPayrollsAsync(new LoadPayrollsRequest { CompanyId = companyId });
        var dto = Assert.Single(result);
        Assert.Equal(structureLevelAnnex2.Id, dto.StructureLevelId);

        repo.VerifyAll();
    }
}
