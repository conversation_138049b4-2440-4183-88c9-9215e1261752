﻿using Gateway.Connections.Interfaces;
using WorkTimeApi.Common.Requests.Templates;
using MediatR;

namespace Gateway.Handlers.Templates
{
	public class AddTemplateHandler : IRequestHandler<AddTemplateRequest, IResult>
	{
		private readonly IWorkTimeApiConnection _workTimeApiConnection;

		public AddTemplateHandler(IWorkTimeApiConnection workTimeApiConnection)
		{
			_workTimeApiConnection = workTimeApiConnection;
		}

		public async Task<IResult> Handle(AddTemplateRequest request, CancellationToken cancellationToken)
		{
			var response = await _workTimeApiConnection.AddTemplateAsync(request);

			if (!response.IsSuccessStatusCode)
				return Results.StatusCode((int)response.StatusCode);

			return Results.Text(await response.Content.ReadAsStringAsync(cancellationToken));
		}
	}
}
