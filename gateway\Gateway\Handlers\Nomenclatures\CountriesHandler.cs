﻿using Gateway.Common.Results;
using Gateway.Connections.Interfaces;
using MediatR;
using WorkTimeApi.Common.Requests.Nomenclatures;

namespace Gateway.Handlers.Nomenclatures
{
	public class CountriesHandler : IRequestHandler<GetCountriesRequest, IResult>
	{
		private readonly IWorkTimeApiConnection _workTimeApiConnection;

		public CountriesHandler(IWorkTimeApiConnection workTimeApiConnection)
		{
			_workTimeApiConnection = workTimeApiConnection;
		}

		public async Task<IResult> Handle(GetCountriesRequest request, CancellationToken cancellationToken)
		{
			var countries = await _workTimeApiConnection.GetCountriesAsync();

			return new HttpResponseMessageResult(countries);
		}
	}
}
