﻿using Gateway.Extenstions.MediatorExtensions;
using WorkTimeApi.Common.Requests.Absences;
using Gateway.Common.Extenstions.EndpointExtensions;

namespace Gateway.EndpointDefinitions.Absences
{
    public class AbsencesAndHospitalsEndpointDefinitions : IEndpointDefinition
    {
        public void DefineEndpoints(WebApplication app)
        {
            app.AuthenticatedGet<AbsencesAndHospitalsRequest>($"/absences/monthly");
        }

        public void DefineServices(IServiceCollection services)
        {
        }
    }
}
