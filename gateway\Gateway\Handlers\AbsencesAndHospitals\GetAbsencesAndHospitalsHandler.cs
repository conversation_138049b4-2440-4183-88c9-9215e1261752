﻿using Gateway.Common.Results;
using Gateway.Connections.Interfaces;
using MediatR;
using WorkTimeApi.Common.Requests.Absences;

namespace Gateway.Handlers.AbsencesAndHospitals
{
    public class GetAbsencesAndHospitalsHandler : IRequestHandler<AbsencesAndHospitalsRequest, IResult>
    {
        private readonly IWorkTimeApiConnection _workTimeApiConnection;

        public GetAbsencesAndHospitalsHandler(IWorkTimeApiConnection workTimeApiConnection)
        {
            _workTimeApiConnection = workTimeApiConnection;
        }

        public async Task<IResult> Handle(AbsencesAndHospitalsRequest request, CancellationToken cancellationToken)
        {
            var response = await _workTimeApiConnection.GetAbsencesAndHospitalsAsync(request);

            return new HttpResponseMessageResult(response);
        }
    }

}
