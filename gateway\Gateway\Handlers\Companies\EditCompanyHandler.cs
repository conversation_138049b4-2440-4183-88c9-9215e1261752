﻿using AutoMapper;
using Gateway.Common.Results;
using Gateway.Connections.Interfaces;
using MediatR;
using Microinvest.TransferFiles.Tools.Models.Companies;
using WorkTimeApi.Common.Requests.Companies;

namespace Gateway.Handlers.Companies
{
    public class EditCompanyHandler(IUserRegistrationConnection userRegistrationConnection, IWorkTimeApiConnection workTimeApiConnection, IMapper mapper) : IRequestHandler<EditCompanyRequest, IResult>
    {
        public async Task<IResult> Handle(EditCompanyRequest request, CancellationToken cancellationToken)
        {
            var companyResponse = await userRegistrationConnection.EditCompanyAsync(mapper.Map<CompanyDTO>(request));

            var editedUserRegistrationCompany = await companyResponse.Content.ReadFromJsonAsync<CompanyDTO>(cancellationToken: cancellationToken);

            var editCompanyRequest = mapper.Map<EditCompanyRequest>(editedUserRegistrationCompany);
            editCompanyRequest.Id = request.Id;

            var response = await workTimeApiConnection.EditCompanyAsync(editCompanyRequest);

            if (response.IsSuccessStatusCode)
            {
                await userRegistrationConnection.EditCompanyAsync(new CompanyDTO
                {
                    Id = request.UserRegistrationCompanyId,
                    Name = request.Name,
                    Bulstat = request.Bulstat,
                    ContactName = request.ContactName,
                });
            }

            return new HttpResponseMessageResult(response);
        }
    }
}
