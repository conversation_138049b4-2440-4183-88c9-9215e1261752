using MediatR;
using WorkTimeApi.Common.Requests.Employees;
using WorkTimeApi.Services.Interfaces.Addresses;

namespace WorkTimeApi.Handlers.Employees
{
    public class DeleteEmployeeAddressHandler(IAddressesService addressesService) : IRequestHandler<DeleteEmployeeAddressRequest, IResult>
    {
        public async Task<IResult> Handle(DeleteEmployeeAddressRequest request, CancellationToken cancellationToken)
        {
            var success = await addressesService.DeleteEmployeeAddressAsync(request.EmployeeId, request.AddressId);
            return success ? Results.Ok() : Results.NotFound();
        }
    }
}

