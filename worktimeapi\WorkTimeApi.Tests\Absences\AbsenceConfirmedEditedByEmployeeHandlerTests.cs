using MediatR;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Primitives;
using Moq;
using WorkTime.Notifications.Services.Interfaces;
using WorkTimeApi.Common;
using WorkTimeApi.Common.DTOs.Absences;
using WorkTimeApi.Common.DTOs.Employees;
using WorkTimeApi.Common.Enums;
using WorkTimeApi.Common.Notifications;
using WorkTimeApi.Common.Notifications.Absences;
using WorkTimeApi.Database.Models;
using WorkTimeApi.Database.Repositories.Interfaces.Companies;
using WorkTimeApi.Handlers.Notifications.Absences;
using WorkTimeApi.Services.Interfaces.Absences;
using WorkTimeApi.Services.Interfaces.Notifications;

namespace WorkTimeApi.Tests.Absences;

public class AbsenceConfirmedEditedByEmployeeHandlerTests
{
    private static AbsenceHospitalDTO CreateAbsence()
    {
        return new AbsenceHospitalDTO
        {
            Id = Guid.NewGuid(),
            FromDate = DateTime.UtcNow.Date,
            ToDate = DateTime.UtcNow.Date.AddDays(1),
            TypeIdentifier = EventType.ПлатенГодишенОтпуск,
            Status = AbsenceStatus.Approved,
            Reference = "New comment"
        };
    }

    private static AbsenceHospitalDTO CreateOldAbsence()
    {
        return new AbsenceHospitalDTO
        {
            Id = Guid.NewGuid(),
            FromDate = DateTime.UtcNow.Date.AddDays(-1),
            ToDate = DateTime.UtcNow.Date,
            TypeIdentifier = EventType.ПлатенГодишенОтпуск,
            Status = AbsenceStatus.Pending,
            Reference = "Old comment"
        };
    }

    private static EmployeeDTO CreateEmployee()
    {
        return new EmployeeDTO
        {
            WorkTimeId = Guid.NewGuid(),
            FirstName = "John",
            LastName = "Doe",
            Email = "<EMAIL>"
        };
    }

    private static Company CreateCompany()
    {
        return new Company
        {
            Id = Guid.NewGuid(),
            Name = "Test Company",
            Bulstat = "*********"
        };
    }

    [Fact]
    public async Task Handle_WithValidData_SendsNotificationsAndEmails()
    {
        // Arrange
        var signalR = new Mock<ISignalRNotificationService>();
        var notificationsService = new Mock<INotificationsService>();
        var emailsNotificationService = new Mock<IEmailsNotificationService>();
        var companiesRepository = new Mock<ICompaniesRepository>();
        var absencesService = new Mock<IAbsencesService>();
        var configuration = new Mock<IConfiguration>();

        var companyId = Guid.NewGuid();
        var userId = Guid.NewGuid();
        var absence = CreateAbsence();
        var oldAbsence = CreateOldAbsence();
        var employee = CreateEmployee();
        var company = CreateCompany();

        var notification = new AbsenceConfirmedEditedByEmployeeNotification(
            absence,
            oldAbsence,
            companyId,
            userId,
            "John Doe");

        var employeesToNotify = new List<EmployeeDTO> { employee };

        notificationsService.Setup(n => n.GetEmployeesToNotifyAsync(
                companyId,
                NotificationsName.Absences.EditedConfirmedAbsenceByEmployee.Email))
            .ReturnsAsync(employeesToNotify);

        absencesService.Setup(a => a.GetEmployeeDTOByAbsenceIdAsync(absence.Id, absence.IsHospital))
            .ReturnsAsync(employee);

        companiesRepository.Setup(c => c.GetByIdAsync(companyId))
            .ReturnsAsync(company);

        var configurationSection = new Mock<IConfigurationSection>();
        configurationSection.Setup(s => s.Value).Returns("https://worktime.company.com");
        configuration.Setup(c => c.GetSection("WorkTimeUrl"))
            .Returns(configurationSection.Object);

        var handler = new AbsenceConfirmedEditedByEmployeeHandler(
            signalR.Object,
            notificationsService.Object,
            emailsNotificationService.Object,
            companiesRepository.Object,
            absencesService.Object,
            configuration.Object);

        // Act
        await handler.Handle(notification, CancellationToken.None);

        // Assert
        notificationsService.Verify(n => n.GetEmployeesToNotifyAsync(
            companyId,
            NotificationsName.Absences.EditedConfirmedAbsenceByEmployee.Email), Times.Once);

        absencesService.Verify(a => a.GetEmployeeDTOByAbsenceIdAsync(absence.Id, absence.IsHospital), Times.Once);

        companiesRepository.Verify(c => c.GetByIdAsync(companyId), Times.Once);

        configuration.Verify(c => c.GetSection("WorkTimeUrl"), Times.Once);

        // Verify that notifications are sent to employees
        notificationsService.Verify(n => n.AddNotificationByUserIdAndCompanyIdAsync(
            employee.WorkTimeId, notification), Times.Once);

        signalR.Verify(s => s.NotifyUser(It.IsAny<Guid>(), It.IsAny<BaseNotification<AbsenceHospitalDTO>>()), Times.Once);

        // Verify that emails are sent
        emailsNotificationService.Verify(e => e.SendEmailsAsync(
            It.Is<IEnumerable<string>>(emails => emails.Contains(employee.Email)),
            "absences/confirm-edit-byemployee",
            It.Is<object>(req =>
                req.GetType().GetProperty("AbsenceTypeOld") != null &&
                req.GetType().GetProperty("StartDateOld") != null &&
                req.GetType().GetProperty("EndDateOld") != null &&
                req.GetType().GetProperty("CommentOld") != null
            )), Times.Once);
    }

    [Fact]
    public async Task Handle_WithMultipleEmployees_SendsNotificationsToAll()
    {
        // Arrange
        var signalR = new Mock<ISignalRNotificationService>();
        var notificationsService = new Mock<INotificationsService>();
        var emailsNotificationService = new Mock<IEmailsNotificationService>();
        var companiesRepository = new Mock<ICompaniesRepository>();
        var absencesService = new Mock<IAbsencesService>();
        var configuration = new Mock<IConfiguration>();

        var companyId = Guid.NewGuid();
        var userId = Guid.NewGuid();
        var absence = CreateAbsence();
        var oldAbsence = CreateOldAbsence();
        var employee1 = CreateEmployee();
        var employee2 = new EmployeeDTO
        {
            WorkTimeId = Guid.NewGuid(),
            FirstName = "Jane",
            LastName = "Smith",
            Email = "<EMAIL>"
        };
        var company = CreateCompany();

        var notification = new AbsenceConfirmedEditedByEmployeeNotification(
            absence,
            oldAbsence,
            companyId,
            userId,
            "John Doe");

        var employeesToNotify = new List<EmployeeDTO> { employee1, employee2 };

        notificationsService.Setup(n => n.GetEmployeesToNotifyAsync(
                companyId,
                NotificationsName.Absences.EditedConfirmedAbsenceByEmployee.Email))
            .ReturnsAsync(employeesToNotify);

        absencesService.Setup(a => a.GetEmployeeDTOByAbsenceIdAsync(absence.Id, absence.IsHospital))
            .ReturnsAsync(employee1);

        companiesRepository.Setup(c => c.GetByIdAsync(companyId))
            .ReturnsAsync(company);

        var configurationSection = new Mock<IConfigurationSection>();
        configurationSection.Setup(s => s.Value).Returns("https://worktime.company.com");
        configuration.Setup(c => c.GetSection("WorkTimeUrl"))
            .Returns(configurationSection.Object);

        var handler = new AbsenceConfirmedEditedByEmployeeHandler(
            signalR.Object,
            notificationsService.Object,
            emailsNotificationService.Object,
            companiesRepository.Object,
            absencesService.Object,
            configuration.Object);

        // Act
        await handler.Handle(notification, CancellationToken.None);

        // Assert
        // Verify that notifications are sent to both employees
        notificationsService.Verify(n => n.AddNotificationByUserIdAndCompanyIdAsync(
            employee1.WorkTimeId, notification), Times.Once);
        notificationsService.Verify(n => n.AddNotificationByUserIdAndCompanyIdAsync(
            employee2.WorkTimeId, notification), Times.Once);

        signalR.Verify(s => s.NotifyUser(It.IsAny<Guid>(), It.IsAny<BaseNotification<AbsenceHospitalDTO>>()), Times.Exactly(2));

        // Verify that emails are sent to both employees
        emailsNotificationService.Verify(e => e.SendEmailsAsync(
            It.Is<IEnumerable<string>>(emails =>
                emails.Contains(employee1.Email) && emails.Contains(employee2.Email)),
            "absences/confirm-edit-byemployee",
            It.IsAny<object>()), Times.Once);
    }

    [Fact]
    public async Task Handle_WithNullOldAbsence_ThrowsException()
    {
        // Arrange
        var signalR = new Mock<ISignalRNotificationService>();
        var notificationsService = new Mock<INotificationsService>();
        var emailsNotificationService = new Mock<IEmailsNotificationService>();
        var companiesRepository = new Mock<ICompaniesRepository>();
        var absencesService = new Mock<IAbsencesService>();
        var configuration = new Mock<IConfiguration>();

        var companyId = Guid.NewGuid();
        var userId = Guid.NewGuid();
        var absence = CreateAbsence();

        var notification = new AbsenceConfirmedEditedByEmployeeNotification(
            absence,
            null!, // This should cause an issue when trying to access OldAbsence properties
            companyId,
            userId,
            "John Doe");

        var handler = new AbsenceConfirmedEditedByEmployeeHandler(
            signalR.Object,
            notificationsService.Object,
            emailsNotificationService.Object,
            companiesRepository.Object,
            absencesService.Object,
            configuration.Object);

        // Act & Assert
        await Assert.ThrowsAsync<Exception>(() =>
            handler.Handle(notification, CancellationToken.None));
    }

    [Fact]
    public async Task Handle_WithMissingWorkTimeUrl_ThrowsException()
    {
        // Arrange
        var signalR = new Mock<ISignalRNotificationService>();
        var notificationsService = new Mock<INotificationsService>();
        var emailsNotificationService = new Mock<IEmailsNotificationService>();
        var companiesRepository = new Mock<ICompaniesRepository>();
        var absencesService = new Mock<IAbsencesService>();
        var configuration = new Mock<IConfiguration>();

        var companyId = Guid.NewGuid();
        var userId = Guid.NewGuid();
        var absence = CreateAbsence();
        var oldAbsence = CreateOldAbsence();
        var employee = CreateEmployee();
        var company = CreateCompany();

        var notification = new AbsenceConfirmedEditedByEmployeeNotification(
            absence,
            oldAbsence,
            companyId,
            userId,
            "John Doe");

        var employeesToNotify = new List<EmployeeDTO> { employee };

        notificationsService.Setup(n => n.GetEmployeesToNotifyAsync(
                companyId,
                NotificationsName.Absences.EditedConfirmedAbsenceByEmployee.Email))
            .ReturnsAsync(employeesToNotify);

        absencesService.Setup(a => a.GetEmployeeDTOByAbsenceIdAsync(absence.Id, absence.IsHospital))
            .ReturnsAsync(employee);

        companiesRepository.Setup(c => c.GetByIdAsync(companyId))
            .ReturnsAsync(company);

        var configurationSection = new Mock<IConfigurationSection>();
        configurationSection.Setup(s => s.Value).Returns((string)null);
        configuration.Setup(c => c.GetSection("WorkTimeUrl"))
            .Returns(configurationSection.Object);

        var handler = new AbsenceConfirmedEditedByEmployeeHandler(
            signalR.Object,
            notificationsService.Object,
            emailsNotificationService.Object,
            companiesRepository.Object,
            absencesService.Object,
            configuration.Object);

        // Act & Assert
        var exception = await Assert.ThrowsAsync<Exception>(() =>
            handler.Handle(notification, CancellationToken.None));
        Assert.Equal("Невалидна настройка на WorkTimeUrl в appsettings!", exception.Message);
    }
}
