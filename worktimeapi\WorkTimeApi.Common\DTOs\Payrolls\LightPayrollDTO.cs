using WorkTimeApi.Common.DTOs.Absences;
using WorkTimeApi.Common.DTOs.Employees;
using WorkTimeApi.Common.DTOs.Nomenclatures;

namespace WorkTimeApi.Common.DTOs.Payrolls
{
    public class LightPayrollDTO
    {
        public Guid Id { get; set; }

        public LightEmployeeDTO Employee { get; set; } = null!;

        public Guid CompanyId { get; set; }

        public string? ContractNumber { get; set; }

        public NomenclatureDTO? Position { get; set; }

        public Guid StructureLevelId { get; set; }

        public int? ContractType { get; set; }

        public int AnnualPaidLeave { get; set; }

        public int AdditionalAnnualPaidLeave { get; set; }

        public int AnnualPaidLeavePastYears { get; set; }

        public ICollection<AbsenceHospitalDTO> Leaves { get; set; } = new List<AbsenceHospitalDTO>();

        public ICollection<LightAnnexPayrollDTO> AnnexPayrolls{ get; set; } = new List<LightAnnexPayrollDTO>();

        public DateTime? FromDate { get; set; }

        public DateTime? ToDate { get; set; }
    }
}
