﻿using Microsoft.Extensions.Options;
using System.Globalization;
using System.Text.Json;
using System.Text.Json.Serialization;
using WorkTimeApi.Common.DTOs.Calendar;
using WorkTimeApi.Infrastructure.Configuration;
using WorkTimeApi.Database.Services.Interfaces;

namespace WorkTimeApi.Services.Calendar
{
    public class HolidayService : IHolidayService
    {
        private readonly string _folder;
        private readonly string _pattern;

        public HolidayService(IOptions<HolidayOptions> options, IWebHostEnvironment enviroment)
        {
            var fileOptions = options.Value;
            _folder = Path.IsPathRooted(fileOptions.FolderPath) ? fileOptions.FolderPath
                : Path.Combine(enviroment.ContentRootPath, fileOptions.FolderPath);
            _pattern = fileOptions.FileNamePattern;
        }

        public async Task<List<HolidayDTO>> GetHolidaysAsync(int year, int month)
        {
            if (month < 1 || month > 12) 
                return new List<HolidayDTO>();

            var fileName = _pattern.Replace("{year}", year.ToString(CultureInfo.InvariantCulture))
                .Replace("{month}", month.ToString("00", CultureInfo.InvariantCulture));

            var file = Path.Combine(_folder, fileName);

            if (!File.Exists(file))
                return new List<HolidayDTO>();

            await using var stream = File.OpenRead(file);

            var options = new JsonSerializerOptions
            {
                PropertyNameCaseInsensitive = true,
                Converters = { new JsonStringEnumConverter() }
            };

            var data = await JsonSerializer.DeserializeAsync<List<HolidayDTO>>(stream, options)
                        ?? new List<HolidayDTO>();

            var result = new List<HolidayDTO>();

            foreach (var element in data)
            {
                if (DateOnly.TryParseExact(element.Date, "yyyy-MM-dd", CultureInfo.InvariantCulture, DateTimeStyles.None, out DateOnly date)
                    && date.Month == month)
                {
                    result.Add(element);
                }
            }

            return result;
        }
    }
}

