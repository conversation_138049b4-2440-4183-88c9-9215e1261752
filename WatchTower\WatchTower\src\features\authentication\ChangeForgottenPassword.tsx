import { useNavigate, useParams } from "react-router-dom";
import Container from "../../components/Container";
import { ChangeEvent, MouseEvent, useState } from "react";
import { changeForgottenPassword } from "../../services/authentication/authenticationService";
import Textbox from "../../components/Inputs/Textbox";
import Label from "../../components/Inputs/Label";
import styled from "styled-components";
import Button from "../../components/Inputs/Button";
import MainWindowContainer from "../../components/MainWindowContainer";

const ChangeForgottenPasswordContainer = styled(MainWindowContainer)`
  width: clamp(30%, 20rem, 100%);
  display: flex;
  justify-content: center;
  align-items: center;
  margin: 0 auto;
`;

const DataContainer = styled(Container)`
  width: 100%;
`;

const ChangePasswordButton = styled(Button)`
  width: 100%;
`;

const ChangeForgottenPassword = () => {
  const { email, code } = useParams();
  const [password, setPassword] = useState("");
  const [confirmPassword, setConfirmPassword] = useState("");
  const [isChangePasswordButtonActive, setIsChangePasswordButtonActive] =
    useState(false);
  const navigate = useNavigate();

  const handlePasswordChange = (e: ChangeEvent<HTMLInputElement>) => {
    setPassword(e.currentTarget.value);

    setIsChangePasswordButtonActive(
      e.currentTarget.value !== undefined &&
        e.currentTarget.value !== "" &&
        e.currentTarget.value === confirmPassword
    );
  };

  const handleConfirmPasswordChange = (e: ChangeEvent<HTMLInputElement>) => {
    setConfirmPassword(e.currentTarget.value);

    setIsChangePasswordButtonActive(
      e.currentTarget.value !== undefined &&
        e.currentTarget.value !== "" &&
        e.currentTarget.value === password
    );
  };

  const handleChangePasswordClicked = (e: MouseEvent<HTMLButtonElement>) => {
    e.preventDefault();

    email &&
      code &&
      changeForgottenPassword({
        changeForgottenPasswordDTO: { email, code, password },
      }).then((isChangedSuccessfully) => {
        if (isChangedSuccessfully) navigate(`/auth/login/${email}`);
      });
  };

  return (
    <ChangeForgottenPasswordContainer>
      <DataContainer>
        <Label>Change your password</Label>
        <Textbox
          label="Password"
          value={password}
          handleChange={handlePasswordChange}
          type="password"
        />
        <Textbox
          label="Confirm Password"
          value={confirmPassword}
          handleChange={handleConfirmPasswordChange}
          type="password"
        />
        <ChangePasswordButton
          disabled={!isChangePasswordButtonActive}
          onClick={handleChangePasswordClicked}
          label="Change Password"
        />
      </DataContainer>
    </ChangeForgottenPasswordContainer>
  );
};

export default ChangeForgottenPassword;
