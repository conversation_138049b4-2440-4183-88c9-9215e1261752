﻿namespace Gateway.Connections.Handlers
{
    public class UserIdHandler : DelegatingHandler
    {
        private readonly IHttpContextAccessor _httpContextAccessor;

        public UserIdHandler(IHttpContextAccessor httpContextAccessor)
        {
            _httpContextAccessor = httpContextAccessor;
        }

        protected override async Task<HttpResponseMessage> SendAsync(HttpRequestMessage request, CancellationToken cancellationToken)
        {
            var requestId = _httpContextAccessor.HttpContext?.Request.Headers["X-User-ID"].FirstOrDefault();

            if (!string.IsNullOrEmpty(requestId))
            {
                request.Headers.TryAddWithoutValidation("X-User-ID", requestId);
            }

            return await base.SendAsync(request, cancellationToken);
        }
    }
}
