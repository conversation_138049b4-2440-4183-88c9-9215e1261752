import { render, screen } from "@testing-library/react";
import { <PERSON><PERSON><PERSON><PERSON>out<PERSON> } from "react-router-dom";
import { Genders } from "../../constants/enum";
import { EmployeeDTO } from "../../models/DTOs/employees/EmployeeDTO";
import { PersonalInformationDTO } from "../../models/DTOs/payrolls/PersonalInformationDTO";
import Menu from "../Menu";

// Mock all the dependencies
const mockToggleMenu = jest.fn();
const mockCloseMenu = jest.fn();
const mockNavigate = jest.fn();
const mockDispatch = jest.fn();

jest.mock("react-router-dom", () => ({
  ...jest.requireActual("react-router-dom"),
  useNavigate: () => mockNavigate,
  useLocation: () => ({ pathname: "/employees" }),
}));

jest.mock("../../app/hooks", () => ({
  useAppDispatch: () => mockDispatch,
}));

jest.mock("../../services/authentication/authenticationService", () => ({
  logout: jest.fn(),
}));

jest.mock("../../services/companies/companiesService", () => ({
  initCompany: jest
    .fn()
    .mockResolvedValue({ id: "company-1", name: "Test Company" }),
}));

jest.mock("../../services/users/userService", () => ({
  initUserEmployee: jest.fn().mockResolvedValue({ id: "user-1" }),
}));

const useMenuMock = jest.fn();
jest.mock("../MenuContext", () => ({
  useMenu: () => useMenuMock(),
}));

jest.mock("../../components/PopUp/ActionModalContext", () => ({
  useModal: () => ({ isModalOpen: false }),
}));

jest.mock("../companies/CompanyContext", () => ({
  useCompany: () => ({
    company: { id: "company-1", name: "Test Company" },
    setCompany: jest.fn(),
    resetCompany: jest.fn(),
  }),
}));

jest.mock("../UserEmployeeContext", () => ({
  useUserEmployee: () => ({
    setUserEmployee: jest.fn(),
    resetUserEmployee: jest.fn(),
  }),
}));

jest.mock("../authentication/AuthContext", () => ({
  useAuth: () => ({
    user: {
      firstName: "John",
      secondName: "Doe",
      lastName: "Smith",
      email: "<EMAIL>",
    },
    resetUser: jest.fn(),
  }),
}));

jest.mock("../absences/AbsenceContext", () => ({
  useAbsence: () => ({
    selectedAbsence: null,
    isEditing: false,
  }),
}));

// Mock all the side menu components
jest.mock("../employees/editEmployee/EditEmployeeDataSideMenu", () => () => (
  <div data-testid="edit-employee-side-menu">Edit Employee Data</div>
));

jest.mock(
  "../employees/editEmployee/EditEmployeeAddressesSideMenu",
  () => () =>
    (
      <div data-testid="edit-employee-addresses-side-menu">
        Edit Employee Addresses
      </div>
    )
);

jest.mock("../employees/newEmployee/NewEmployeeSideMenu", () => () => (
  <div data-testid="new-employee-side-menu">New Employee</div>
));

jest.mock("../employees/employeeProfile/AddAddresses", () => () => (
  <div data-testid="new-address-side-menu">Add Addresses</div>
));

jest.mock("../absences/EmployeeAbsencesSideMenu", () => () => (
  <div data-testid="menu-absence">Employee Absences</div>
));

jest.mock("../companies/EditCompany", () => () => (
  <div data-testid="menu-edit-company">Edit Company</div>
));

jest.mock("../employees/employeeProfile/ProfileSideMenu", () => () => (
  <div data-testid="menu-profile-side-menu">Profile Side Menu</div>
));

jest.mock("../../components/ArrowTip", () => () => (
  <div data-testid="menu-arrow-tip">Arrow Tip</div>
));

jest.mock("../../services/language/Translator", () => ({ getString }: any) => (
  <span data-testid={`translator-${getString}`}>{getString}</span>
));

// Helper function to create mock employee data
const createMockEmployee = (
  overrides: Partial<EmployeeDTO> = {}
): EmployeeDTO => ({
  id: "emp-1",
  workTimeId: "worktime-123",
  firstName: "Jane",
  secondName: "Marie",
  lastName: "Johnson",
  egn: "**********",
  email: "<EMAIL>",
  phone: "+**********",
  workPhone: "+**********",
  idNumber: "ID123456",
  idIssueDate: "2020-01-01",
  idIssuedFrom: "Test Authority",
  gender: Genders.Female,
  birthDate: "1990-01-01",
  birthPlace: "Test City",
  citizenship: "Test Country",
  userId: "user-123",
  companyId: "company-1",
  company: {} as any,
  bankAccounts: [],
  addresses: [],
  ...overrides,
});

// Helper function to create mock profile data
const createMockProfile = (
  employeeOverrides: Partial<EmployeeDTO> = {}
): PersonalInformationDTO => ({
  iban: "BG123456789",
  code: "EMP001",
  employee: createMockEmployee(employeeOverrides),
  addresses: [],
  payrollPersonalData: {} as any,
  employeePropertyEdits: [],
});

// Helper function to render Menu with custom props
const renderMenu = (
  props: { isOpen?: boolean; onToggleMenu?: () => void } = {}
) => {
  const defaultProps = {
    isOpen: true,
    onToggleMenu: mockToggleMenu,
    ...props,
  };

  return render(
    <BrowserRouter>
      <Menu {...defaultProps} />
    </BrowserRouter>
  );
};

// Helper to configure useMenu mock per-test
const setUseMenu = (
  viewData: any,
  activeView: string = "edit-employee",
  overrides: Partial<ReturnType<typeof useMenuMock>> = {}
) => {
  useMenuMock.mockReturnValue({
    currentPage: "/employees",
    closeMenu: mockCloseMenu,
    activeView,
    menuOpenedFrom: "other",
    viewData,
    ...overrides,
  });
};

describe("Menu Component - Employee Header", () => {
  beforeEach(() => {
    jest.clearAllMocks();
    // default state unless a test overrides
    setUseMenu(undefined, "edit-employee");
  });

  describe("getEmployeeHeaderInfo function", () => {
    test("should return employee name and email when employee data is available", () => {
      const mockProfile = createMockProfile({
        firstName: "John",
        secondName: "Michael",
        lastName: "Doe",
        email: "<EMAIL>",
      });

      setUseMenu({ incomingProfile: mockProfile });

      renderMenu();

      expect(screen.getByTestId("menu-edit-employee-name")).toHaveTextContent(
        "John Michael Doe"
      );
      expect(
        screen.getByTestId("menu-edit-employee-contact")
      ).toHaveTextContent("<EMAIL>");
    });

    test("should return employee name and workTimeId when email is not available", () => {
      const mockProfile = createMockProfile({
        firstName: "Jane",
        secondName: "Marie",
        lastName: "Smith",
        email: "",
        workTimeId: "worktime-456",
      });

      setUseMenu({ incomingProfile: mockProfile });

      renderMenu();

      expect(screen.getByTestId("menu-edit-employee-name")).toHaveTextContent(
        "Jane Marie Smith"
      );
    });

    test("should handle missing secondName gracefully", () => {
      const mockProfile = createMockProfile({
        firstName: "Alice",
        secondName: "",
        lastName: "Brown",
        email: "<EMAIL>",
      });

      setUseMenu({ incomingProfile: mockProfile });

      renderMenu();

      expect(screen.getByTestId("menu-edit-employee-name")).toHaveTextContent(
        "Alice Brown"
      );
      expect(
        screen.getByTestId("menu-edit-employee-contact")
      ).toHaveTextContent("<EMAIL>");
    });

    test("should fallback to profile email when employee email is not available", () => {
      const mockProfile = createMockProfile({
        firstName: "Bob",
        secondName: "",
        lastName: "Wilson",
        email: "",
      });

      // Mock profile with email at the profile level
      const profileWithEmail = {
        ...mockProfile,
        email: "<EMAIL>",
      };

      setUseMenu({ incomingProfile: profileWithEmail });

      renderMenu();

      expect(screen.getByTestId("menu-edit-employee-name")).toHaveTextContent(
        "Bob Wilson"
      );
      expect(
        screen.getByTestId("menu-edit-employee-contact")
      ).toHaveTextContent("<EMAIL>");
    });

    test("should fallback to employee code when neither email nor workTimeId is available", () => {
      const mockProfile = createMockProfile({
        firstName: "Charlie",
        secondName: "",
        lastName: "Davis",
        email: "",
        workTimeId: "",
      });

      const profileWithCode = {
        ...mockProfile,
        code: "EMP789",
      };

      setUseMenu({ incomingProfile: profileWithCode });

      renderMenu();

      expect(screen.getByTestId("menu-edit-employee-name")).toHaveTextContent(
        "Charlie Davis"
      );
      expect(
        screen.getByTestId("menu-edit-employee-contact")
      ).toHaveTextContent("EMP789");
    });

    test("should return null and show fallback text when no employee data is available", () => {
      setUseMenu(null);

      renderMenu();

      expect(
        screen.getByTestId("translator-Edit employee")
      ).toBeInTheDocument();
      expect(
        screen.queryByTestId("menu-edit-employee-name")
      ).not.toBeInTheDocument();
      expect(
        screen.queryByTestId("menu-edit-employee-contact")
      ).not.toBeInTheDocument();
    });

    test("should return null and show fallback text when viewData is undefined", () => {
      setUseMenu(undefined);

      renderMenu();

      expect(
        screen.getByTestId("translator-Edit employee")
      ).toBeInTheDocument();
      expect(
        screen.queryByTestId("menu-edit-employee-name")
      ).not.toBeInTheDocument();
      expect(
        screen.queryByTestId("menu-edit-employee-contact")
      ).not.toBeInTheDocument();
    });
  });

  describe("edit-employee view", () => {
    test("should display employee header for edit-employee view", () => {
      const mockProfile = createMockProfile({
        firstName: "David",
        secondName: "Lee",
        lastName: "Garcia",
        email: "<EMAIL>",
      });

      setUseMenu({ incomingProfile: mockProfile }, "edit-employee");

      renderMenu();

      expect(screen.getByTestId("menu-edit-employee-name")).toHaveTextContent(
        "David Lee Garcia"
      );
      expect(
        screen.getByTestId("menu-edit-employee-contact")
      ).toHaveTextContent("<EMAIL>");
    });

    test("should render EditEmployeeDataSideMenu for edit-employee view", () => {
      const mockProfile = createMockProfile();
      setUseMenu({ incomingProfile: mockProfile }, "edit-employee");

      renderMenu();

      expect(screen.getByTestId("edit-employee-side-menu")).toBeInTheDocument();
    });
  });

  describe("edit-employee-addresses view", () => {
    test("should display employee header for edit-employee-addresses view", () => {
      const mockProfile = createMockProfile({
        firstName: "Emma",
        secondName: "Rose",
        lastName: "Taylor",
        email: "<EMAIL>",
      });

      setUseMenu({ incomingProfile: mockProfile }, "edit-employee-addresses");

      renderMenu();

      expect(screen.getByTestId("menu-edit-employee-name")).toHaveTextContent(
        "Emma Rose Taylor"
      );
      expect(
        screen.getByTestId("menu-edit-employee-contact")
      ).toHaveTextContent("<EMAIL>");
    });

    test("should render EditEmployeeAddressesSideMenu for edit-employee-addresses view", () => {
      const mockProfile = createMockProfile();
      setUseMenu({ incomingProfile: mockProfile }, "edit-employee-addresses");

      renderMenu();

      expect(
        screen.getByTestId("edit-employee-addresses-side-menu")
      ).toBeInTheDocument();
    });

    test("should show fallback text when no employee data is available for addresses view", () => {
      setUseMenu(null, "edit-employee-addresses");

      renderMenu();

      expect(
        screen.getByTestId("translator-Edit employee")
      ).toBeInTheDocument();
      expect(
        screen.queryByTestId("menu-edit-employee-name")
      ).not.toBeInTheDocument();
      expect(
        screen.queryByTestId("menu-edit-employee-contact")
      ).not.toBeInTheDocument();
    });
  });

  describe("other views", () => {
    test("should show user information for employee pages when not in edit mode", () => {
      setUseMenu(null, "profile");

      renderMenu();

      expect(screen.getByTestId("menu-user-name")).toHaveTextContent(
        "John Doe Smith"
      );
      expect(screen.getByTestId("menu-user-email")).toHaveTextContent(
        "<EMAIL>"
      );
    });

    test("should show company name for company pages", () => {
      setUseMenu(null, "profile", { currentPage: "/company-structure" } as any);

      renderMenu();

      expect(screen.getByTestId("menu-company-name")).toHaveTextContent(
        "Test Company"
      );
    });
  });

  describe("edge cases", () => {
    test("should handle empty employee name gracefully", () => {
      const mockProfile = createMockProfile({
        firstName: "",
        secondName: "",
        lastName: "",
        email: "<EMAIL>",
      });

      setUseMenu({ incomingProfile: mockProfile });

      renderMenu();

      expect(screen.getByTestId("menu-edit-employee-name")).toHaveTextContent(
        ""
      );
      expect(
        screen.getByTestId("menu-edit-employee-contact")
      ).toHaveTextContent("<EMAIL>");
    });

    test("should handle null employee object", () => {
      const mockProfile = {
        iban: "BG123456789",
        code: "EMP001",
        employee: null as any,
        addresses: [],
        payrollPersonalData: {} as any,
        employeePropertyEdits: [],
      };

      setUseMenu({ incomingProfile: mockProfile });

      renderMenu();

      expect(
        screen.getByTestId("translator-Edit employee")
      ).toBeInTheDocument();
      expect(
        screen.queryByTestId("menu-edit-employee-name")
      ).not.toBeInTheDocument();
      expect(
        screen.queryByTestId("menu-edit-employee-contact")
      ).not.toBeInTheDocument();
    });

    test("should handle undefined employee object", () => {
      const mockProfile = {
        iban: "BG123456789",
        code: "EMP001",
        employee: undefined as any,
        addresses: [],
        payrollPersonalData: {} as any,
        employeePropertyEdits: [],
      };

      setUseMenu({ incomingProfile: mockProfile });

      renderMenu();

      expect(
        screen.getByTestId("translator-Edit employee")
      ).toBeInTheDocument();
      expect(
        screen.queryByTestId("menu-edit-employee-name")
      ).not.toBeInTheDocument();
      expect(
        screen.queryByTestId("menu-edit-employee-contact")
      ).not.toBeInTheDocument();
    });
  });
});
