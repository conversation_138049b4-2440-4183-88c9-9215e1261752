﻿using System;
using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

#pragma warning disable CA1814 // Prefer jagged arrays over multidimensional

namespace WorkTimeApi.Database.Migrations
{
    /// <inheritdoc />
    public partial class CreatorRoleAdded : Migration
    {
        /// <inheritdoc />
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DeleteData(
                table: "PermissionRoles",
                keyColumns: new[] { "PermissionId", "RoleId" },
                keyValues: new object[] { new Guid("7950869f-7210-a6ad-0249-d656a910c002"), new Guid("14eaaca4-a5e8-47dd-8942-46ebfbd3e3ae") });

            migrationBuilder.DeleteData(
                table: "PermissionRoles",
                keyColumns: new[] { "PermissionId", "RoleId" },
                keyValues: new object[] { new Guid("7950869f-7210-a6ad-0249-d656a910c002"), new Guid("1e85b254-6fc7-4a01-9ad0-e8d9bdb39def") });

            migrationBuilder.DeleteData(
                table: "PermissionRoles",
                keyColumns: new[] { "PermissionId", "RoleId" },
                keyValues: new object[] { new Guid("7950869f-7210-a6ad-0249-d656a910c002"), new Guid("5d8c1a1c-**************-ca65cef3a90b") });

            migrationBuilder.DeleteData(
                table: "PermissionRoles",
                keyColumns: new[] { "PermissionId", "RoleId" },
                keyValues: new object[] { new Guid("7950869f-7210-a6ad-0249-d656a910c002"), new Guid("af9e26b5-c21a-4185-8b4e-4d6276b5c6fe") });

            migrationBuilder.DeleteData(
                table: "PermissionRoles",
                keyColumns: new[] { "PermissionId", "RoleId" },
                keyValues: new object[] { new Guid("7950869f-7210-a6ad-0249-d656a910c002"), new Guid("c5835183-271f-400d-a1de-34753816559c") });

            migrationBuilder.DeleteData(
                table: "PermissionRoles",
                keyColumns: new[] { "PermissionId", "RoleId" },
                keyValues: new object[] { new Guid("7950869f-7210-a6ad-0249-d656a910c002"), new Guid("d5757f97-b9cd-491a-8bf1-0d4a443e435a") });

            migrationBuilder.DeleteData(
                table: "PermissionRoles",
                keyColumns: new[] { "PermissionId", "RoleId" },
                keyValues: new object[] { new Guid("7950869f-7210-a6ad-0249-d656a910c002"), new Guid("dc221982-8016-4215-81af-58c09783d1f8") });

            migrationBuilder.DeleteData(
                table: "PermissionRoles",
                keyColumns: new[] { "PermissionId", "RoleId" },
                keyValues: new object[] { new Guid("7950869f-7210-a6ad-0249-d656a910c002"), new Guid("e4a7048a-8045-45ed-bee2-cee5ac65c574") });

            migrationBuilder.DeleteData(
                table: "PermissionRoles",
                keyColumns: new[] { "PermissionId", "RoleId" },
                keyValues: new object[] { new Guid("7950869f-7210-a6ad-0249-d656a910c002"), new Guid("ea42d985-92a3-4378-b895-87a9f4550e13") });

            migrationBuilder.InsertData(
                table: "Permissions",
                columns: new[] { "Id", "Name" },
                values: new object[] { new Guid("af4832a8-ed7b-fa40-8fb7-b05d20f2bded"), "Companies.Leave" });

            migrationBuilder.InsertData(
                table: "Roles",
                columns: new[] { "Id", "CompanyId", "Name", "Type" },
                values: new object[] { new Guid("1ef8d46a-54eb-4414-aefa-a0cda0fd8406"), new Guid("b3007f68-d72e-48ab-a0b5-deee0ead94b1"), "grCreator", 4 });

            migrationBuilder.InsertData(
                table: "PermissionRoles",
                columns: new[] { "PermissionId", "RoleId" },
                values: new object[,]
                {
                    { new Guid("1340cdab-a99c-ab23-0fb8-7bd4edcaeaf0"), new Guid("1ef8d46a-54eb-4414-aefa-a0cda0fd8406") },
                    { new Guid("269baed5-dd92-36bc-7fa8-f9c547fcec4d"), new Guid("1ef8d46a-54eb-4414-aefa-a0cda0fd8406") },
                    { new Guid("3b801a4a-f37c-8efb-49cf-faa0fbeb9ea9"), new Guid("1ef8d46a-54eb-4414-aefa-a0cda0fd8406") },
                    { new Guid("5053acf7-2718-0f02-592a-1b32addf5972"), new Guid("1ef8d46a-54eb-4414-aefa-a0cda0fd8406") },
                    { new Guid("528aa1f9-7f45-80f9-cfa6-81cf384bbc16"), new Guid("1ef8d46a-54eb-4414-aefa-a0cda0fd8406") },
                    { new Guid("621e7ab3-d58f-9d92-2b28-fbc5ebcdf6a2"), new Guid("1ef8d46a-54eb-4414-aefa-a0cda0fd8406") },
                    { new Guid("7950869f-7210-a6ad-0249-d656a910c002"), new Guid("1ef8d46a-54eb-4414-aefa-a0cda0fd8406") },
                    { new Guid("79a3e9d2-999c-a677-c304-1054d8d29dc0"), new Guid("1ef8d46a-54eb-4414-aefa-a0cda0fd8406") },
                    { new Guid("7dd10ffd-497d-c5ee-a3e4-42ca56a5480c"), new Guid("1ef8d46a-54eb-4414-aefa-a0cda0fd8406") },
                    { new Guid("8571a7bc-4a39-7b25-98a8-55ca4dcf3eed"), new Guid("1ef8d46a-54eb-4414-aefa-a0cda0fd8406") },
                    { new Guid("887cacb5-6597-6814-3ca0-f07d3f88b8e9"), new Guid("1ef8d46a-54eb-4414-aefa-a0cda0fd8406") },
                    { new Guid("8a5e2a7d-6d34-bb72-5fc8-b8aeac9f024b"), new Guid("1ef8d46a-54eb-4414-aefa-a0cda0fd8406") },
                    { new Guid("8aefd196-c542-90b2-5eb5-a4bad7ee97af"), new Guid("1ef8d46a-54eb-4414-aefa-a0cda0fd8406") },
                    { new Guid("94e36e68-877d-865b-cdea-891e65f876de"), new Guid("1ef8d46a-54eb-4414-aefa-a0cda0fd8406") },
                    { new Guid("ae53941f-39c8-520f-687b-d8fe4ef87542"), new Guid("1ef8d46a-54eb-4414-aefa-a0cda0fd8406") },
                    { new Guid("b1815361-f2d2-d704-4129-f7ad010513ea"), new Guid("1ef8d46a-54eb-4414-aefa-a0cda0fd8406") },
                    { new Guid("b207f4fd-afc9-ca30-3900-01f04dd17662"), new Guid("1ef8d46a-54eb-4414-aefa-a0cda0fd8406") },
                    { new Guid("db287155-b609-d03b-7893-9b83dc5ec562"), new Guid("1ef8d46a-54eb-4414-aefa-a0cda0fd8406") },
                    { new Guid("e7297783-ee91-baef-4ec1-9e0051b0c524"), new Guid("1ef8d46a-54eb-4414-aefa-a0cda0fd8406") },
                    { new Guid("fa6fdc50-e8b4-0314-84cd-6e6cdcd2700e"), new Guid("1ef8d46a-54eb-4414-aefa-a0cda0fd8406") },
                    { new Guid("af4832a8-ed7b-fa40-8fb7-b05d20f2bded"), new Guid("c5835183-271f-400d-a1de-34753816559c") }
                });
        }

        /// <inheritdoc />
        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DeleteData(
                table: "PermissionRoles",
                keyColumns: new[] { "PermissionId", "RoleId" },
                keyValues: new object[] { new Guid("1340cdab-a99c-ab23-0fb8-7bd4edcaeaf0"), new Guid("1ef8d46a-54eb-4414-aefa-a0cda0fd8406") });

            migrationBuilder.DeleteData(
                table: "PermissionRoles",
                keyColumns: new[] { "PermissionId", "RoleId" },
                keyValues: new object[] { new Guid("269baed5-dd92-36bc-7fa8-f9c547fcec4d"), new Guid("1ef8d46a-54eb-4414-aefa-a0cda0fd8406") });

            migrationBuilder.DeleteData(
                table: "PermissionRoles",
                keyColumns: new[] { "PermissionId", "RoleId" },
                keyValues: new object[] { new Guid("3b801a4a-f37c-8efb-49cf-faa0fbeb9ea9"), new Guid("1ef8d46a-54eb-4414-aefa-a0cda0fd8406") });

            migrationBuilder.DeleteData(
                table: "PermissionRoles",
                keyColumns: new[] { "PermissionId", "RoleId" },
                keyValues: new object[] { new Guid("5053acf7-2718-0f02-592a-1b32addf5972"), new Guid("1ef8d46a-54eb-4414-aefa-a0cda0fd8406") });

            migrationBuilder.DeleteData(
                table: "PermissionRoles",
                keyColumns: new[] { "PermissionId", "RoleId" },
                keyValues: new object[] { new Guid("528aa1f9-7f45-80f9-cfa6-81cf384bbc16"), new Guid("1ef8d46a-54eb-4414-aefa-a0cda0fd8406") });

            migrationBuilder.DeleteData(
                table: "PermissionRoles",
                keyColumns: new[] { "PermissionId", "RoleId" },
                keyValues: new object[] { new Guid("621e7ab3-d58f-9d92-2b28-fbc5ebcdf6a2"), new Guid("1ef8d46a-54eb-4414-aefa-a0cda0fd8406") });

            migrationBuilder.DeleteData(
                table: "PermissionRoles",
                keyColumns: new[] { "PermissionId", "RoleId" },
                keyValues: new object[] { new Guid("7950869f-7210-a6ad-0249-d656a910c002"), new Guid("1ef8d46a-54eb-4414-aefa-a0cda0fd8406") });

            migrationBuilder.DeleteData(
                table: "PermissionRoles",
                keyColumns: new[] { "PermissionId", "RoleId" },
                keyValues: new object[] { new Guid("79a3e9d2-999c-a677-c304-1054d8d29dc0"), new Guid("1ef8d46a-54eb-4414-aefa-a0cda0fd8406") });

            migrationBuilder.DeleteData(
                table: "PermissionRoles",
                keyColumns: new[] { "PermissionId", "RoleId" },
                keyValues: new object[] { new Guid("7dd10ffd-497d-c5ee-a3e4-42ca56a5480c"), new Guid("1ef8d46a-54eb-4414-aefa-a0cda0fd8406") });

            migrationBuilder.DeleteData(
                table: "PermissionRoles",
                keyColumns: new[] { "PermissionId", "RoleId" },
                keyValues: new object[] { new Guid("8571a7bc-4a39-7b25-98a8-55ca4dcf3eed"), new Guid("1ef8d46a-54eb-4414-aefa-a0cda0fd8406") });

            migrationBuilder.DeleteData(
                table: "PermissionRoles",
                keyColumns: new[] { "PermissionId", "RoleId" },
                keyValues: new object[] { new Guid("887cacb5-6597-6814-3ca0-f07d3f88b8e9"), new Guid("1ef8d46a-54eb-4414-aefa-a0cda0fd8406") });

            migrationBuilder.DeleteData(
                table: "PermissionRoles",
                keyColumns: new[] { "PermissionId", "RoleId" },
                keyValues: new object[] { new Guid("8a5e2a7d-6d34-bb72-5fc8-b8aeac9f024b"), new Guid("1ef8d46a-54eb-4414-aefa-a0cda0fd8406") });

            migrationBuilder.DeleteData(
                table: "PermissionRoles",
                keyColumns: new[] { "PermissionId", "RoleId" },
                keyValues: new object[] { new Guid("8aefd196-c542-90b2-5eb5-a4bad7ee97af"), new Guid("1ef8d46a-54eb-4414-aefa-a0cda0fd8406") });

            migrationBuilder.DeleteData(
                table: "PermissionRoles",
                keyColumns: new[] { "PermissionId", "RoleId" },
                keyValues: new object[] { new Guid("94e36e68-877d-865b-cdea-891e65f876de"), new Guid("1ef8d46a-54eb-4414-aefa-a0cda0fd8406") });

            migrationBuilder.DeleteData(
                table: "PermissionRoles",
                keyColumns: new[] { "PermissionId", "RoleId" },
                keyValues: new object[] { new Guid("ae53941f-39c8-520f-687b-d8fe4ef87542"), new Guid("1ef8d46a-54eb-4414-aefa-a0cda0fd8406") });

            migrationBuilder.DeleteData(
                table: "PermissionRoles",
                keyColumns: new[] { "PermissionId", "RoleId" },
                keyValues: new object[] { new Guid("b1815361-f2d2-d704-4129-f7ad010513ea"), new Guid("1ef8d46a-54eb-4414-aefa-a0cda0fd8406") });

            migrationBuilder.DeleteData(
                table: "PermissionRoles",
                keyColumns: new[] { "PermissionId", "RoleId" },
                keyValues: new object[] { new Guid("b207f4fd-afc9-ca30-3900-01f04dd17662"), new Guid("1ef8d46a-54eb-4414-aefa-a0cda0fd8406") });

            migrationBuilder.DeleteData(
                table: "PermissionRoles",
                keyColumns: new[] { "PermissionId", "RoleId" },
                keyValues: new object[] { new Guid("db287155-b609-d03b-7893-9b83dc5ec562"), new Guid("1ef8d46a-54eb-4414-aefa-a0cda0fd8406") });

            migrationBuilder.DeleteData(
                table: "PermissionRoles",
                keyColumns: new[] { "PermissionId", "RoleId" },
                keyValues: new object[] { new Guid("e7297783-ee91-baef-4ec1-9e0051b0c524"), new Guid("1ef8d46a-54eb-4414-aefa-a0cda0fd8406") });

            migrationBuilder.DeleteData(
                table: "PermissionRoles",
                keyColumns: new[] { "PermissionId", "RoleId" },
                keyValues: new object[] { new Guid("fa6fdc50-e8b4-0314-84cd-6e6cdcd2700e"), new Guid("1ef8d46a-54eb-4414-aefa-a0cda0fd8406") });

            migrationBuilder.DeleteData(
                table: "PermissionRoles",
                keyColumns: new[] { "PermissionId", "RoleId" },
                keyValues: new object[] { new Guid("af4832a8-ed7b-fa40-8fb7-b05d20f2bded"), new Guid("c5835183-271f-400d-a1de-34753816559c") });

            migrationBuilder.DeleteData(
                table: "Permissions",
                keyColumn: "Id",
                keyValue: new Guid("af4832a8-ed7b-fa40-8fb7-b05d20f2bded"));

            migrationBuilder.DeleteData(
                table: "Roles",
                keyColumn: "Id",
                keyValue: new Guid("1ef8d46a-54eb-4414-aefa-a0cda0fd8406"));

            migrationBuilder.InsertData(
                table: "PermissionRoles",
                columns: new[] { "PermissionId", "RoleId" },
                values: new object[,]
                {
                    { new Guid("7950869f-7210-a6ad-0249-d656a910c002"), new Guid("14eaaca4-a5e8-47dd-8942-46ebfbd3e3ae") },
                    { new Guid("7950869f-7210-a6ad-0249-d656a910c002"), new Guid("1e85b254-6fc7-4a01-9ad0-e8d9bdb39def") },
                    { new Guid("7950869f-7210-a6ad-0249-d656a910c002"), new Guid("5d8c1a1c-**************-ca65cef3a90b") },
                    { new Guid("7950869f-7210-a6ad-0249-d656a910c002"), new Guid("af9e26b5-c21a-4185-8b4e-4d6276b5c6fe") },
                    { new Guid("7950869f-7210-a6ad-0249-d656a910c002"), new Guid("c5835183-271f-400d-a1de-34753816559c") },
                    { new Guid("7950869f-7210-a6ad-0249-d656a910c002"), new Guid("d5757f97-b9cd-491a-8bf1-0d4a443e435a") },
                    { new Guid("7950869f-7210-a6ad-0249-d656a910c002"), new Guid("dc221982-8016-4215-81af-58c09783d1f8") },
                    { new Guid("7950869f-7210-a6ad-0249-d656a910c002"), new Guid("e4a7048a-8045-45ed-bee2-cee5ac65c574") },
                    { new Guid("7950869f-7210-a6ad-0249-d656a910c002"), new Guid("ea42d985-92a3-4378-b895-87a9f4550e13") }
                });
        }
    }
}
