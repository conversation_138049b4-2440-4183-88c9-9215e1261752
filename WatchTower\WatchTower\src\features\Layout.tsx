import styled from "styled-components";
import Translator from "../services/language/Translator";
import UserNavMenu from "./authentication/UserNavMenu";
import LanguageSelector from "./LanguageSelector";
import { Link } from "react-router-dom";

const NavContainer = styled.div`
  display: flex;
  flex: 0 1 auto;
  align-items: center;
  justify-content: space-between;
  width: 100%;
  padding: 0.7rem 0;
  background-color: var(--app-header-color);
`;

const NavigationLink = styled(Link)`
  text-decoration: none;
  margin-left: 1.5rem;
`;

const AppLabel = styled.label`
  letter-spacing: var(--unnamed-character-spacing-0);
  color: var(--app-label-color);
  font-size: 2rem;
  font-weight: Montserrat;
  letter-spacing: 0;

  &:hover {
    cursor: pointer;
  }
`;

const Layout = () => {
  return (
    <NavContainer>
      <NavigationLink to="/">
        <AppLabel>
          <Translator getString="WatchTower" />
        </AppLabel>
      </NavigationLink>
      <NavigationLink to="/watch/health-check/create">
        <Translator getString="Create Watcher" />
      </NavigationLink>
      <UserNavMenu />
      <LanguageSelector />
    </NavContainer>
  );
};

export default Layout;
