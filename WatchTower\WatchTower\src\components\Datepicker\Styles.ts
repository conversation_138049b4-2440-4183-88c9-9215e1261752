import styled from "styled-components";
import calendarImage from "../../assets/images/logos/calendar.png";

export const ContainerNavBar = styled.div`
  width: 16.2em;
  display: flex;
  justify-content: space-between;
  align-items: center;
  border-bottom: 0.2em solid var(--datepicker-nav-bottom-color);
  background-color: var(--datepicker-nav-backround-color);
  border-radius: 2em;
  padding: 0.1em;
`;

export const ContainerNavButtons = styled.div`
  height: 1em;
  padding: 0.2em;
`;

export const Button = styled.button`
  border-color: var(--datepicker-view-buttons-font-color);
  position: relative;
  height: 3.15em;
  width: 4em;
  border-bottom-right-radius: 1.9em;
  border-top-right-radius: 1.9em;
  border: 0.1em solid var(--textbox-border-color);

  outline: none;

  background-image: url(calendarImage);
  background-size: 75% 75%;
  background-repeat: no-repeat;
  background-position: center;
`;

export const MainContainer = styled.div`
  display: flex;
  justify-content: center;
  align-items: center;
  padding: 0.1em;
  gap: 0.1em;
  cursor: pointer;
  position: relative;
  max-width: 15em;
  max-height: 4em;
  margin: 0.1em auto;
  z-index: 2;
  cursor: pointer;
  user-select: none;
`;

export const ContainerDropDown = styled.div<{ active: boolean }>`
  display: none;
  position: absolute;
  top: 100%;
  left: 0;
  right: 0;
  ${({ active }) =>
    active &&
    `
    display: block;
    width: 15em;
    max-height: 12em;
  `}
`;
