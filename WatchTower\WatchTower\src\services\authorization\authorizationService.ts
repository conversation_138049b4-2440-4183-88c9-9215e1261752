import { PermissionsMap } from "../../features/authorization/PermissionsContext";
import { Company } from "../../features/companies/CompanyContext";

const getUserPermissions = (
  permissions: PermissionsMap | undefined,
  company: Company
): string[] => {
  return permissions
    ? permissions[company.userRegistrationCompanyId ?? -1] ?? []
    : [];
};

export const isUserPermitted = (
  requiredPermissions: string[],
  permissions: PermissionsMap | undefined,
  company: Company
): boolean => {
  const companyPermissions = getUserPermissions(permissions, company);
  return requiredPermissions.some((permission) =>
    companyPermissions.includes(permission)
  );
};
