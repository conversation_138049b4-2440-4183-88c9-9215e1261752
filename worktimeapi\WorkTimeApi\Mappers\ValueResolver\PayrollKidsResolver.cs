﻿using AutoMapper;
using WorkTimeApi.Common.DTOs.Payrolls;
using WorkTimeApi.Database.Models.TRZ;

namespace WorkTimeApi.Mappers.ValueResolver
{
    public class PayrollKidsResolver : IValueResolver<PayrollDTO, TRZPayroll, ICollection<TRZPayrollKid>>
    {
        public ICollection<TRZPayrollKid> Resolve(PayrollDTO src, TRZPayroll dest, ICollection<TRZPayrollKid> destMember, ResolutionContext context)
        {
            var kids = new List<TRZPayrollKid>();

            if (src.Kids != null)
            {
                kids.AddRange(src.Kids
                    .Where(k => k is not null && k.Id != Guid.Empty)
                    .Select(k => new TRZPayrollKid { Payroll = dest, KIDId = k.Id }));
            }

            return kids;
        }       
    }
}