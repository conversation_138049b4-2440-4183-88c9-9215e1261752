using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Configuration;
using Moq;
using WorkTimeApi.Database;
using WorkTimeApi.Database.Models;
using WorkTimeApi.Database.Repositories.Addresses;

namespace WorkTimeApi.Tests.Repositories.Addresses
{
    public class AddressesRepositoryTests
    {
        [Fact]
        public async Task DeleteEmployeeAddressAsync_RemovesLinkAndAddress_ReturnsTrue()
        {
            // Arrange
            var options = new DbContextOptionsBuilder<WorkTimeApiDbContext>()
                .UseInMemoryDatabase(databaseName: Guid.NewGuid().ToString())
                .Options;

            var testConfig = new ConfigurationBuilder()
                .AddInMemoryCollection(new Dictionary<string, string?>
                {
                    ["WorkTimeDefaultCompanyId"] = Guid.NewGuid().ToString()
                })
                .Build();

            await using var ctx = new WorkTimeApiDbContext(options, testConfig);

            var employeeId = Guid.NewGuid();
            var addressId = Guid.NewGuid();

            ctx.Employees.Add(new Employee { Id = employeeId });
            var address = new Address { Id = addressId };
            ctx.Addresses.Add(address);
            ctx.EmployeeAddress.Add(new EmployeeAddress { EmployeeId = employeeId, AddressId = addressId, Address = address });
            await ctx.SaveChangesAsync();

            var factoryMock = new Mock<IDbContextFactory<WorkTimeApiDbContext>>();
            factoryMock.Setup(f => f.CreateDbContextAsync(It.IsAny<CancellationToken>()))
                .ReturnsAsync(() => new WorkTimeApiDbContext(options, testConfig));
            var repo = new AddressesRepository(factoryMock.Object);

            // Act
            var result = await repo.DeleteEmployeeAddressAsync(employeeId, addressId);

            // Assert
            Assert.True(result);
            await using var verifyCtx = await factoryMock.Object.CreateDbContextAsync();
            Assert.Null(await verifyCtx.EmployeeAddress.FirstOrDefaultAsync(ea => ea.EmployeeId == employeeId && ea.AddressId == addressId));
            Assert.Null(await verifyCtx.Addresses.FirstOrDefaultAsync(a => a.Id == addressId));
        }
    }
}
