﻿using Gateway.Common.Results;
using Gateway.Connections.Interfaces;
using MediatR;

namespace WorkTimeApi.Common.Requests.Employees
{
    public class LoadEmployeePayrollHandler : IRequestHandler<LoadEmployeePayrollRequest, IResult>
    {
        private readonly IWorkTimeApiConnection _workTimeApiConnection;

        public LoadEmployeePayrollHandler(IWorkTimeApiConnection workTimeApiConnection)
        {
            _workTimeApiConnection = workTimeApiConnection;
        }

        public async Task<IResult> Handle(LoadEmployeePayrollRequest request, CancellationToken cancellationToken)
        {
            var response = await _workTimeApiConnection.LoadEmployeePayrollListAsync(request);

            return new HttpResponseMessageResult(response);
        }
    }
}
