﻿using Gateway.Connections.Interfaces;
using SSO.Common.Requests;
using System.Net.Http.Headers;

namespace Gateway.Connections
{
    public class SSOConnection : ISSOConnection
    {
        private readonly HttpClient _httpClient;

        public SSOConnection(IConfiguration configuration, HttpClient httpClient)
        {
            _httpClient = httpClient;
            _httpClient.BaseAddress = new Uri(configuration["SSOBaseUrl"]!);
        }

        public Task<HttpResponseMessage> AuthenticateAsync(string authorizationHeader, string refreshTokenHeader)
        {
            _httpClient.DefaultRequestHeaders.Add("Authorization", authorizationHeader);
            _httpClient.DefaultRequestHeaders.Add("refresh-token", refreshTokenHeader);

            return _httpClient.GetAsync("sso/auth");
        }

        public Task<HttpResponseMessage> LoginAsync(string authorizationHeader)
        {
            _httpClient.DefaultRequestHeaders.Add("Authorization", authorizationHeader);

            return _httpClient.PostAsync("sso/login", null);
        }

        public Task<HttpResponseMessage> HeaderLoginAsync(string authorizationHeader)
        {
            _httpClient.DefaultRequestHeaders.Add("Authorization", authorizationHeader);

            return _httpClient.GetAsync("sso/try-login");
        }

        public Task<HttpResponseMessage> LogoutAsync(string authorizationHeader, string refreshTokenHeader)
        {
            _httpClient.DefaultRequestHeaders.Add("Authorization", authorizationHeader);
            _httpClient.DefaultRequestHeaders.Add("refresh-token", refreshTokenHeader);

            return _httpClient.PostAsJsonAsync("sso/logout", new LogoutRequest());
        }

        public Task<HttpResponseMessage> GoogleLoginAsync(string googleToken)
        {
            _httpClient.DefaultRequestHeaders.Add("Authorization", $"Bearer {googleToken}");

            return _httpClient.GetAsync("sso/try-google-login");
        }

        public Task<HttpResponseMessage> FacebookLoginAsync(string facebookToken)
        {
            _httpClient.DefaultRequestHeaders.Add("Authorization", facebookToken);

            return _httpClient.GetAsync("sso/try-facebook-login");
        }

        public Task<HttpResponseMessage> MicrosoftLoginAsync(string microsoftToken)
        {
            _httpClient.DefaultRequestHeaders.Authorization = new AuthenticationHeaderValue("Bearer", microsoftToken);

            return _httpClient.GetAsync("sso/try-microsoft-login");
        }

        public Task<HttpResponseMessage> ResetPasswordAsync(ResetPasswordRequest resetPasswordRequest)
        {
            return _httpClient.PostAsJsonAsync("sso/reset-password", resetPasswordRequest);
        }

        public Task<HttpResponseMessage> ChangeForgottenPasswordAsync(ChangeForgottenPasswordRequest changeForgottenPasswordRequest)
        {
            return _httpClient.PostAsJsonAsync("sso/change-forgotten-password", changeForgottenPasswordRequest);
        }

        public Task<HttpResponseMessage> GetUserPermissionsByUserRegistrationsCompanyIdAsync(int companyId, string token)
        {
            return _httpClient.GetAsync($"sso/companies/{companyId}/permissions/{token}");
        }

        public Task<HttpResponseMessage> ValidateRefreshToken(ValidateRefreshTokenRequest validateRefreshTokenRequest)
        {
            return _httpClient.PostAsJsonAsync("sso/validate-refresh-token", validateRefreshTokenRequest);
        }

        public Task<HttpResponseMessage> ValidateRecaptchaToken(RecaptchaValidationRequest recaptchaValidationRequest)
        {
            return _httpClient.GetAsync($"sso/validate-recaptcha-token?token={recaptchaValidationRequest.Token}");
        }
    }
}
