﻿using Gateway.Connections.Interfaces;
using MediatR;
using SSO.Common.Requests;

namespace Gateway.Handlers.SSO
{
    public class ResetPasswordHandler(ISSOConnection ssoConnection, IUserRegistrationConnection userRegistrationConnection) : IRequestHandler<ResetPasswordRequest, IResult>
    {
        public async Task<IResult> Handle(ResetPasswordRequest request, CancellationToken cancellationToken)
        {
            var isUserExisting = await userRegistrationConnection.GetUserByUserNameAsync(request.ResetPasswordDTO.Email);
            if (isUserExisting.StatusCode == System.Net.HttpStatusCode.NotFound)
                return Results.NotFound();

            var response = await ssoConnection.ResetPasswordAsync(request);
            return response is not null && response.IsSuccessStatusCode ? Results.Ok() : Results.BadRequest();
        }
    }
}
