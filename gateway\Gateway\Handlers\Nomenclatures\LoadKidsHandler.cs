﻿using Gateway.Connections.Interfaces;
using MediatR;
using WorkTimeApi.Common.Requests.Nomenclatures;

namespace Gateway.Handlers.Nomenclatures
{
    public class LoadKidsHandler : IRequestHandler<GetKidsRequest, IResult>
    {
        private readonly IWorkTimeApiConnection _workTimeApiConnection;

        public LoadKidsHandler(IWorkTimeApiConnection workTimeApiConnection)
        {
            _workTimeApiConnection = workTimeApiConnection;
        }

        public async Task<IResult> Handle(GetKidsRequest request, CancellationToken cancellationToken)
        {
            var response = await _workTimeApiConnection.GetKidsAsync();

            if (!response.IsSuccessStatusCode)
                return Results.StatusCode((int)response.StatusCode);

            return Results.Text(await response.Content.ReadAsStringAsync(cancellationToken));
        }
    }
}
