﻿using Gateway.Connections.Interfaces;
using WorkTimeApi.Common.Requests.StructureLevels;
using MediatR;

namespace Gateway.Handlers.StructureLevels
{
	public class AddStructureLevelHandler : IRequestHandler<AddStructureLevelRequest, IResult>
	{
		private readonly IWorkTimeApiConnection _workTimeApiConnection;

		public AddStructureLevelHandler(IWorkTimeApiConnection workTimeApiConnection)
		{
			_workTimeApiConnection = workTimeApiConnection;
		}

		public async Task<IResult> Handle(AddStructureLevelRequest request, CancellationToken cancellationToken)
		{
			var response = await _workTimeApiConnection.AddStructureLevelAsync(request);

			if (!response.IsSuccessStatusCode)
				return Results.StatusCode((int)response.StatusCode);

			return Results.Text(await response.Content.ReadAsStringAsync(cancellationToken));
		}
	}
}
