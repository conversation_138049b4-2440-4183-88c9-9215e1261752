import { NavLink, useLocation } from "react-router-dom";
import styled from "styled-components";

import Label from "./Inputs/Label";

const StyledLink = styled(NavLink)<{
  currentpath: string;
  to: string;
}>`
  display: flex;
  justify-content: left;
  align-items: center;
  text-decoration: none;
  font-family: Segoe UI;
  font-weight: Regular;
  width: 10rem;
  font-size: 1.1rem;
  padding: 0.5rem;
  background-color: var(--link-background-color);
  color: var(--link-color);
  border-radius: 3.75rem;
  margin: 0.3rem;
  transition: background-color 0.3s ease;

  &:hover {
    background-color: var(--link-background-color-hover);
    color: var(--link-color-hover);
  }
  ${({ currentpath, to }) =>
    currentpath === to &&
    `
      background-color: var(--link-background-color-active);
      color: var(--link-color-active);`}
`;

const Image = styled.img`
  width: 1.5rem;
  height: 1.5rem;
  cursor: pointer;
  margin-left: 0.7rem;
  margin-right: 0.3rem;
`;

const LinkLabel = styled(Label)`
  cursor: pointer;
`;

interface Props {
  to: string;
  imageSrc: string;
  label: string;
  currentpath: string;
}
const NavLinkButton = ({ to, imageSrc, label }: Props) => {
  const location = useLocation();
  return (
    <StyledLink to={to} currentpath={location.pathname}>
      <Image src={imageSrc} />
      <LinkLabel>{label}</LinkLabel>
    </StyledLink>
  );
};

export default NavLinkButton;
