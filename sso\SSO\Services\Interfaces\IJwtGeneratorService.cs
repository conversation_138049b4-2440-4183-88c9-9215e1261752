﻿using SSO.Common.DTOs;
using System.Security.Claims;

namespace SSO.Services.Interfaces
{
    public interface IJwtGeneratorService
    {
        Task<string> GenerateAccessTokenAsync(UserDTO userDTO);

        Task<string> GenerateAccessTokenAsync(IEnumerable<Claim> claims);

        Task<string> GenerateRefreshTokenAsync(UserDTO userDTO);

        Task<string> GenerateRefreshTokenAsync(IEnumerable<Claim> claims);
    }
}
