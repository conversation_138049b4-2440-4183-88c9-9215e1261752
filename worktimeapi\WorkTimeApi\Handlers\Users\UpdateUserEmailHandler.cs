using MediatR;
using WorkTimeApi.Common.Requests.Users;
using WorkTimeApi.Services.Interfaces.Users;

namespace WorkTimeApi.Handlers.Users
{
    public class UpdateUserEmailHandler(IUserService userService) : IRequestHandler<UpdateUserEmailRequest, IResult>
    {
        public async Task<IResult> Handle(UpdateUserEmailRequest request, CancellationToken cancellationToken)
        {
            return await userService.UpdateUserEmailAsync(request.UserDTO);
        }
    }
}
