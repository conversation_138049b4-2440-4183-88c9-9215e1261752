﻿using MediatR;
using System.Diagnostics.Metrics;
using WatchTower.Common.Requests;
using WatchTower.Services.Interfaces;

namespace WatchTower.Api.Handlers.Watch
{
    public class GetAllWatchHealthCheckHandler : IRequestHandler<GetAllWatchHealthCheckRequest, IResult>
    {
        private readonly IWatchHealthCheckService _watchHealthCheckService;
        private readonly IMeterFactory _meterFactory;

        public GetAllWatchHealthCheckHandler(IWatchHealthCheckService watchHealthCheckService,
            IMeterFactory meterFactory)
        {
            _watchHealthCheckService = watchHealthCheckService;
            _meterFactory = meterFactory;
        }

        public async Task<IResult> Handle(GetAllWatchHealthCheckRequest request, CancellationToken cancellationToken)
        {
            var meter = _meterFactory.Create("WatchTowerApi");

            var instrument = meter.CreateCounter<int>("health_checks_counter");

            var healthChecks = await _watchHealthCheckService
                .GetAllAsync(cancellationToken);

            instrument.Add(healthChecks.Count);

            return Results.Ok(healthChecks.GroupBy(x => x.Environment)
                .ToDictionary(w => w.Key, w => w.ToList()));
        }
    }
}
