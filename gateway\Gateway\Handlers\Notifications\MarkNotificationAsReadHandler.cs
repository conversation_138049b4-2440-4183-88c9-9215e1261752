﻿using Gateway.Common.Results;
using Gateway.Connections.Interfaces;
using MediatR;
using WorkTimeApi.Common.Requests.Notifications;

namespace Gateway.Handlers.Notifications
{
    public class MarkNotificationAsReadHandler : IRequestHandler<ReadNotificationRequest, IResult>
    {
        private readonly IWorkTimeApiConnection _workTimeApiConnection;

        public MarkNotificationAsReadHandler(IWorkTimeApiConnection workTimeApiConnection)
        {
            _workTimeApiConnection = workTimeApiConnection;
        }

        public async Task<IResult> Handle(ReadNotificationRequest request, CancellationToken cancellationToken)
        {
            var response = await _workTimeApiConnection.ReadNotificationAsync(request);

            return new HttpResponseMessageResult(response);
        }
    }
}
