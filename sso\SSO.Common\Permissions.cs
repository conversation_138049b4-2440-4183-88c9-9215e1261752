﻿using System.Security.Cryptography;
using System.Text;

namespace SSO.Common
{
    public class Permissions
    {
        public static class SupportChatService
        {
            public static string Chats => "SupportChatService.Chats";

            public static string Users => "SupportChatService.Users";
        }

        public static Guid CreateGuidFromName(string name)
            => new(MD5.HashData(Encoding.Default.GetBytes(name)));
    }
}
