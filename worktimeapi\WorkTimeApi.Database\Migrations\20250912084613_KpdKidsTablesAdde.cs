﻿using System;
using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace WorkTimeApi.Database.Migrations
{
    /// <inheritdoc />
    public partial class KpdKidsTablesAdde : Migration
    {
        /// <inheritdoc />
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.AddColumn<string>(
                name: "AdditionalTerms",
                table: "Payrolls",
                type: "nvarchar(max)",
                nullable: true);

            migrationBuilder.AddColumn<string>(
                name: "NoticePeriod",
                table: "Payrolls",
                type: "nvarchar(max)",
                nullable: true);

            migrationBuilder.AddColumn<DateTime>(
                name: "ProbationaryDate",
                table: "Payrolls",
                type: "datetime2",
                nullable: true);

            migrationBuilder.AlterColumn<int>(
                name: "EventType",
                schema: "trz",
                table: "Events",
                type: "int",
                nullable: false,
                defaultValue: 0,
                oldClrType: typeof(int),
                oldType: "int",
                oldNullable: true);

            migrationBuilder.AddColumn<DateTime>(
                name: "AnnexPayrollFromDate",
                schema: "trz",
                table: "AnnexPayrolls",
                type: "datetime2",
                nullable: true);

            migrationBuilder.AddColumn<DateTime>(
                name: "AnnexPayrollToDate",
                schema: "trz",
                table: "AnnexPayrolls",
                type: "datetime2",
                nullable: true);

            migrationBuilder.CreateTable(
                name: "AnnexPayrollKids",
                columns: table => new
                {
                    AnnexPayrollId = table.Column<Guid>(type: "uniqueidentifier", nullable: false),
                    KIDId = table.Column<Guid>(type: "uniqueidentifier", nullable: false)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_AnnexPayrollKids", x => new { x.AnnexPayrollId, x.KIDId });
                    table.ForeignKey(
                        name: "FK_AnnexPayrollKids_AnnexPayrolls_AnnexPayrollId",
                        column: x => x.AnnexPayrollId,
                        principalTable: "AnnexPayrolls",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Cascade);
                    table.ForeignKey(
                        name: "FK_AnnexPayrollKids_Kids_KIDId",
                        column: x => x.KIDId,
                        principalTable: "Kids",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Cascade);
                });

            migrationBuilder.CreateTable(
                name: "AnnexPayrollKids",
                schema: "trz",
                columns: table => new
                {
                    AnnexPayrollId = table.Column<Guid>(type: "uniqueidentifier", nullable: false),
                    KIDId = table.Column<Guid>(type: "uniqueidentifier", nullable: false)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_AnnexPayrollKids", x => new { x.AnnexPayrollId, x.KIDId });
                    table.ForeignKey(
                        name: "FK_AnnexPayrollKids_AnnexPayrolls_AnnexPayrollId",
                        column: x => x.AnnexPayrollId,
                        principalSchema: "trz",
                        principalTable: "AnnexPayrolls",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Cascade);
                    table.ForeignKey(
                        name: "FK_AnnexPayrollKids_Kids_KIDId",
                        column: x => x.KIDId,
                        principalTable: "Kids",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Cascade);
                });

            migrationBuilder.CreateTable(
                name: "AnnexPayrollKpds",
                columns: table => new
                {
                    AnnexPayrollId = table.Column<Guid>(type: "uniqueidentifier", nullable: false),
                    KPDId = table.Column<Guid>(type: "uniqueidentifier", nullable: false)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_AnnexPayrollKpds", x => new { x.AnnexPayrollId, x.KPDId });
                    table.ForeignKey(
                        name: "FK_AnnexPayrollKpds_AnnexPayrolls_AnnexPayrollId",
                        column: x => x.AnnexPayrollId,
                        principalTable: "AnnexPayrolls",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Cascade);
                    table.ForeignKey(
                        name: "FK_AnnexPayrollKpds_NKPDs_KPDId",
                        column: x => x.KPDId,
                        principalTable: "NKPDs",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Cascade);
                });

            migrationBuilder.CreateTable(
                name: "AnnexPayrollKPDs",
                schema: "trz",
                columns: table => new
                {
                    AnnexPayrollId = table.Column<Guid>(type: "uniqueidentifier", nullable: false),
                    KPDId = table.Column<Guid>(type: "uniqueidentifier", nullable: false)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_AnnexPayrollKPDs", x => new { x.AnnexPayrollId, x.KPDId });
                    table.ForeignKey(
                        name: "FK_AnnexPayrollKPDs_AnnexPayrolls_AnnexPayrollId",
                        column: x => x.AnnexPayrollId,
                        principalSchema: "trz",
                        principalTable: "AnnexPayrolls",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Cascade);
                    table.ForeignKey(
                        name: "FK_AnnexPayrollKPDs_NKPDs_KPDId",
                        column: x => x.KPDId,
                        principalTable: "NKPDs",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Cascade);
                });

            migrationBuilder.CreateTable(
                name: "PayrollKids",
                columns: table => new
                {
                    PayrollId = table.Column<Guid>(type: "uniqueidentifier", nullable: false),
                    KIDId = table.Column<Guid>(type: "uniqueidentifier", nullable: false)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_PayrollKids", x => new { x.PayrollId, x.KIDId });
                    table.ForeignKey(
                        name: "FK_PayrollKids_Kids_KIDId",
                        column: x => x.KIDId,
                        principalTable: "Kids",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Cascade);
                    table.ForeignKey(
                        name: "FK_PayrollKids_Payrolls_PayrollId",
                        column: x => x.PayrollId,
                        principalTable: "Payrolls",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Cascade);
                });

            migrationBuilder.CreateTable(
                name: "PayrollKids",
                schema: "trz",
                columns: table => new
                {
                    PayrollId = table.Column<Guid>(type: "uniqueidentifier", nullable: false),
                    KIDId = table.Column<Guid>(type: "uniqueidentifier", nullable: false)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_PayrollKids", x => new { x.PayrollId, x.KIDId });
                    table.ForeignKey(
                        name: "FK_PayrollKids_Kids_KIDId",
                        column: x => x.KIDId,
                        principalTable: "Kids",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Cascade);
                    table.ForeignKey(
                        name: "FK_PayrollKids_Payrolls_PayrollId",
                        column: x => x.PayrollId,
                        principalSchema: "trz",
                        principalTable: "Payrolls",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Cascade);
                });

            migrationBuilder.CreateTable(
                name: "PayrollKpds",
                columns: table => new
                {
                    PayrollId = table.Column<Guid>(type: "uniqueidentifier", nullable: false),
                    KPDId = table.Column<Guid>(type: "uniqueidentifier", nullable: false)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_PayrollKpds", x => new { x.PayrollId, x.KPDId });
                    table.ForeignKey(
                        name: "FK_PayrollKpds_NKPDs_KPDId",
                        column: x => x.KPDId,
                        principalTable: "NKPDs",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Cascade);
                    table.ForeignKey(
                        name: "FK_PayrollKpds_Payrolls_PayrollId",
                        column: x => x.PayrollId,
                        principalTable: "Payrolls",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Cascade);
                });

            migrationBuilder.CreateTable(
                name: "PayrollKPDs",
                schema: "trz",
                columns: table => new
                {
                    PayrollId = table.Column<Guid>(type: "uniqueidentifier", nullable: false),
                    KPDId = table.Column<Guid>(type: "uniqueidentifier", nullable: false)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_PayrollKPDs", x => new { x.PayrollId, x.KPDId });
                    table.ForeignKey(
                        name: "FK_PayrollKPDs_NKPDs_KPDId",
                        column: x => x.KPDId,
                        principalTable: "NKPDs",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Cascade);
                    table.ForeignKey(
                        name: "FK_PayrollKPDs_Payrolls_PayrollId",
                        column: x => x.PayrollId,
                        principalSchema: "trz",
                        principalTable: "Payrolls",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Cascade);
                });

            migrationBuilder.CreateIndex(
                name: "IX_AnnexPayrollKids_KIDId",
                table: "AnnexPayrollKids",
                column: "KIDId");

            migrationBuilder.CreateIndex(
                name: "IX_AnnexPayrollKids_KIDId",
                schema: "trz",
                table: "AnnexPayrollKids",
                column: "KIDId");

            migrationBuilder.CreateIndex(
                name: "IX_AnnexPayrollKpds_KPDId",
                table: "AnnexPayrollKpds",
                column: "KPDId");

            migrationBuilder.CreateIndex(
                name: "IX_AnnexPayrollKPDs_KPDId",
                schema: "trz",
                table: "AnnexPayrollKPDs",
                column: "KPDId");

            migrationBuilder.CreateIndex(
                name: "IX_PayrollKids_KIDId",
                table: "PayrollKids",
                column: "KIDId");

            migrationBuilder.CreateIndex(
                name: "IX_PayrollKids_KIDId",
                schema: "trz",
                table: "PayrollKids",
                column: "KIDId");

            migrationBuilder.CreateIndex(
                name: "IX_PayrollKpds_KPDId",
                table: "PayrollKpds",
                column: "KPDId");

            migrationBuilder.CreateIndex(
                name: "IX_PayrollKPDs_KPDId",
                schema: "trz",
                table: "PayrollKPDs",
                column: "KPDId");
        }

        /// <inheritdoc />
        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropTable(
                name: "AnnexPayrollKids");

            migrationBuilder.DropTable(
                name: "AnnexPayrollKids",
                schema: "trz");

            migrationBuilder.DropTable(
                name: "AnnexPayrollKpds");

            migrationBuilder.DropTable(
                name: "AnnexPayrollKPDs",
                schema: "trz");

            migrationBuilder.DropTable(
                name: "PayrollKids");

            migrationBuilder.DropTable(
                name: "PayrollKids",
                schema: "trz");

            migrationBuilder.DropTable(
                name: "PayrollKpds");

            migrationBuilder.DropTable(
                name: "PayrollKPDs",
                schema: "trz");

            migrationBuilder.DropColumn(
                name: "AdditionalTerms",
                table: "Payrolls");

            migrationBuilder.DropColumn(
                name: "NoticePeriod",
                table: "Payrolls");

            migrationBuilder.DropColumn(
                name: "ProbationaryDate",
                table: "Payrolls");

            migrationBuilder.DropColumn(
                name: "AnnexPayrollFromDate",
                schema: "trz",
                table: "AnnexPayrolls");

            migrationBuilder.DropColumn(
                name: "AnnexPayrollToDate",
                schema: "trz",
                table: "AnnexPayrolls");

            migrationBuilder.AlterColumn<int>(
                name: "EventType",
                schema: "trz",
                table: "Events",
                type: "int",
                nullable: true,
                oldClrType: typeof(int),
                oldType: "int");
        }
    }
}
