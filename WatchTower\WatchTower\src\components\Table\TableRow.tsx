import styled from "styled-components";
import { IEntity } from "../../models/DTOs/IEntity";
import { ColumnDefinitionType } from "./Table";
import Button from "../Inputs/Button";

interface TableRowProps<T extends IEntity, K extends keyof T>
  extends React.DetailedHTMLProps<
    React.HTMLAttributes<HTMLTableRowElement>,
    HTMLTableRowElement
  > {
  data: Array<T>;
  columns: Array<ColumnDefinitionType<T, K>>;
  searchFiled?: string | undefined;
  handleDelete(idToDelete: string): Promise<void>;
}

const TableRowItem = styled.tr`
  &:first-child {
    width: 2.5rem;
  }
  border-bottom: 1px solid var(--table-row-bottom-line-color);
`;

const TableCell = styled.td`
  padding: 0.75rem;
  font-size: 0.875rem;
  color: var(--table-cell-color);
  background-color: var(--table-cell-backgroundColor);
`;

const TableRows = <T extends IEntity, <PERSON> extends keyof T>({
  data,
  columns,
  handleDelete,
}: TableRowProps<T, K>): JSX.Element => {
  return (
    <tbody>
      {data.map((row, index) => (
        <TableRowItem key={`row-${index}`}>
          {columns.map((column, index2) => {
            return (
              <TableCell key={`cell-${index2}`}>
                <>{row[column.key]}</>
              </TableCell>
            );
          })}
          {/* <TableCell>
            <Button label="X" onClick={() => handleDelete(row.id)} />
          </TableCell> */}
        </TableRowItem>
      ))}
    </tbody>
  );
};

export default TableRows;
