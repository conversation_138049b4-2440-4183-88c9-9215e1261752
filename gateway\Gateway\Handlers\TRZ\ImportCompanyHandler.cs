﻿using Gateway.Common.Globals;
using Gateway.Common.Redis.Models;
using Gateway.Common.Redis.Services.Interfaces;
using Gateway.Common.Results;
using Gateway.Connections.Interfaces;
using MediatR;
using Microinvest.TransferFiles.Tools.Models.Companies;
using WorkTimeApi.Common.DTOs.Users;
using WorkTimeApi.Common.Requests.TRZ;

namespace Gateway.Handlers.TRZ
{
    public class ImportCompanyHandler(GlobalUser globalUser,
        IUserRegistrationConnection userRegistrationConnection,
        IWorkTimeApiConnection workTimeApiConnection,
        IRedisService<RedisCompanyDTO> companiesRedisService) : IRequestHandler<ImportCompanyRequest, IResult>
    {
        public async Task<IResult> Handle(ImportCompanyRequest request, CancellationToken cancellationToken)
        {
            var senderaCompany = companiesRedisService.GetByKey($"{globalUser.Email}:{request.Bulstat}");
            if (senderaCompany is null)
                return Results.NotFound();

            request.UserRegistrationsId = senderaCompany.Id;

            var worktimeRequest = new ImportTRZCompanyRequest
            {
                ImportCompanyRequest = request,
                User = new UserDTO
                {
                    Email = globalUser.Email,
                    FirstName = globalUser.FirstName,
                    Id = globalUser.Id,
                    LastName = globalUser.LastName,
                    SecondName = globalUser.SecondName
                }
            };

            var workTimeCompanyResponse = await workTimeApiConnection.ImportCompanyAsync(worktimeRequest);

            if (workTimeCompanyResponse.IsSuccessStatusCode)
            {
                await userRegistrationConnection.EditCompanyAsync(new CompanyDTO
                {
                    Id = request.UserRegistrationsId,
                    Name = request.Name,
                    Bulstat = request.Bulstat,
                    ContactName = request.MOL
                });
            }

            return new HttpResponseMessageResult(workTimeCompanyResponse);
        }
    }
}
