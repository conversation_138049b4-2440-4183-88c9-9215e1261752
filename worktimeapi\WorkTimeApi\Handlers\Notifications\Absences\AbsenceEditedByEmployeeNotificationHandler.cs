﻿using MediatR;
using WorkTime.Notifications.Services.Interfaces;
using WorkTimeApi.Common;
using WorkTimeApi.Common.Enums;
using WorkTimeApi.Common.Notifications.Absences;
using WorkTimeApi.Database.Repositories.Interfaces.Companies;
using WorkTimeApi.Services.Interfaces.Absences;
using WorkTimeApi.Services.Interfaces.Notifications;

namespace WorkTimeApi.Handlers.Notifications.Absences
{
    public class AbsenceEditedByEmployeeNotificationHandler(ISignalRNotificationService signalRNotificationService,
        INotificationsService notificationsService,
        IEmailsNotificationService emailsNotificationService,
        ICompaniesRepository companiesRepository,
        IAbsencesService absencesService,
        IConfiguration configuration) : INotificationHandler<AbsenceEditedByEmployeeNotification>
    {
        public async Task Handle(AbsenceEditedByEmployeeNotification notification, CancellationToken cancellationToken)
        {
            var employee = await absencesService.GetEmployeeDTOByAbsenceIdAsync(notification.Payload.Id, notification.Payload.IsHospital) ?? throw new Exception("Невалидно Employee!");
            var employeesToNotify = await notificationsService.GetEmployeesToNotifyAsync(notification.CompanyId, NotificationsName.Absences.EditedByEmployee.Email);

            var absence = notification.Payload ?? throw new Exception("Няма данни за заявени отсъствия");
            var company = await companiesRepository.GetByIdAsync(notification.CompanyId) ?? throw new Exception("Невалидна компания!");
            var fullName = string.Join(" ", new[] { employee.FirstName, employee.LastName }
                           .Where(name => !string.IsNullOrWhiteSpace(name)));

            var worktimeUrl = configuration.GetValue<string>("WorkTimeUrl") ?? throw new Exception("Невалидна настройка на WorkTimeUrl в appsettings!");
      
            var emailRequest = new
            {
                Name = fullName,
                CompanyName = company.Name,
                AbsenceType = absence.TypeIdentifier.GetDescription(),
                StartDate = absence.FromDate.ToString("dd.MM.yyyy"),
                EndDate = absence.ToDate.ToString("dd.MM.yyyy"),
                Url = $"{worktimeUrl}{notification.Url}"
            };

            var emails = employeesToNotify
               .Select(e => e.Email)
               .Where(e => !string.IsNullOrWhiteSpace(e))
               .ToList();

            var notificationTasks = employeesToNotify
            .Select(async emp =>
            {
                var savedNotification = await notificationsService.AddNotificationByUserIdAndCompanyIdAsync(emp.WorkTimeId, notification);
                await signalRNotificationService.NotifyUser(emp.UserId, savedNotification);
            });

            var emailNotifications = emailsNotificationService.SendEmailsAsync(emails, "absences/confirm-edit-byemployee", emailRequest);

            await Task.WhenAll(notificationTasks.Append(emailNotifications));
        }
    }
}
