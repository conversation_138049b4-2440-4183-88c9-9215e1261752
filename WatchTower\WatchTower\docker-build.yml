trigger:
  branches:
    include:
      - master
variables:
  - group: deployment-variables
steps:
  - checkout: self
  - script: echo $(Build.SourceBranchName)
    displayName: "Display branch name"
  - task: SSH@0
    displayName: "Create directory"
    inputs:
      sshEndpoint: "docker-linux"
      runOptions: "inline"
      inline: |
        mkdir -p /builds/WatchTower/$(Build.Repository.Name)/$(Build.SourceBranchName)
      failOnStdErr: true
  - task: CopyFilesOverSSH@0
    displayName: "Copy files from build directory"
    inputs:
      sshEndpoint: "docker-linux"
      sourceFolder: "$(Build.SourcesDirectory)"
      contents: |
        **
        !**/.git/**
        !**/.vscode/**
      targetFolder: "/builds/WatchTower/$(Build.Repository.Name)/$(Build.SourceBranchName)"
      cleanTargetFolder: true
      overwrite: true
      failOnEmptySource: true
  - task: SSH@0
    displayName: "Build and push Docker image"
    inputs:
      sshEndpoint: "docker-linux"
      runOptions: "inline"
      inline: |
        cd /builds/WatchTower/$(Build.Repository.Name)/$(Build.SourceBranchName)
        export RepositoryName=$(Build.Repository.Name)
        export TagName=$(Build.SourceBranchName)
        export InnerPort=81
        export OuterPort=81
        export VITE_GATEWAY_API=Testing
        export VITE_WATCH_TOWER_API=Testing
        REPO_NAME=$(echo $(Build.Repository.Name) | tr '[:upper:]' '[:lower:]')
        docker build --pull --rm \
          --build-arg VITE_GATEWAY_API \
          --build-arg VITE_WATCH_TOWER_API \
          -f "Dockerfile" -t $REPO_NAME:$(Build.SourceBranchName) . 2>&1
        docker-compose -p watchtower up -d 2>&1
        docker image prune --force 2>&1
      failOnStdErr: false
