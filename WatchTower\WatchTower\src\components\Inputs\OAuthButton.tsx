import styled from "styled-components";
import Container from "../Container";
import Translator from "../../services/language/Translator";
import Text from "../Text";
import Image from "../Image";

const OAuthContainer = styled(Container)`
  display: flex;
  align-items: center;
  height: 2.5rem;
  border: 0.125rem solid var(--auth-button-border-color);
  border-radius: 2.875rem;
  background-color: var(--auth-button-background-color);
  margin-top: 0.25rem;
  margin-bottom: 0.25rem;
  min-width: 17rem;

  &:hover {
    border-color: var(--auth-button-hover-border-color);
    cursor: pointer;
  }
`;

const ContentContainer = styled.div`
  display: flex;
  align-items: center;
  width: 20rem;
  transition: transform 0.2s ease-out;

  ${OAuthContainer}:hover & {
    transform: translateX(1.5rem);
  }
`;

const LogoImg = styled(Image)`
  width: 2.3rem;
  height: 1.8rem;
  object-fit: contain;
  background-color: var(--app-background-color);
  border-top-left-radius: 2.875rem;
  border-bottom-left-radius: 2.875rem;
  padding: 0.35rem 0 0.35rem 0.35rem;

  ${OAuthContainer}:hover & {
    background-color: var(--auth-button-logo-hover-background-color);
  }
`;

const AuthText = styled(Text)`
  padding-left: 0.75rem;
  font-size: 1rem;
  font-weight: 400;
  color: var(--text-color);

  ${OAuthContainer}:hover & {
    color: var(--auth-button-hover-text-color);
  }
`;

interface OAuthButtonProps
  extends React.DetailedHTMLProps<
    React.AllHTMLAttributes<HTMLDivElement>,
    HTMLDivElement
  > {
  logo: string;
  content: string;
}

const OAuthButton = (props: OAuthButtonProps) => {
  const { logo, content, style, onClick } = props;
  return (
    <OAuthContainer style={style} onClick={onClick}>
      <ContentContainer>
        <LogoImg src={logo} alt="OAuthLogo" />
        <AuthText>
          <Translator getString={content} />
        </AuthText>
      </ContentContainer>
    </OAuthContainer>
  );
};

export default OAuthButton;
