﻿using Gateway.Common.Globals;
using MediatR;
using SSO.Common.DTOs;
using SSO.Common.Requests;
using SSO.Services.Interfaces;

namespace SSO.Handlers
{
    public class GetPermissionsHandler(IUsersService usersService, GlobalUser globalUser) : IRequestHandler<GetPermissionsRequest, IResult>
    {
        public async Task<IResult> Handle(GetPermissionsRequest request, CancellationToken cancellationToken)
        {
            var permissions = await usersService.GetUserCompanyPermissionsAsync(globalUser.Id,request.CompanyId);

            return Results.Ok(permissions);
        }
    }
}
