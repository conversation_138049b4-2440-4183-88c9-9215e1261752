﻿using MediatR;
using WorkTimeApi.Common.Requests.Payrolls;
using WorkTimeApi.Services.Interfaces.Payrolls;

namespace WorkTimeApi.Handlers.Payrolls
{
    public class LoadPayrollsPositionNamesHandler(IPayrollsService payrollService) : IRequestHandler<LoadPayrollsPositionNamesRequest, IResult>
    {
        public async Task<IResult> Handle(LoadPayrollsPositionNamesRequest request, CancellationToken cancellationToken)
        {
            var result = await payrollService.LoadPayrollsPositionNamesAsync(request.EmployeeId);
            return Results.Ok(result);
        }
    }
}
