import styled from "styled-components";

interface AvatarProps
  extends React.DetailedHTMLProps<
    React.AllHTMLAttributes<HTMLDivElement>,
    HTMLDivElement
  > {
  photo: string;
  name: string;
  size: number;
}

function getColorset() {
  var letters = "0123456789ABCDEF";
  var color = "#";
  for (var i = 0; i < 6; i++) {
    color += letters[Math.floor(Math.random() * 16)];
  }
  return color;
}

const getStringHashCode = (name: string) => {
  let initials;
  const nameSplit = name.split(" ");
  const nameLength = nameSplit.length;
  if (nameLength > 1) {
    initials =
      nameSplit[0].substring(0, 1) + nameSplit[nameLength - 1].substring(0, 1);
  } else if (nameLength === 1) {
    initials = nameSplit[0].substring(0, 1);
  } else return;

  return initials.toUpperCase();
};

const AvatarDiv = styled.div<{
  initials: string;
  getColorset(name: string): string;
}>`
  overflow: hidden;
  margin-left: 0.625rem;
  padding-top: 0.125rem;
  color: var(--avatar-initials-color);
  text-align: center;
  background: ${(p) => p.getColorset(p.initials)};
  border-radius: 0.813rem;
  width: 1.563rem;
  height: 1.363rem;
`;

const AvatarPhoto = styled.img<{ size: number }>`
  ${({ size }) => `
       height: ${size}rem;
       width: ${size}rem;
  `}
  border-radius: 1rem;
  margin-left: 1rem;
`;

const Avatar = (props: AvatarProps) => {
  const { photo, name, size } = props;
  const initials = getStringHashCode(name) ?? "";
  return (
    <>
      {photo.length === 0 ? (
        <AvatarDiv getColorset={getColorset} initials={initials}>
          {photo} {initials}
        </AvatarDiv>
      ) : (
        <AvatarPhoto src={photo} size={size} />
      )}
    </>
  );
};

export default Avatar;
