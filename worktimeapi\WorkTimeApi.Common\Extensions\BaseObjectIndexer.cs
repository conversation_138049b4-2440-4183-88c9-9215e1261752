﻿using System.Reflection;

namespace WorkTimeApi.Common.Extensions
{
    public class BaseObjectIndexer
    {
        private (PropertyInfo?, object) GetPropertyAndObject(string propertyName, Type? baseType = null, object? obj = null)
        {
            if (baseType == null)
                baseType = GetType();

            if (obj == null)
                obj = this;

            string[] parts = propertyName.Split('.');

            if (parts.Length <= 1)
                return (baseType.GetProperty(propertyName), obj);

            var property = baseType.GetProperty(parts[0]);
            var propertyValue = property?.GetValue(obj);

            if (propertyValue == null)
                return (property, obj);

            return GetPropertyAndObject(string.Join(".", parts.Skip(1)),
               property?.PropertyType,
               propertyValue);
        }

        public object? this[string propertyName]
        {
            get
            {
                var (propertyInfo, obj) = GetPropertyAndObject(propertyName);

                return propertyInfo?.GetValue(obj);
            }
            set
            {
                var (myPropertyInfo, obj) = GetPropertyAndObject(propertyName);
                myPropertyInfo?.SetValue(obj, value);
            }
        }
    }
}
