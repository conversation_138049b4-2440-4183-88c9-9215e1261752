﻿using Gateway.Common.Extenstions.EndpointExtensions;
using Gateway.Extenstions.MediatorExtensions;
using WorkTimeApi.Common.Requests.StructureLevels;

namespace Gateway.EndpointDefinitions.StructureLevels
{
	public class StructureLevelsEndpointDefinitions : IEndpointDefinition
	{
		public void DefineEndpoints(WebApplication app)
		{
			app.AuthenticatedGet<GetStructureLevelsRequest>("/structure-levels")
				.AuthenticatedPost<AddStructureLevelRequest>("/structure-levels")
				.AuthenticatedDelete<DeleteStructureLevelRequest>("/structure-levels/{StructureLevelId}");
        }

		public void DefineServices(IServiceCollection services)
		{
		}
	}
}
