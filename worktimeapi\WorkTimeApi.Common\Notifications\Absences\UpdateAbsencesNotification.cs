﻿using WorkTimeApi.Common.DTOs.Absences;

namespace WorkTimeApi.Common.Notifications.Absences
{
    public class UpdateAbsencesNotification(List<AbsenceHospitalDTO> payload, Guid companyId, Guid userId, string creatorName,Guid? employeeId)
        : BaseNotification<List<AbsenceHospitalDTO>>(payload, companyId, NotificationsName.Absences.EditedByEmployee.PushMany, creatorName)
    {
        public Guid UserId { get; } = userId;

        public Guid? EmployeeId { get; } = employeeId;

    }
}
