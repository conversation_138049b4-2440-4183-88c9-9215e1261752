﻿using MediatR;
using WorkTimeApi.Common.Requests.Employees;
using WorkTimeApi.Services.Interfaces.Employees;

namespace WorkTimeApi.Handlers.Employees
{
    public class DeleteEmployeePropertyEditsHandler : IRequestHandler<DeleteEmployeePropertyEditsRequest, IResult>
    {
        private readonly IEmployeesService _employeesService;

        public DeleteEmployeePropertyEditsHandler(IEmployeesService employeesService)
        {
            _employeesService = employeesService;
        }

        public async Task<IResult> Handle(DeleteEmployeePropertyEditsRequest request, CancellationToken cancellationToken)
        {
            await _employeesService.DeleteEmployeePropertyEditsAsync(request.EmployeeId);

            return Results.Ok();
        }
    }
}
