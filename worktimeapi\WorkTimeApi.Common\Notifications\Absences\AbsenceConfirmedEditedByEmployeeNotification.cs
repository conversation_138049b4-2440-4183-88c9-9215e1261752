﻿using WorkTimeApi.Common.DTOs.Absences;

namespace WorkTimeApi.Common.Notifications.Absences
{
    public class AbsenceConfirmedEditedByEmployeeNotification : BaseNotification<AbsenceHospitalDTO>
    {
        public Guid UserId { get; }

        public AbsenceHospitalDTO OldAbsence { get; }

        public AbsenceConfirmedEditedByEmployeeNotification(AbsenceHospitalDTO payload, AbsenceHospitalDTO oldAbsence, Guid companyId, Guid userId, string creatorName)
            : base(payload, companyId, NotificationsName.Absences.EditedConfirmedAbsenceByEmployee.Push, creatorName)
        {
            UserId = userId;
            OldAbsence = oldAbsence;

            if (payload != null)
            {
                Url = $"{companyId}/attendance?date={payload.FromDate:MM.yyyy}&absenceId={payload.Id}&fromNotification=true";
            }
        }
    }
}
