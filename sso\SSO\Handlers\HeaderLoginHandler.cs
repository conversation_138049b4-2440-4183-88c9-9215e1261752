﻿using MediatR;
using SSO.Common.DTOs;
using SSO.Common.Requests;
using SSO.Connections.Interfaces;
using SSO.Services.Interfaces;
using System.Text.Json;

namespace SSO.Handlers
{
    public class HeaderLoginHandler(IUserRegistrationsConnection userRegistrationsConnection,
        IJwtGeneratorService jwtGeneratorService,
        IRefreshTokensService refreshTokensService) : IRequestHandler<HeaderLoginRequest, IResult>
    {
        public async Task<IResult> Handle(HeaderLoginRequest request, CancellationToken cancellationToken)
        {
            if (request.HttpRequest is null)
                return Results.BadRequest();

            var authorizationHeader = request.HttpRequest.Headers.Authorization;

            var response = await userRegistrationsConnection.TryLoginWithResponseAsync(authorizationHeader.ToString());
            if (response.StatusCode == System.Net.HttpStatusCode.NotFound)
                return Results.NotFound("Invalid Credentials!");

            if (!response.IsSuccessStatusCode)
                return Results.BadRequest("Unknown error!");

            var userDTO = JsonSerializer.Deserialize<UserDTO>(await response.Content.ReadAsStringAsync(cancellationToken));
            if (userDTO is null)
                return Results.BadRequest();

            request.HttpRequest.HttpContext.Response.Headers.Append("Authorization", $"Bearer {await jwtGeneratorService.GenerateAccessTokenAsync(userDTO)}");

            var newRefreshToken = await jwtGeneratorService.GenerateRefreshTokenAsync(userDTO);

            if (string.IsNullOrEmpty(userDTO.Email))
                return Results.BadRequest("Invalid email address");

            await refreshTokensService.AddRefreshTokenAsync(newRefreshToken, userDTO.Id, userDTO.Email);
            request.HttpRequest.HttpContext.Response.Headers.Append("Refresh-Token", newRefreshToken);

            return Results.Ok(userDTO);
        }
    }
}
