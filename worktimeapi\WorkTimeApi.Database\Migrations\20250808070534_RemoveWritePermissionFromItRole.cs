﻿using System;
using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace WorkTimeApi.Database.Migrations
{
    /// <inheritdoc />
    public partial class RemoveWritePermissionFromItRole : Migration
    {
        /// <inheritdoc />
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DeleteData(
                table: "PermissionRoles",
                keyColumns: new[] { "PermissionId", "RoleId" },
                keyValues: new object[] { new Guid("79a3e9d2-999c-a677-c304-1054d8d29dc0"), new Guid("e4a7048a-8045-45ed-bee2-cee5ac65c574") });
        }

        /// <inheritdoc />
        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.InsertData(
                table: "PermissionRoles",
                columns: new[] { "PermissionId", "RoleId" },
                values: new object[] { new Guid("79a3e9d2-999c-a677-c304-1054d8d29dc0"), new Guid("e4a7048a-8045-45ed-bee2-cee5ac65c574") });
        }
    }
}
