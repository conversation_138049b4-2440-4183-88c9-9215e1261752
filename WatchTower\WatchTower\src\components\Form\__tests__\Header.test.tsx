import { render, screen } from "@testing-library/react";
import { Header } from "../Header";
import "@testing-library/jest-dom";

describe("Header Component", () => {
  it("renders children correctly", () => {
    render(<Header>Test Header</Header>);

    const headerElement = screen.getByRole("heading", { level: 2 });
    expect(headerElement).toBeInTheDocument();
    expect(headerElement.textContent).toBe("Test Header");
  });

  it("passes additional props to the h2 element", () => {
    render(
      <Header className="test-class" id="test-id">
        Test Header
      </Header>
    );

    const headerElement = screen.getByRole("heading", { level: 2 });
    expect(headerElement).toHaveClass("test-class");
    expect(headerElement).toHaveAttribute("id", "test-id");
  });
});
