﻿using Gateway.Connections.Interfaces;
using MediatR;
using WorkTimeApi.Common.DTOs.Companies;

namespace Gateway.Handlers.Companies
{
    public class GetCompanyByEmailAndBulstatHandler(IWorkTimeApiConnection worktimeApiConnection) : IRequestHandler<GetCompanyByEmailAndBulstatRequest, IResult>
    {
        public async Task<IResult> Handle(GetCompanyByEmailAndBulstatRequest request, CancellationToken cancellationToken)
        {
            var companyResponse = await worktimeApiConnection.GetCompanyByEmailAndBulstatAsync(request.Email, request.Bulstat);
            if (!companyResponse.IsSuccessStatusCode)
                return Results.StatusCode((int)companyResponse.StatusCode);

            var company = await companyResponse.Content.ReadFromJsonAsync<CompanyDTO>(cancellationToken);

            return Results.Ok(company);
        }
    }
}