﻿using MediatR;
using SSO.Common.Requests;
using SSO.Connections.Interfaces;

namespace SSO.Handlers
{
    public class ChangeForgottenPasswordHandler : IRequestHandler<ChangeForgottenPasswordRequest, IResult>
    {
        private readonly IUserRegistrationsConnection _userRegistrationsConnection;

        public ChangeForgottenPasswordHandler(IUserRegistrationsConnection userRegistrationsConnection)
        {
            _userRegistrationsConnection = userRegistrationsConnection;
        }

        public async Task<IResult> Handle(ChangeForgottenPasswordRequest request, CancellationToken cancellationToken)
        {
            var response = await _userRegistrationsConnection.ChangeForgottenPasswordAsync(request.ChangeForgottenPasswordDTO);
            return response is not null && response.IsSuccessStatusCode ? Results.Ok() : Results.BadRequest();
        }
    }
}
