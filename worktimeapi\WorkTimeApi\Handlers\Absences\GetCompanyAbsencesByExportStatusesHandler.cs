using MediatR;
using WorkTimeApi.Common.Requests.Absences;
using WorkTimeApi.Services.Interfaces.Absences;

namespace WorkTimeApi.Handlers.Absences
{
    public class GetCompanyAbsencesByExportStatusesHandler(IAbsencesService absencesService) : IRequestHandler<GetCompanyAbsencesByExportStatusesRequest, IResult>
    {
        public async Task<IResult> Handle(GetCompanyAbsencesByExportStatusesRequest request, CancellationToken cancellationToken)
        {
            if (!DateTime.TryParseExact(request.Month, "MM.yyyy", null, System.Globalization.DateTimeStyles.None, out var monthDate))
            {
                return Results.BadRequest("Invalid month format. Expected format: MM.yyyy (e.g., 08.2025)");
            }

            var result = await absencesService.GetCompanyAbsencesForTRZByMonthAsync(request.CompanyId, monthDate, request.Exported);

            return Results.Ok(result);
        }
    }
}
