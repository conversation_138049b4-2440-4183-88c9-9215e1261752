﻿using WorkTimeApi.Common.DTOs.Absences;

namespace WorkTimeApi.Common.Notifications.Absences
{
    public class AbsenceStatusChangedNotification : BaseNotification<AbsenceHospitalDTO>
    {
        public Guid UserId { get; }

        public AbsenceHospitalDTO OldAbsence { get; }

        public AbsenceStatusChangedNotification(AbsenceHospitalDTO payload, Guid companyId, Guid userId, string creatorName, AbsenceHospitalDTO oldAbsence)
            : base(payload, companyId, NotificationsName.Absences.AddedByEmployee.Push, creatorName)
        {
            UserId = userId;
            OldAbsence = oldAbsence;

            if (payload != null)
            {
                Url = $"{companyId}/attendance?date={payload.FromDate:MM.yyyy}&absenceId={payload.Id}&fromNotification=true";
            }
        }
    }
}
