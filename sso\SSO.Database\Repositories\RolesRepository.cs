using Microsoft.EntityFrameworkCore;
using SSO.Database.Models;
using SSO.Database.Repositories.Interfaces;

namespace SSO.Database.Repositories
{
    public class RolesRepository(IDbContextFactory<SSODbContext> contextFactory) : IRolesRepository
    {
        public async Task AddRoleToEmployeeAsync(RoleUser roleUser)
        {
            var context = await contextFactory.CreateDbContextAsync();

            var existingRole = await context
                .RoleUsers
                .FirstOrDefaultAsync(ru => ru.RolesId == roleUser.RolesId 
                    && ru.UsersId == roleUser.UsersId 
                    && ru.UserRegistrationCompanyId == roleUser.UserRegistrationCompanyId);

            if (existingRole != null)
                return;

            await context.RoleUsers.AddAsync(roleUser);
            await context.SaveChangesAsync();
        }
    }
}
