using MediatR;
using WorkTimeApi.Common.Requests.TRZ;
using WorkTimeApi.Services.Interfaces.Absences;

namespace WorkTimeApi.Handlers.TRZ
{
    public class MarkTRZAbsencesExportedHandler(IAbsencesService absencesService) : IRequestHandler<MarkTRZAbsencesExportedRequest, IResult>
    {
        public async Task<IResult> Handle(MarkTRZAbsencesExportedRequest request, CancellationToken cancellationToken)
        {
            var updated = await absencesService.MarkAbsencesAndHospitalsExportedAsync(request.CompanyId, request.AbsenceIds, request.HospitalIds);
            return Results.Ok(updated);
        }
    }
}