﻿using Gateway.Common.Globals;
using Gateway.Common.Redis.Models;
using Gateway.Common.Redis.Services.Interfaces;
using Gateway.Common.Requests.Companies;
using Gateway.Common.ResponseObjects;
using Gateway.Connections.Interfaces;
using MediatR;
using WorkTimeApi.Common.DTOs.Companies;
using WorkTime = WorkTimeApi.Common.Requests.Companies;

namespace Gateway.Handlers.Companies
{
    public class CompaniesHandler(IWorkTimeApiConnection workTimeApiConnection,
        IRedisService<RedisCompanyDTO> companiesRedisService,
        GlobalUser globalUser) : IRequestHandler<GetCompaniesRequest, IResult>
    {
        public async Task<IResult> Handle(GetCompaniesRequest request, CancellationToken cancellationToken)
        {
            var worktimeRequest = new WorkTime.GetCompaniesRequest()
            {
                UserId = globalUser.Id
            };
            var worktimeResponse = await workTimeApiConnection.GetCompaniesAsync(worktimeRequest);
            if (!worktimeResponse.IsSuccessStatusCode)
                return Results.StatusCode((int)worktimeResponse.StatusCode);

            var worktimeCompanies = await worktimeResponse.Content.ReadFromJsonAsync<GetCompaniesResponse>(cancellationToken);
            if (worktimeCompanies == null)
                return Results.Ok(Enumerable.Empty<CompanyResponseObject>());

            var activeCompanies = worktimeCompanies.ActiveCompanies.Select(wtc => new CompanyResponseObject
            {
                Id = wtc.Id,
                Name = wtc.Name,
                ContactName = wtc.ContactName,
                Bulstat = wtc.Bulstat,
                UserRegistrationCompanyId = wtc.UserRegistrationCompanyId
            });

            var pendingCompanies = new List<CompanyResponseObject>();

            var urPendingCompanies = worktimeCompanies.PendingCompanies.Select(c => companiesRedisService.GetByKey(c.UserRegistrationCompanyId));

            if (urPendingCompanies is not null)
            {
                var pendingCompaniesResponse = urPendingCompanies.Select(c => new CompanyResponseObject
                {
                    Id = worktimeCompanies.PendingCompanies.FirstOrDefault(wtc => wtc.UserRegistrationCompanyId == c.Id)?.Id ?? Guid.Empty,
                    Name = c.Name,
                    ContactName = c.ContactName,
                    Bulstat = c.Bulstat,
                    UserRegistrationCompanyId = c.Id
                });

                pendingCompanies = [.. pendingCompaniesResponse];
            }

            return Results.Ok(new
            {
                ActiveCompanies = activeCompanies,
                PendingCompanies = pendingCompanies
            });
        }
    }
}
