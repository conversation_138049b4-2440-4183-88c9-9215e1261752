﻿using WorkTimeApi.Common.DTOs.Absences;

namespace WorkTimeApi.Common.Notifications.Hospitals
{
    public class HospitalsAddedNotification : BaseNotification<List<AbsenceHospitalDTO>>
    {
        public Guid UserId { get; }

        public HospitalsAddedNotification(List<AbsenceHospitalDTO> payload, Guid companyId, Guid userId, string creatorName)
            : base(payload, companyId, NotificationsName.Hospitals.AddedByEmployee.Push, creatorName)
        {
            UserId = userId;
            var firstAbsence = payload.FirstOrDefault();
            if (firstAbsence != null)
            {
                Url = $"{companyId}/attendance?date={firstAbsence.FromDate:MM.yyyy}&absenceId={firstAbsence.Id}&fromNotification=true";
            }
        }
    }
}
