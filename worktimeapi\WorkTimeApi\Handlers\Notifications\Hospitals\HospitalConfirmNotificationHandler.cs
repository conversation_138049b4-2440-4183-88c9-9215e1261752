﻿using MediatR;
using WorkTime.Notifications.Services.Interfaces;
using WorkTimeApi.Common.Enums;
using WorkTimeApi.Common.Notifications.Hospitals;
using WorkTimeApi.Database.Repositories.Interfaces.Companies;
using WorkTimeApi.Services.Interfaces.Absences;
using WorkTimeApi.Services.Interfaces.Notifications;

namespace WorkTimeApi.Handlers.Notifications.Hospitals
{
    public class HospitalConfirmNotificationHandler(ISignalRNotificationService signalRNotificationService,
        INotificationsService notificationsService,
        IAbsencesService absencesService,
        IEmailsNotificationService emailsNotificationService,
        ICompaniesRepository companiesRepository,
        IConfiguration configuration) : INotificationHandler<HospitalConfirmNotification>
    {
        public async Task Handle(HospitalConfirmNotification notification, CancellationToken cancellationToken)
        {
            var employee = await absencesService.GetEmployeeDTOByAbsenceIdAsync(notification.Payload.Id, notification.Payload.IsHospital) ?? throw new Exception("<PERSON><PERSON><PERSON>àëèäíî Employee!");

            var company = await companiesRepository.GetByIdAsync(notification.CompanyId) ?? throw new Exception("Невалидна компания!");
            var fullName = string.Join(" ", new[] { employee.FirstName, employee.LastName }
                .Where(name => !string.IsNullOrWhiteSpace(name)));

            var worktimeUrl = configuration.GetValue<string>("WorkTimeUrl") ?? throw new Exception("Липсва конфигурация за WorkTimeUrl!");

            var hospital = notification.Payload;

            var emailRequest = new
            {
                Name = fullName,
                CompanyName = company.Name,
                AbsenceType = hospital.TypeIdentifier.GetDescription(),
                StartDate = hospital.FromDate.ToString("dd.MM.yyyy"),
                EndDate = hospital.ToDate.ToString("dd.MM.yyyy"),
                Comment = hospital.Reference,
                Url = $"{worktimeUrl}{notification.Url}"
            };

            var emailNotifications = emailsNotificationService.SendEmailsAsync(new List<string> { employee.Email }, "absences/confirmed", emailRequest);

            var savedNotification = await notificationsService.AddNotificationByUserIdAndCompanyIdAsync(employee.WorkTimeId, notification);
            await signalRNotificationService.NotifyUser(employee.UserId, savedNotification);
        }
    }
}