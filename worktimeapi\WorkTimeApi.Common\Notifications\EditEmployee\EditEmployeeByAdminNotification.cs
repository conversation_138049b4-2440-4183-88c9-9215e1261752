﻿using WorkTimeApi.Common.DTOs.Employees;

namespace WorkTimeApi.Common.Notifications.EditEmployee
{
    public class EditEmployeeByAdminNotification : BaseNotification<EmployeeDTO>
    {
        public Guid UserId { get; }

        public EditEmployeeByAdminNotification(EmployeeDTO payload, Guid payrollId, Guid companyId, Guid userId, string creatorName)
            : base(payload, companyId, NotificationsName.Employees.EditedByAdmin.Push, creatorName)
        {
            UserId = userId;

            if (payload == null)
                return;

            if (payrollId == Guid.Empty)
            {
                Url = $"{companyId}/employees/0/{payload.WorkTimeId}?fromNotification=true";
            }
            else
            {
                Url = $"{companyId}/employees/0/{payload.WorkTimeId}/{payrollId}?fromNotification=true";
            }
        }
    }
}