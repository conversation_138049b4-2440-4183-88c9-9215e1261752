﻿using Gateway.Connections.Interfaces;
using WorkTimeApi.Common.Requests.OnboardingDocuments;
using MediatR;

namespace Gateway.Handlers.OnboardingDocuments
{
	public class AddOnboardingDocumentHandler : IRequestHandler<AddOnboardingDocumentRequest, IResult>
	{
		private readonly IWorkTimeApiConnection _workTimeApiConnection;

		public AddOnboardingDocumentHandler(IWorkTimeApiConnection workTimeApiConnection)
		{
			_workTimeApiConnection = workTimeApiConnection;
		}

		public async Task<IResult> Handle(AddOnboardingDocumentRequest request, CancellationToken cancellationToken)
		{
			var response = await _workTimeApiConnection.AddOnboardingDocumentAsync(request);

			if (!response.IsSuccessStatusCode)
				return Results.StatusCode((int)response.StatusCode);

			return Results.Text(await response.Content.ReadAsStringAsync(cancellationToken));
		}
	}
}