import { NomenclatureDTO } from "../nomenclatures/NomenclatureDTO";
import { ContractDTO } from "./ContractDTO";
import { LengthOfServiceDTO } from "./LengthOfService";

export interface PayrollDataDTO {
  contractType: ContractDTO;
  dailyWorktime: number;
  structureLevelId: string;
  structureLevelName: string;
  workplace: string;
  contractReason?: NomenclatureDTO;
  additionalTerms: string;
  fromDate: Date | null;
  annexFromDate: Date | null;
  professionalЕxperienceInCompany?: LengthOfServiceDTO;
  professionalЕxperience?: LengthOfServiceDTO;
  workExperience?: LengthOfServiceDTO;
  contractNumber?: string;
  annexPayrollNumber?: string;
  kid: string;
  ekatte: string;
  nkpd: string;
  position?: NomenclatureDTO;

  toDate: Date | null;
  contractTermDate: Date | null;
  contractDate: Date | null;
  contractEndDate: Date | null;
  contractTerminationDate: Date | null;
}
