﻿using Gateway.Common.Requests;

namespace Gateway.Requests.UserRegistration
{
    public class RegistrationRequest : BaseRequest
    {
        public string Email { get; set; } = string.Empty;

        public string Password { get; set; } = string.Empty;

        public string FirstName { get; set; } = string.Empty;

        public string SecondName { get; set; } = string.Empty;

        public string LastName { get; set; } = string.Empty;

        public string CallbackUrl { get; set; } = string.Empty;

        public string TemplateName { get; set; } = string.Empty;
    }
}
