import { authenticatedGet } from "../worktimeConnectionService";
import { PayrollSummaryDTO } from "../../models/DTOs/payrolls/PayrollSummaryDTO";
import { PayrollsPositionNamesDTO } from "../../models/DTOs/payrolls/PayrollsPositionNamesDTO";

export const getPayrollData = async (
  companyId: string,
  payrollId: string
): Promise<PayrollSummaryDTO> => {
  var payrollData = await authenticatedGet<PayrollSummaryDTO>(
    `${companyId}/payrolls/payroll-data?payrollId=${payrollId}`
  );
  return payrollData;
};

export const getEmployeePayrollsPositionNames = async (
  companyId: string,
  employeeId: string
): Promise<PayrollsPositionNamesDTO[]> => {
  var payrollNames = await authenticatedGet<PayrollsPositionNamesDTO[]>(
    `${companyId}/payrolls/position-names?employeeId=${employeeId}`
  );
  return payrollNames;
};
