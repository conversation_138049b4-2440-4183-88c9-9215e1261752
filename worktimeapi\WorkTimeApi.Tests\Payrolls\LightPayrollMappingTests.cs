using AutoMapper;
using WorkTimeApi.Common.DTOs.Payrolls;
using WorkTimeApi.Common.Enums;
using WorkTimeApi.Database.Models;
using WorkTimeApi.Mappers;

namespace WorkTimeApi.Tests.Payrolls;

public class LightPayrollMappingTests
{
    private static IMapper CreateMapper()
    {
        var config = new MapperConfiguration(cfg => cfg.AddProfile<MapperProfile>());
        return config.CreateMapper();
    }

    [Fact]
    public void Map_Payroll_To_LightPayroll_Uses_Last_Annex_By_FromDate_For_Position()
    {
        var mapper = CreateMapper();

        var companyId = Guid.NewGuid();
        var structureLevelMain = new StructureLevel { Id = Guid.NewGuid(), Name = "Main", Type = StructureLevelType.Department, CompanyId = companyId };
        var structureLevelAnnex1 = new StructureLevel { Id = Guid.NewGuid(), Name = "Annex1", Type = StructureLevelType.Department, CompanyId = companyId };
        var structureLevelAnnex2 = new StructureLevel { Id = Guid.NewGuid(), Name = "Annex2", Type = StructureLevelType.Department, CompanyId = companyId };

        var company = new Company { Id = companyId, Name = "Comp", Bulstat = "B" };
        var employee = new Employee { Id = Guid.NewGuid(), CompanyId = companyId, Company = company, UserId = Guid.NewGuid(), User = new User() };

        var payroll = new Payroll
        {
            Id = Guid.NewGuid(),
            CompanyId = companyId,
            Company = company,
            EmployeeId = employee.Id,
            Employee = employee,
            Position = "MainPos",
            StructureLevelId = structureLevelMain.Id,
            StructureLevel = structureLevelMain
        };

        var annexOld = new AnnexPayroll
        {
            Id = Guid.NewGuid(),
            PayrollId = payroll.Id,
            MainPayroll = payroll,
            FromDate = new DateTime(2023, 1, 1),
            Position = "AnnexOld",
            StructureLevelId = structureLevelAnnex1.Id,
            StructureLevel = structureLevelAnnex1
        };

        var annexNew = new AnnexPayroll
        {
            Id = Guid.NewGuid(),
            PayrollId = payroll.Id,
            MainPayroll = payroll,
            FromDate = new DateTime(2023, 5, 1),
            Position = "AnnexNew",
            StructureLevelId = structureLevelAnnex2.Id,
            StructureLevel = structureLevelAnnex2
        };

        payroll.AnnexPayrolls = new List<AnnexPayroll> { annexOld, annexNew };

        var dto = mapper.Map<LightPayrollDTO>(payroll);

        Assert.NotNull(dto.Position);
        Assert.Equal("AnnexNew", dto.Position!.Name);
    }
}