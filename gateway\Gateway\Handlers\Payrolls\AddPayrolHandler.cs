﻿using Gateway.Connections.Interfaces;
using MediatR;
using WorkTimeApi.Common.Requests.Payrolls;

namespace Gateway.Handlers.Payrolls
{
    public class AddPayrolHandler : IRequestHandler<AddPayrollRequest, IResult>
	{
		private readonly IWorkTimeApiConnection _workTimeApiConnection;

		public AddPayrolHandler(IWorkTimeApiConnection workTimeApiConnection)
		{
			_workTimeApiConnection = workTimeApiConnection;
		}

		public async Task<IResult> Handle(AddPayrollRequest request, CancellationToken cancellationToken)
		{
			var response = await _workTimeApiConnection.AddPayrollAsync(request);

			if (!response.IsSuccessStatusCode)
				return Results.StatusCode((int)response.StatusCode);

			return Results.Text(await response.Content.ReadAsStringAsync(cancellationToken));
		}
	}
}
