using WorkTimeApi.Common.Enums;

namespace WorkTimeApi.Tests.Enums;

public class HospitalCategoriesTests
{
    [Theory]
    [InlineData(EventType.Болничен, true)]
    [InlineData(EventType.ГледанеНаБоленЧленОтСемейството, true)]
    [InlineData(EventType.НеплатенЗаВременнаНеработоспособност, true)]
    [InlineData(EventType.БолниченПоБременност, true)]
    [InlineData(EventType.БолниченСледРаждане, true)]
    [InlineData(EventType.НеплатенЗаБременностИРаждане, true)]
    [InlineData(EventType.ОтпускЗаМайкаСледРаждане135До410Ден, true)]
    [InlineData(EventType.ОтпускЗаБащаЗаГледанеНаДетеНад6Месеца, true)]
    [InlineData(EventType.ОтпускДо15ДниПриРажданеНаДете, true)]
    [InlineData(EventType.ОтпускЗаОтглежданеНаДетеДо2Години, true)]
    [InlineData(EventType.ОтпускПриОсиновяванеНаДетеДо5ГодишнаВъзраст, true)]
    [InlineData(EventType.НеплатенОтпускПриОсиновяванеНаДетеДо5ГодишнаВъзраст, true)]
    [InlineData(EventType.ОтпускЗаБащаПриОсиновяванеНаДетеДо5ГодишнаВъзраст, true)]
    [InlineData(EventType.ОтпускЗаОтглежданеНаДетеДо8ГодишнаВъзрастОтБащата, true)]
    public void IsHospital_Should_Return_True_For_All_Hospital_EventTypes(EventType eventType, bool expected)
    {
        // Act
        var result = eventType.IsHospital();

        // Assert
        Assert.Equal(expected, result);
    }

    [Theory]
    [InlineData(EventType.ПлатенГодишенОтпуск, false)]
    [InlineData(EventType.ПлатенГодишенОтпускЗаМиналаГодина, false)]
    [InlineData(EventType.НеплатенСОсигурителенСтажОтОсигурител, false)]
    [InlineData(EventType.ПлатенОбучениеЧл169Ал1, false)]
    [InlineData(EventType.ИнцидентенГражданскиБракЧл157Ал1Т1, false)]
    [InlineData(EventType.ПлатенКомпенсация, false)]
    public void IsHospital_Should_Return_False_For_Non_Hospital_EventTypes(EventType eventType, bool expected)
    {
        // Act
        var result = eventType.IsHospital();

        // Assert
        Assert.Equal(expected, result);
    }

    [Theory]
    [InlineData(TRZEventType.Болничен, true)]
    [InlineData(TRZEventType.ГледанеНаБоленЧлен, true)]
    [InlineData(TRZEventType.ТрудоваЗлополука, true)]
    [InlineData(TRZEventType.ПрофесионалнаБолест, true)]
    [InlineData(TRZEventType.НеплатенПоНетрудоспособност, true)]
    [InlineData(TRZEventType.БолниченПоБременност, true)]
    [InlineData(TRZEventType.БолниченСледРаждане, true)]
    [InlineData(TRZEventType.НеплатенЗаБременност, true)]
    [InlineData(TRZEventType.ОтпускМайка135До410, true)]
    [InlineData(TRZEventType.ОтпускБащаНад6Месеца, true)]
    [InlineData(TRZEventType.Отпуск15ДниРаждане, true)]
    [InlineData(TRZEventType.ОтпускЗаДетеДо2, true)]
    [InlineData(TRZEventType.ОтпускОсиновяванеДо5, true)]
    [InlineData(TRZEventType.НеплатенОсиновяванеДо5, true)]
    [InlineData(TRZEventType.БащаОсиновителДо5, true)]
    [InlineData(TRZEventType.БащаГледаДетеДо8, true)]
    public void IsHospital_Should_Return_True_For_All_Hospital_TRZEventTypes(TRZEventType trzEventType, bool expected)
    {
        // Act
        var result = trzEventType.IsHospital();

        // Assert
        Assert.Equal(expected, result);
    }

    [Theory]
    [InlineData(TRZEventType.ПлатенГодишенОтпуск, false)]
    [InlineData(TRZEventType.ПлатенГодишенОтпускЗаМиналаГодина, false)]
    [InlineData(TRZEventType.НеплатенСОсигурителенСтажОтОсигурител, false)]
    [InlineData(TRZEventType.ПлатенПоДругиЧленове, false)]
    [InlineData(TRZEventType.ПлатенПоЧл173а, false)]
    public void IsHospital_Should_Return_False_For_Non_Hospital_TRZEventTypes(TRZEventType trzEventType, bool expected)
    {
        // Act
        var result = trzEventType.IsHospital();

        // Assert
        Assert.Equal(expected, result);
    }

    [Theory]
    [InlineData(EventType.ГледанеНаБоленЧленОтСемейството, TRZEventType.ГледанеНаБоленЧлен)]
    [InlineData(EventType.НеплатенЗаВременнаНеработоспособност, TRZEventType.НеплатенПоНетрудоспособност)]
    [InlineData(EventType.НеплатенЗаБременностИРаждане, TRZEventType.НеплатенЗаБременност)]
    public void ToTRZEventType_Should_Map_Sick_Leave_600_Range_To_Existing_TRZEventTypes(EventType eventType, TRZEventType expectedTrzEventType)
    {
        // Act
        var result = eventType.ToTRZEventType();

        // Assert
        Assert.Equal(expectedTrzEventType, result);
    }

    [Theory]
    [InlineData(EventType.ОтпускЗаМайкаСледРаждане135До410Ден, TRZEventType.ОтпускМайка135До410)]
    [InlineData(EventType.ОтпускЗаБащаЗаГледанеНаДетеНад6Месеца, TRZEventType.ОтпускБащаНад6Месеца)]
    [InlineData(EventType.ОтпускДо15ДниПриРажданеНаДете, TRZEventType.Отпуск15ДниРаждане)]
    [InlineData(EventType.ОтпускЗаОтглежданеНаДетеДо2Години, TRZEventType.ОтпускЗаДетеДо2)]
    [InlineData(EventType.ОтпускПриОсиновяванеНаДетеДо5ГодишнаВъзраст, TRZEventType.ОтпускОсиновяванеДо5)]
    [InlineData(EventType.НеплатенОтпускПриОсиновяванеНаДетеДо5ГодишнаВъзраст, TRZEventType.НеплатенОсиновяванеДо5)]
    [InlineData(EventType.ОтпускЗаБащаПриОсиновяванеНаДетеДо5ГодишнаВъзраст, TRZEventType.БащаОсиновителДо5)]
    [InlineData(EventType.ОтпускЗаОтглежданеНаДетеДо8ГодишнаВъзрастОтБащата, TRZEventType.БащаГледаДетеДо8)]
    public void ToTRZEventType_Should_Map_Maternity_Leave_700_Range_To_Existing_TRZEventTypes(EventType eventType, TRZEventType expectedTrzEventType)
    {
        // Act
        var result = eventType.ToTRZEventType();

        // Assert
        Assert.Equal(expectedTrzEventType, result);
    }

    [Theory]
    [InlineData(TRZEventType.ГледанеНаБоленЧлен, EventType.ГледанеНаБоленЧленОтСемейството)]
    [InlineData(TRZEventType.ТрудоваЗлополука, EventType.ТрудоваЗлополука)]
    [InlineData(TRZEventType.ПрофесионалнаБолест, EventType.ПрофесионалнаБолест)]
    [InlineData(TRZEventType.НеплатенПоНетрудоспособност, EventType.НеплатенЗаВременнаНеработоспособност)]
    [InlineData(TRZEventType.БолниченПоБременност, EventType.БолниченПоБременност)]
    [InlineData(TRZEventType.БолниченСледРаждане, EventType.БолниченСледРаждане)]
    [InlineData(TRZEventType.НеплатенЗаБременност, EventType.НеплатенЗаБременностИРаждане)]
    public void ToEventType_Should_Map_Existing_TRZEventTypes_To_Sick_Leave_600_Range(TRZEventType trzEventType, EventType expectedEventType)
    {
        // Act
        var result = trzEventType.ToEventType();

        // Assert
        Assert.Equal(expectedEventType, result);
    }

    [Theory]
    [InlineData(TRZEventType.ОтпускМайка135До410, EventType.ОтпускЗаМайкаСледРаждане135До410Ден)]
    [InlineData(TRZEventType.ОтпускБащаНад6Месеца, EventType.ОтпускЗаБащаЗаГледанеНаДетеНад6Месеца)]
    [InlineData(TRZEventType.Отпуск15ДниРаждане, EventType.ОтпускДо15ДниПриРажданеНаДете)]
    [InlineData(TRZEventType.ОтпускЗаДетеДо2, EventType.ОтпускЗаОтглежданеНаДетеДо2Години)]
    [InlineData(TRZEventType.ОтпускОсиновяванеДо5, EventType.ОтпускПриОсиновяванеНаДетеДо5ГодишнаВъзраст)]
    [InlineData(TRZEventType.НеплатенОсиновяванеДо5, EventType.НеплатенОтпускПриОсиновяванеНаДетеДо5ГодишнаВъзраст)]
    [InlineData(TRZEventType.БащаОсиновителДо5, EventType.ОтпускЗаБащаПриОсиновяванеНаДетеДо5ГодишнаВъзраст)]
    [InlineData(TRZEventType.БащаГледаДетеДо8, EventType.ОтпускЗаОтглежданеНаДетеДо8ГодишнаВъзрастОтБащата)]
    public void ToEventType_Should_Map_Existing_TRZEventTypes_To_Maternity_Leave_700_Range(TRZEventType trzEventType, EventType expectedEventType)
    {
        // Act
        var result = trzEventType.ToEventType();

        // Assert
        Assert.Equal(expectedEventType, result);
    }

    [Fact]
    public void Hospital_Categories_Mapping_Should_Be_Bidirectional_Consistent()
    {
        // Arrange - Test all hospital categories in 600-700 range
        var hospitalEventTypes = new[]
        {
            EventType.ГледанеНаБоленЧленОтСемейството,
            EventType.НеплатенЗаВременнаНеработоспособност,
            EventType.НеплатенЗаБременностИРаждане,
            EventType.ОтпускЗаМайкаСледРаждане135До410Ден,
            EventType.ОтпускЗаБащаЗаГледанеНаДетеНад6Месеца,
            EventType.ОтпускДо15ДниПриРажданеНаДете,
            EventType.ОтпускЗаОтглежданеНаДетеДо2Години,
            EventType.ОтпускПриОсиновяванеНаДетеДо5ГодишнаВъзраст,
            EventType.НеплатенОтпускПриОсиновяванеНаДетеДо5ГодишнаВъзраст,
            EventType.ОтпускЗаБащаПриОсиновяванеНаДетеДо5ГодишнаВъзраст,
            EventType.ОтпускЗаОтглежданеНаДетеДо8ГодишнаВъзрастОтБащата
        };

        foreach (var eventType in hospitalEventTypes)
        {
            // Act
            var trzEventType = eventType.ToTRZEventType();
            var mappedBackEventType = trzEventType.ToEventType();

            // Assert
            Assert.Equal(eventType, mappedBackEventType);
        }
    }

    [Fact]
    public void Hospital_TRZEventTypes_Mapping_Should_Be_Bidirectional_Consistent()
    {
        // Arrange - Test all hospital-related TRZ event types
        var hospitalTrzEventTypes = new[]
        {
            TRZEventType.ГледанеНаБоленЧлен,
            TRZEventType.НеплатенПоНетрудоспособност,
            TRZEventType.НеплатенЗаБременност,
            TRZEventType.ОтпускМайка135До410,
            TRZEventType.ОтпускБащаНад6Месеца,
            TRZEventType.Отпуск15ДниРаждане,
            TRZEventType.ОтпускЗаДетеДо2,
            TRZEventType.ОтпускОсиновяванеДо5,
            TRZEventType.НеплатенОсиновяванеДо5,
            TRZEventType.БащаОсиновителДо5,
            TRZEventType.БащаГледаДетеДо8
        };

        foreach (var trzEventType in hospitalTrzEventTypes)
        {
            // Act
            var eventType = trzEventType.ToEventType();
            var mappedBackTrzEventType = eventType.ToTRZEventType();

            // Assert
            Assert.Equal(trzEventType, mappedBackTrzEventType);
        }
    }

    [Fact]
    public void IsHospital_Should_Handle_Invalid_EventType_Values()
    {
        // Arrange
        var invalidEventType = (EventType)999999;

        // Act
        var result = invalidEventType.IsHospital();

        // Assert
        Assert.False(result);
    }

    [Fact]
    public void IsHospital_Should_Handle_Invalid_TRZEventType_Values()
    {
        // Arrange
        var invalidTrzEventType = (TRZEventType)999999;

        // Act
        var result = invalidTrzEventType.IsHospital();

        // Assert
        Assert.False(result);
    }

    [Fact]
    public void ToTRZEventType_Should_Return_Default_For_Invalid_Hospital_EventType()
    {
        // Arrange
        var invalidEventType = (EventType)999999;

        // Act
        var result = invalidEventType.ToTRZEventType();

        // Assert
        Assert.Equal(TRZEventType.ПлатенГодишенОтпуск, result);
    }

    [Fact]
    public void ToEventType_Should_Return_Default_For_Invalid_Hospital_TRZEventType()
    {
        // Arrange
        var invalidTrzEventType = (TRZEventType)999999;

        // Act
        var result = invalidTrzEventType.ToEventType();

        // Assert
        Assert.Equal(EventType.ПлатенГодишенОтпуск, result);
    }

    [Fact]
    public void All_600_Range_EventTypes_Should_Be_Hospital_Related()
    {
        // Arrange
        var sickLeaveEventTypes = new[]
        {
            EventType.Болничен, // 601
            EventType.ГледанеНаБоленЧленОтСемейството, // 602
            EventType.НеплатенЗаВременнаНеработоспособност, // 605
            EventType.БолниченПоБременност, // 606
            EventType.БолниченСледРаждане, // 607
            EventType.НеплатенЗаБременностИРаждане // 608
        };

        foreach (var eventType in sickLeaveEventTypes)
        {
            // Act & Assert
            Assert.True(eventType.IsHospital(), $"EventType {eventType} ({(int)eventType}) should be hospital-related");
        }
    }

    [Fact]
    public void All_700_Range_EventTypes_Should_Be_Hospital_Related()
    {
        // Arrange
        var maternityLeaveEventTypes = new[]
        {
            EventType.ОтпускЗаМайкаСледРаждане135До410Ден, // 701
            EventType.ОтпускЗаБащаЗаГледанеНаДетеНад6Месеца, // 702
            EventType.ОтпускДо15ДниПриРажданеНаДете, // 703
            EventType.ОтпускЗаОтглежданеНаДетеДо2Години, // 704
            EventType.ОтпускПриОсиновяванеНаДетеДо5ГодишнаВъзраст, // 705
            EventType.НеплатенОтпускПриОсиновяванеНаДетеДо5ГодишнаВъзраст, // 706
            EventType.ОтпускЗаБащаПриОсиновяванеНаДетеДо5ГодишнаВъзраст, // 707
            EventType.ОтпускЗаОтглежданеНаДетеДо8ГодишнаВъзрастОтБащата // 708
        };

        foreach (var eventType in maternityLeaveEventTypes)
        {
            // Act & Assert
            Assert.True(eventType.IsHospital(), $"EventType {eventType} ({(int)eventType}) should be hospital-related");
        }
    }

    [Fact]
    public void All_Hospital_TRZEventTypes_Should_Have_Valid_IDs_Within_1_26_Range()
    {
        // Arrange
        var hospitalTrzEventTypes = new[]
        {
            TRZEventType.Болничен, // 9
            TRZEventType.ГледанеНаБоленЧлен, // 10
            TRZEventType.ТрудоваЗлополука, // 11
            TRZEventType.ПрофесионалнаБолест, // 12
            TRZEventType.НеплатенПоНетрудоспособност, // 13
            TRZEventType.БолниченПоБременност, // 14
            TRZEventType.БолниченСледРаждане, // 15
            TRZEventType.НеплатенЗаБременност, // 16
            TRZEventType.ОтпускМайка135До410, // 17
            TRZEventType.ОтпускБащаНад6Месеца, // 18
            TRZEventType.Отпуск15ДниРаждане, // 19
            TRZEventType.ОтпускЗаДетеДо2, // 20
            TRZEventType.ОтпускОсиновяванеДо5, // 22
            TRZEventType.НеплатенОсиновяванеДо5, // 23
            TRZEventType.БащаОсиновителДо5, // 24
            TRZEventType.БащаГледаДетеДо8 // 26
        };

        foreach (var trzEventType in hospitalTrzEventTypes)
        {
            // Act & Assert
            var id = (int)trzEventType;
            Assert.True(id >= 1 && id <= 26, $"TRZEventType {trzEventType} has ID {id} which should be between 1-26");
            Assert.True(trzEventType.IsHospital(), $"TRZEventType {trzEventType} should be hospital-related");
        }
    }
}
