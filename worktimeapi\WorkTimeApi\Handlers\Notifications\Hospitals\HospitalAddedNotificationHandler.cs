﻿using MediatR;
using WorkTime.Notifications.Services.Interfaces;
using WorkTimeApi.Common;
using WorkTimeApi.Common.Enums;
using WorkTimeApi.Common.Notifications.Hospitals;
using WorkTimeApi.Database.Repositories.Interfaces.Companies;
using WorkTimeApi.Services.Interfaces.Employees;
using WorkTimeApi.Services.Interfaces.Notifications;

namespace WorkTimeApi.Handlers.Notifications.Hospitals
{
    public class HospitalAddedNotificationHandler(ISignalRNotificationService signalRNotificationService,
        INotificationsService notificationsService,
        IEmailsNotificationService emailsNotificationService,
        IEmployeesService employeesService,
        ICompaniesRepository companiesRepository,
        IConfiguration configuration) : INotificationHandler<HospitalsAddedNotification>
    {
        public async Task Handle(HospitalsAddedNotification notification, CancellationToken cancellationToken)
        {
            var employee = await employeesService.GetUserEmployeeAsync(notification.UserId, notification.CompanyId)
                           ?? throw new Exception("Невалиден служител!");

            var employeesToNotify = await notificationsService.GetEmployeesToNotifyAsync(
                notification.CompanyId,
                NotificationsName.Hospitals.AddedByEmployee.Email
            );

            var hospital = notification.Payload.FirstOrDefault() ?? throw new Exception("Липсва информация за добавеното отсъствие!");

            var company = await companiesRepository.GetByIdAsync(notification.CompanyId) ?? throw new Exception("Невалидна компания!");

            var fullName = string.Join(" ", new[] { employee.FirstName, employee.LastName }
                .Where(name => !string.IsNullOrWhiteSpace(name)));

            var worktimeUrl = configuration.GetValue<string>("WorkTimeUrl") ?? throw new Exception("Липсва конфигурация за WorkTimeUrl!");

            var emailRequest = new
            {
                Name = fullName,
                CompanyName = company.Name,
                AbsenceType = hospital.TypeIdentifier.GetDescription(),
                StartDate = hospital.FromDate.ToString("dd.MM.yyyy"),
                EndDate = hospital.ToDate.ToString("dd.MM.yyyy"),
                Comment = hospital.Reference,
                Url = $"{worktimeUrl}{notification.Url}"
            };

            var emails = employeesToNotify
                .Select(e => e.Email)
                .Where(e => !string.IsNullOrWhiteSpace(e))
                .ToList();

            var emailNotifications = emailsNotificationService.SendEmailsAsync(emails, "absences/added", emailRequest);

            var notificationTasks = employeesToNotify
                .Select(async emp =>
                {
                    var savedNotification = await notificationsService.AddNotificationByUserIdAndCompanyIdAsync(emp.WorkTimeId, notification);
                    await signalRNotificationService.NotifyUser(emp.UserId, savedNotification);
                });

            await Task.WhenAll(notificationTasks.Append(emailNotifications));
        }
    }
}