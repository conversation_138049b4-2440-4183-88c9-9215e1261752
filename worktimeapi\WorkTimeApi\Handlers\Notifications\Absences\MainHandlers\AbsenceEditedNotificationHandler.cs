using MediatR;
using WorkTime.Notifications.Services.Interfaces;
using WorkTimeApi.Common.Enums;
using WorkTimeApi.Common.Notifications.Absences;

namespace WorkTimeApi.Handlers.Notifications.Absences.MainHandlers
{
    public class AbsenceEditedNotificationHandler(
        ISignalRNotificationService signalRNotificationService,
        IMediator mediator)
        : INotificationHandler<AbsenceEditNotification>
    {
        public async Task Handle(AbsenceEditNotification notification, CancellationToken cancellationToken)
        {
            var tasks = new List<Task>();

            switch (notification.Payload.Status)
            {
                case AbsenceStatus.DeletedByUserAfterApproval:
                    var notificationDeleteByEmployee = new AbsenceEditedByEmployeeNotification(notification.Payload, notification.CompanyId, notification.UserId, notification.IsActiveAbsence, notification.CreatorName);
                    await mediator.Publish(notificationDeleteByEmployee, cancellationToken);
                    break;
                case AbsenceStatus.DeletedByAdmin:
                    var notificationDeleteByAdmin = new AbsenceDeletedByAdminNotification(notification.Payload, notification.CompanyId, notification.UserId, notification.CreatorName, notification.EmployeeId);
                    await mediator.Publish(notificationDeleteByAdmin, cancellationToken);
                    break;
                case AbsenceStatus.Deleted:
                    var notificationDelete = new AbsenceDeletedNotification(notification.Payload, notification.CompanyId, notification.UserId, notification.CreatorName);
                    await mediator.Publish(notificationDelete, cancellationToken);
                    break;
                case AbsenceStatus.Approved:
                    if (notification.IsActiveAbsence)
                    {
                        var adminsNotification = new AbsenceConfirmedEditedByEmployeeNotification(notification.Payload, notification.OldAbsence, notification.CompanyId, notification.UserId, notification.CreatorName);
                        await mediator.Publish(adminsNotification, cancellationToken);
                    }
                    else
                    {
                        var notificationConfirm = new AbsenceConfirmNotification(notification.Payload, notification.CompanyId, notification.UserId, notification.CreatorName);
                        await mediator.Publish(notificationConfirm, cancellationToken);
                    }

                    break;
                case AbsenceStatus.Declined:
                    var notificationDeclined = new AbsenceDeclinedNotification(notification.Payload, notification.CompanyId, notification.UserId, notification.CreatorName);
                    await mediator.Publish(notificationDeclined, cancellationToken);
                    break;
                case AbsenceStatus.EditedByAdmin:
                    var notificationEditedByAdmin = new AbsenceEditedByAdminNotification(notification.Payload, notification.OldAbsence, notification.CompanyId, notification.UserId, notification.CreatorName);
                    await mediator.Publish(notificationEditedByAdmin, cancellationToken);
                    break;
                case AbsenceStatus.EditedByEmployee:
                    var notificationEditedByEmployee = new AbsenceEditedByEmployeeNotification(notification.Payload, notification.CompanyId, notification.UserId, notification.IsActiveAbsence, notification.CreatorName);
                    await mediator.Publish(notificationEditedByEmployee, cancellationToken);
                    break;

                default:
                    var pushNotifications = signalRNotificationService.BroadcastAsync(notification);
                    tasks.Add(pushNotifications);
                    break;
            }

            await Task.WhenAll(tasks);
        }
    }
}