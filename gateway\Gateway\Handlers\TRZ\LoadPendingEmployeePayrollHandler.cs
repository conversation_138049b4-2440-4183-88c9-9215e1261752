﻿using Gateway.Common.Results;
using Gateway.Connections.Interfaces;
using MediatR;
using WorkTimeApi.Common.Requests.TRZ;

namespace Gateway.Handlers.TRZ
{
    public class LoadPendingEmployeePayrollHandler : IRequestHandler<LoadPendingEmployeePayrollRequest, IResult>
    {
        private readonly IWorkTimeApiConnection _workTimeApiConnection;

        public LoadPendingEmployeePayrollHandler(IWorkTimeApiConnection workTimeApiConnection)
        {
            _workTimeApiConnection = workTimeApiConnection;
        }

        public async Task<IResult> Handle(LoadPendingEmployeePayrollRequest request, CancellationToken cancellationToken)
        {
            var response = await _workTimeApiConnection.LoadPendingEmployeePayrollListAsync(request);

            return new HttpResponseMessageResult(response);
        }
    }
}
