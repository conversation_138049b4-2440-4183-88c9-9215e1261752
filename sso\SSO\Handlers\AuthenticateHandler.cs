﻿using MediatR;
using SSO.Common.DTOs;
using SSO.Common.Requests;
using SSO.Services.Interfaces;
using System.Security.Claims;

namespace SSO.Handlers
{
    public class AuthenticateHandler : IRequestHandler<AuthenticateRequest, IResult>
    {
        private readonly IPrincipalExtensionsWrapper _principalExtensionsWrapper;

        public AuthenticateHandler(IPrincipalExtensionsWrapper principalExtensionsWrapper)
        {
            _principalExtensionsWrapper = principalExtensionsWrapper;
        }

        public Task<IResult> Handle(AuthenticateRequest request, CancellationToken cancellationToken)
        {
            if (request.HttpRequest is null)
                return Task.FromResult(Results.BadRequest());

            var user = request.HttpRequest.HttpContext.User;
            var userId = new Guid(_principalExtensionsWrapper.FindFirstValue(user, "UserId") ?? "");
            var email = _principalExtensionsWrapper.FindFirstValue(user, ClaimTypes.Email) ?? "";
            return Task.FromResult(Results.Ok(new UserDTO { Id = userId, Email = email }));
        }
    }
}
