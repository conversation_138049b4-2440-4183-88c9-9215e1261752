import { useContext } from "react";
import { DropdownContext } from "./Context";

interface HeaderProps
  extends React.DetailedHTMLProps<
    React.AllHTMLAttributes<HTMLDivElement>,
    HTMLDivElement
  > {
  children: React.ReactNode | React.ReactNode[];
}

export const Header = (props: HeaderProps) => {
  const { children } = props;
  const { setIsOpen } = useContext(DropdownContext);

  const openDropdown = () => setIsOpen(true);

  return (
    <div {...props} onClick={openDropdown}>
      {children}
    </div>
  );
};
