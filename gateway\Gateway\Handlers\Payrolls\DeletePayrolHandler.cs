﻿using Gateway.Connections.Interfaces;
using MediatR;
using WorkTimeApi.Common.Requests.Payrolls;

namespace Gateway.Handlers.Payrolls
{
    public class DeletePayrolHandler : IRequestHandler<DeletePayrollRequest, IResult>
	{
		private readonly IWorkTimeApiConnection _workTimeApiConnection;

		public DeletePayrolHandler(IWorkTimeApiConnection workTimeApiConnection)
		{
			_workTimeApiConnection = workTimeApiConnection;
		}

		public async Task<IResult> Handle(DeletePayrollRequest request, CancellationToken cancellationToken)
		{
			var response = await _workTimeApiConnection.DeletePayrollAsync(request);

			if (!response.IsSuccessStatusCode)
				return Results.StatusCode((int)response.StatusCode);

			return Results.Text(await response.Content.ReadAsStringAsync(cancellationToken));
		}
	}
}
