import styled from "styled-components";

interface ContainerProps
  extends React.DetailedHTMLProps<
    React.AllHTMLAttributes<HTMLDivElement>,
    HTMLDivElement
  > {
  ref?: React.Ref<HTMLDivElement>;
  as?: undefined;
  children?: React.ReactNode | React.ReactNode[];
}

const ContainerDiv = styled.div<ContainerProps>``;

const Container = (props: ContainerProps) => {
  const { children } = props;
  return <ContainerDiv {...props}>{children}</ContainerDiv>;
};

export default Container;
