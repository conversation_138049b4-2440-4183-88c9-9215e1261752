﻿using Gateway.Connections.Interfaces;
using MediatR;
using WorkTimeApi.Common.Requests.Nomenclatures;

namespace Gateway.Handlers.Nomenclatures
{
    public class LoadNKPDsHandler : IRequestHandler<GetNKPDsRequest, IResult>
    {
        private readonly IWorkTimeApiConnection _workTimeApiConnection;

        public LoadNKPDsHandler(IWorkTimeApiConnection workTimeApiConnection)
        {
            _workTimeApiConnection = workTimeApiConnection;
        }

        public async Task<IResult> Handle(GetNKPDsRequest request, CancellationToken cancellationToken)
        {
            var response = await _workTimeApiConnection.GetNKPDsAsync();

            if (!response.IsSuccessStatusCode)
                return Results.StatusCode((int)response.StatusCode);

            return Results.Text(await response.Content.ReadAsStringAsync(cancellationToken));
        }
    }
}
