import { useContext } from "react";
import { UserNavMenuContainer } from "../../components/Layout/UserNavMenuContainer";
import { AuthContext } from "./AuthContext";
import LoggedNavMenu from "./LoggedNavMenu";
import NotLoggedNavMenu from "./NotLoggedNavMenu";

const UserNavMenu = () => {
  const { user } = useContext(AuthContext);

  return (
    <UserNavMenuContainer>
      {user.email ? (
        <LoggedNavMenu />
      ) : (
        <NotLoggedNavMenu />
      )}
    </UserNavMenuContainer>
  );
};

export default UserNavMenu;
