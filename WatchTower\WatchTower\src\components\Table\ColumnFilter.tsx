import React, { ChangeEvent, useEffect, useState } from "react";
import { ColumnDefinitionType } from "../../components/Table/Table";
import styled from "styled-components";
import searchImage from "../../assets/images/table/search.png";
import searchHoverImage from "../../assets/images/table/searchHover.png";
import sortImage from "../../assets/images/table/sort.png";
import sortHoverImage from "../../assets/images/table/sortHover.png";
import sortTable from "./sortTable";

interface ColumnFilterProps<T, K extends keyof T>
  extends React.DetailedHTMLProps<
    React.TableHTMLAttributes<HTMLTableElement>,
    HTMLTableElement
  > {
  showSort: boolean;
  column: ColumnDefinitionType<T, K>;
  data: T[];
  setTableData: (tableData: T[]) => void;
  filterColumns(columnKey: string, searchText: string): void;
}

const SearchInput = styled.input`
  text-align: left;
  opacity: 1;
  font-family: segoe-ui;
  font-style: normal;
  border-radius: 1.625rem;
  padding: 0.625rem;
  border: 0.063rem;
  &:focus {
    outline: none;
    box-shadow: 0rem 0rem 0.125rem var(--search-input-box-shadow-color);
  }
`;

const SortInput = styled.input`
  background-color: var(--sort-input-color);
  opacity: 1;
  font-family: segoe-ui;
  font-style: normal;
  border-radius: 1.625rem;
  padding: 0.625rem;
  border: none;
`;

const SearchButton = styled.button`
  background-image: url(${searchImage});
  background-color: transparent;
  background-size: cover;
  width: 1.25rem;
  height: 1.25rem;
  text-indent: -999px;
  overflow: hidden;
  border: none;
  position: absolute;
  margin-top: 0.5rem;
  margin-left: -1.875rem;
  cursor: pointer;
  &:hover {
    background-image: url(${searchHoverImage});
  }
`;

const SortButton = styled.button`
  background-image: url(${sortImage});
  background-color: transparent;
  background-size: cover;
  width: 1rem;
  height: 1rem;
  text-indent: -999px;
  overflow: hidden;
  border: none;
  position: absolute;
  margin-top: 0.5rem;
  margin-left: -1.875rem;
  cursor: pointer;

  &:hover {
    background-image: url(${sortHoverImage});
  }
`;

const SearchDiv = styled.div`
  margin: 0.313rem;
`;

export enum Direction {
  Ascending = -1,
  Descending = 1,
}

const ColumnFilter = <T, K extends keyof T>({
  showSort,
  column,
  data,
  setTableData,
  filterColumns,
}: ColumnFilterProps<T, K>): JSX.Element => {
  const [search, setSearch] = useState("");
  const [direction, setDirection] = useState(Direction.Ascending);

  useEffect(() => {
    setSearch("");
  }, [showSort]);

  useEffect(() => {
    filterColumns(column.key as string, search);
  }, [search]);

  const onSearchChange = (e: ChangeEvent<HTMLInputElement>) => {
    setSearch(e.target.value);
  };

  const handleSort = () => {
    setDirection(direction * -1);
    let sortedData = sortTable(data, column.key as string, direction);
    setTableData(sortedData);
  };

  return (
    <SearchDiv>
      {!showSort ? (
        <>
          <SearchInput
            placeholder={column.value}
            value={search}
            onChange={onSearchChange}
          />
          <SearchButton />
        </>
      ) : (
        <>
          <SortInput placeholder={column.value} disabled={true} />
          <SortButton onClick={handleSort} />
        </>
      )}
    </SearchDiv>
  );
};
export default ColumnFilter;
