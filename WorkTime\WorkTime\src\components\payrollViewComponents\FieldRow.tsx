import React from "react";
import styled from "styled-components";
import Container from "../Container";
import Label from "../Inputs/Label";

const FieldsetRow = styled(Container)`
  display: flex;
  flex-direction: row;
  align-items: center;
  margin: 0.2rem;
  width: 100%;
`;

const LabelColumn = styled(Container)`
  display: flex;
  align-items: center;
  width: 45%;
`;

const ValueColumn = styled(Container)`
  display: flex;
  align-items: center;
  width: 60%;
`;

const LightLabel = styled(Label)`
  color: var(--profile-department-leader-name-font-color);
  font-size: 0.9rem;
`;

const ValueLabel = styled(Label)`
  font-size: 1rem;
`;

const EmptyRow = styled.div`
  height: 1.5rem;
  width: 100%;
`;

const ThreeDotsIcon = styled.div`
  width: 1.2rem;
  height: 1.2rem;
  border-radius: 50%;
  background-color: var(--profile-left-part-background-color);
  border: 1px solid var(--addresses-delete-button-color-hover);
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: all 0.3s ease;

  &:hover {
    background-color: var(--addresses-delete-button-color-hover);
    transform: scale(1.1);
  }

  &::before {
    content: "⋯";
    font-size: 0.8rem;
    color: black;
  }

  &:hover::before {
    color: white;
  }
`;

interface FieldRowProps {
  label: string;
  value: any;
  includeEmptyRow?: boolean;
  showThreeDotsIcon?: boolean;
  onThreeDotsClick?: () => void;
}

const FieldRow: React.FC<FieldRowProps> = ({
  label,
  value,
  includeEmptyRow = false,
  showThreeDotsIcon = false,
  onThreeDotsClick,
}) => {
  return (
    <>
      <FieldsetRow>
        <LabelColumn>
          <LightLabel>{label}</LightLabel>
        </LabelColumn>
        <ValueColumn>
          <ValueLabel>{value || ""}</ValueLabel>
        </ValueColumn>
        {showThreeDotsIcon && (
          <ThreeDotsIcon
            onClick={onThreeDotsClick}
            data-testid="additional-terms-icon"
          />
        )}
      </FieldsetRow>
      {includeEmptyRow && <EmptyRow />}
    </>
  );
};

export default FieldRow;
