import { AnnexPayrollSummaryDTO } from "../models/DTOs/payrolls/AnnexPayrollSummaryDTO";
import { PayrollDataDTO } from "../models/DTOs/payrolls/PayrollDataDTO";
import { PayrollSummaryDTO } from "../models/DTOs/payrolls/PayrollSummaryDTO";

export const mapAnnexPayrollToPayrollData = (
  annexPayroll: AnnexPayrollSummaryDTO
): PayrollDataDTO => {
  return {
    contractType: annexPayroll.contractType,
    dailyWorktime: annexPayroll.dailyWorktime,
    structureLevelId: annexPayroll.structureLevelId,
    structureLevelName: annexPayroll.structureLevelName,
    workplace: annexPayroll.workplace,
    contractReason: annexPayroll.contractReason,
    additionalTerms: "",
    fromDate: annexPayroll.fromDate,
    annexFromDate: annexPayroll.fromDate,
    professionalЕxperienceInCompany: undefined,
    professionalЕxperience: undefined,
    workExperience: undefined,
    contractNumber: annexPayroll.contractNumber,
    annexPayrollNumber: annexPayroll.annexPayrollNumber,
    kid: annexPayroll.kid,
    ekatte: annexPayroll.ekatte,
    nkpd: annexPayroll.nkpd,
    position: annexPayroll.position,
    toDate: annexPayroll.toDate,
    contractTermDate: annexPayroll.contractTermDate,
    contractDate: annexPayroll.contractDate,
    contractEndDate: annexPayroll.contractEndDate,
    contractTerminationDate: annexPayroll.contractTerminationDate,
  };
};

export const mapPayrollSummaryToPayrollData = (
  payrollSummary: PayrollSummaryDTO
): PayrollDataDTO => {
  return {
    contractType: payrollSummary.contractType,
    dailyWorktime: payrollSummary.dailyWorktime,
    structureLevelId: payrollSummary.structureLevelId,
    structureLevelName: payrollSummary.structureLevelName,
    workplace: payrollSummary.workplace,
    contractReason: payrollSummary.contractReason,
    additionalTerms: payrollSummary.additionalTerms,
    fromDate: payrollSummary.fromDate,
    annexFromDate: payrollSummary.fromDate,
    professionalЕxperienceInCompany:
      payrollSummary.professionalЕxperienceInCompany,
    professionalЕxperience: payrollSummary.professionalЕxperience,
    workExperience: payrollSummary.workExperience,
    contractNumber: payrollSummary.contractNumber,
    annexPayrollNumber: undefined,
    kid: payrollSummary.kid,
    ekatte: payrollSummary.ekatte,
    nkpd: payrollSummary.nkpd,
    position: payrollSummary.position,
    toDate: payrollSummary.toDate,
    contractTermDate: payrollSummary.contractTermDate,
    contractDate: payrollSummary.contractDate,
    contractEndDate: payrollSummary.contractEndDate,
    contractTerminationDate: payrollSummary.contractTerminationDate,
  };
};

export const mapPayrollAndAnnexToPayrollData = (
  payrollSummary: PayrollSummaryDTO
): PayrollDataDTO => {
  const lastActiveAnnex =
    payrollSummary.annexPayrolls && payrollSummary.annexPayrolls.length > 0
      ? payrollSummary.annexPayrolls?.reduce((max, cur) =>
          Number(cur.annexPayrollNumber ?? 0) >
          Number(max.annexPayrollNumber ?? 0)
            ? cur
            : max
        )
      : undefined;
  return {
    contractType: payrollSummary.contractType,
    dailyWorktime:
      lastActiveAnnex?.dailyWorktime ?? payrollSummary.dailyWorktime,
    structureLevelId:
      lastActiveAnnex?.structureLevelId ?? payrollSummary.structureLevelId,
    structureLevelName:
      lastActiveAnnex?.structureLevelName ?? payrollSummary.structureLevelName,
    workplace: lastActiveAnnex?.workplace ?? payrollSummary.workplace,
    contractReason:
      lastActiveAnnex?.contractReason ?? payrollSummary.contractReason,
    additionalTerms: payrollSummary.additionalTerms,
    fromDate: payrollSummary.fromDate,
    annexFromDate: lastActiveAnnex?.fromDate ?? payrollSummary.fromDate,
    professionalЕxperienceInCompany:
      payrollSummary.professionalЕxperienceInCompany,
    professionalЕxperience: payrollSummary.professionalЕxperience,
    workExperience: payrollSummary.workExperience,
    contractNumber: payrollSummary.contractNumber,
    annexPayrollNumber: lastActiveAnnex?.annexPayrollNumber ?? undefined,
    kid: "",
    ekatte: "",
    nkpd: "",
    position: lastActiveAnnex?.position ?? payrollSummary.position,
    toDate: lastActiveAnnex?.toDate ?? payrollSummary.toDate,
    contractTermDate:
      lastActiveAnnex?.contractTermDate ?? payrollSummary.contractTermDate,
    contractDate: lastActiveAnnex?.contractDate ?? payrollSummary.contractDate,
    contractEndDate:
      lastActiveAnnex?.contractEndDate ?? payrollSummary.contractEndDate,
    contractTerminationDate:
      lastActiveAnnex?.contractTerminationDate ??
      payrollSummary.contractTerminationDate,
  };
};
