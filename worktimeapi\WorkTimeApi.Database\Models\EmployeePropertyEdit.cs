using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using WorkTimeApi.Common.Enums;

namespace WorkTimeApi.Database.Models
{
    public class EmployeePropertyEdit
    {
        [Key]
        [DatabaseGenerated(DatabaseGeneratedOption.Identity)]
        public Guid Id { get; set; }

        public EditSource EditSource { get; set; }

        public EditStatus EditStatus { get; set; }

        public Guid EditorId { get; set; }

        public Employee Employee { get; set; }

        public Guid EmployeeId { get; set; }

        public required string ObjectName { get; set; }

        public Guid ObjectId { get; set; }

        public required string PropertyName { get; set; }

        public string? OldValue { get; set; }

        public string? NewValue { get; set; }

        public DateTime CreatedAt { get; set; } = DateTime.UtcNow;

        public DateTime UpdatedAt { get; set; } = DateTime.UtcNow;
    }
}
