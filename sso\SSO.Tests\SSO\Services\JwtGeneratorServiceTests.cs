﻿using Microsoft.Extensions.Configuration;
using Moq;
using SSO.Common.DTOs;
using SSO.Services;
using SSO.Services.Interfaces;
using System.Security.Claims;

namespace SSO.Tests.SSO.Services
{
    public class JwtGeneratorServiceTests
    {
        private const string USER_GUID_STR = "2da630af-1f0a-4297-9798-eabf2b02b124";

        [Fact]
        public async Task GenerateAccessTokenReturnsValidToken()
        {
            // Arrange
            var inMemorySettings = new Dictionary<string, string?> {
                {"JwtSecret", "0123456789ABCDEF0123456789ABCDEF0123456789ABCDEF0123456789ABCDEF"}, // 32 bytes (256 bits)
                {"JwtIssuer", "Test"},
                {"JwtAudience", "All"},
            };

            var configuration = new ConfigurationBuilder()
                .AddInMemoryCollection(inMemorySettings)
                .Build();

            var user = new UserDTO
            {
                Id = Guid.NewGuid(),
                Email = "<EMAIL>",
                FirstName = "Test",
                LastName = "Testov",
                SecondName = "Testov",
            };

            var usersServiceMock = new Mock<IUsersService>();
            usersServiceMock.Setup(us => us.GetUserPermissionsAsync(It.IsAny<Guid>())).ReturnsAsync(new List<UserPermissionsDTO>());

            IJwtGeneratorService jwtGeneratorService = new JwtGeneratorService(configuration, usersServiceMock.Object);
            IJWTValidator jWTValidator = new JWTValidator(configuration);

            // Act
            var token = await jwtGeneratorService.GenerateAccessTokenAsync(user);
            var claimsPrincipal = jWTValidator.ValidateToken(token);

            // Assert
            Assert.NotNull(claimsPrincipal);
            Assert.NotNull(claimsPrincipal.Claims.FirstOrDefault(c => c.Type == ClaimTypes.Email));
            Assert.Equal(user.Email, claimsPrincipal.Claims.FirstOrDefault(c => c.Type == ClaimTypes.Email)!.Value);
        }

        [Fact]
        public async Task GenerateRefreshTokenReturnsValidToken()
        {
            // Arrange
            var inMemorySettings = new Dictionary<string, string?> {
                {"JwtSecret", "0123456789ABCDEF0123456789ABCDEF0123456789ABCDEF0123456789ABCDEF"}, // 32 bytes (256 bits)
                {"JwtIssuer", "Test"},
                {"JwtAudience", "All"},
            };

            var configuration = new ConfigurationBuilder()
                .AddInMemoryCollection(inMemorySettings)
                .Build();

            var user = new UserDTO
            {
                Id = Guid.NewGuid(),
                Email = "<EMAIL>",
                FirstName = "Test",
                LastName = "Testov",
                SecondName = "Testov",
            };

            var usersServiceMock = new Mock<IUsersService>();
            usersServiceMock.Setup(us => us.GetUserPermissionsAsync(It.IsAny<Guid>())).ReturnsAsync(new List<UserPermissionsDTO>());

            IJwtGeneratorService jwtGeneratorService = new JwtGeneratorService(configuration, usersServiceMock.Object);
            IJWTValidator jWTValidator = new JWTValidator(configuration);

            // Act
            var token = await jwtGeneratorService.GenerateRefreshTokenAsync(user);
            var claimsPrincipal = jWTValidator.ValidateToken(token);

            // Assert
            Assert.NotNull(claimsPrincipal);
            Assert.NotNull(claimsPrincipal.Claims.FirstOrDefault(c => c.Type == ClaimTypes.Email));
            Assert.Equal(user.Email, claimsPrincipal.Claims.FirstOrDefault(c => c.Type == ClaimTypes.Email)!.Value);
        }

        [Fact]
        public async Task GenerateAccessTokenWithClaimsReturnsValidToken()
        {
            // Arrange
            var inMemorySettings = new Dictionary<string, string?> {
                {"JwtSecret", "0123456789ABCDEF0123456789ABCDEF0123456789ABCDEF0123456789ABCDEF"}, // 32 bytes (256 bits)
                {"JwtIssuer", "Test"},
                {"JwtAudience", "All"},
            };

            var configuration = new ConfigurationBuilder()
                .AddInMemoryCollection(inMemorySettings)
                .Build();

            var email = "<EMAIL>";
            var claims = new List<Claim>
            {
                new Claim(ClaimTypes.Email, email),
                new Claim("UserId", USER_GUID_STR),
            };

            var usersServiceMock = new Mock<IUsersService>();
            usersServiceMock.Setup(us => us.GetUserPermissionsAsync(It.IsAny<Guid>())).ReturnsAsync(new List<UserPermissionsDTO>());

            IJwtGeneratorService jwtGeneratorService = new JwtGeneratorService(configuration, usersServiceMock.Object);
            IJWTValidator jWTValidator = new JWTValidator(configuration);

            // Act
            var token = await jwtGeneratorService.GenerateAccessTokenAsync(claims);
            var claimsPrincipal = jWTValidator.ValidateToken(token);

            // Assert
            Assert.NotNull(claimsPrincipal);
            Assert.NotNull(claimsPrincipal.Claims.FirstOrDefault(c => c.Type == ClaimTypes.Email));
            Assert.Equal(email, claimsPrincipal.Claims.FirstOrDefault(c => c.Type == ClaimTypes.Email)!.Value);
            Assert.NotNull(claimsPrincipal.Claims.FirstOrDefault(c => c.Type == "UserId"));
            Assert.Equal(USER_GUID_STR, claimsPrincipal.Claims.FirstOrDefault(c => c.Type == "UserId")!.Value);
        }

        [Fact]
        public async Task GenerateRefreshTokenWithClaimsReturnsValidToken()
        {
            // Arrange
            var inMemorySettings = new Dictionary<string, string?> {
                {"JwtSecret", "0123456789ABCDEF0123456789ABCDEF0123456789ABCDEF0123456789ABCDEF"}, // 32 bytes (256 bits)
                {"JwtIssuer", "Test"},
                {"JwtAudience", "All"},
            };

            var configuration = new ConfigurationBuilder()
                .AddInMemoryCollection(inMemorySettings)
                .Build();

            var email = "<EMAIL>";
            var claims = new List<Claim>
            {
                new Claim(ClaimTypes.Email, email),
                new Claim("UserId", USER_GUID_STR),
            };

            var usersServiceMock = new Mock<IUsersService>();
            usersServiceMock.Setup(us => us.GetUserPermissionsAsync(It.IsAny<Guid>())).ReturnsAsync(new List<UserPermissionsDTO>());

            IJwtGeneratorService jwtGeneratorService = new JwtGeneratorService(configuration, usersServiceMock.Object);
            IJWTValidator jWTValidator = new JWTValidator(configuration);

            // Act
            var token = await jwtGeneratorService.GenerateRefreshTokenAsync(claims);
            var claimsPrincipal = jWTValidator.ValidateToken(token);

            // Assert
            Assert.NotNull(claimsPrincipal);
            Assert.NotNull(claimsPrincipal.Claims.FirstOrDefault(c => c.Type == ClaimTypes.Email));
            Assert.Equal(email, claimsPrincipal.Claims.FirstOrDefault(c => c.Type == ClaimTypes.Email)!.Value);
            Assert.NotNull(claimsPrincipal.Claims.FirstOrDefault(c => c.Type == "UserId"));
            Assert.Equal(USER_GUID_STR, claimsPrincipal.Claims.FirstOrDefault(c => c.Type == "UserId")!.Value);
        }
    }
}
