﻿using Gateway.Common.Globals;
using MediatR;
using WorkTimeApi.Common;
using WorkTimeApi.Common.Notifications.EditEmployee;
using WorkTimeApi.Common.Requests.Employees;
using WorkTimeApi.Services.Interfaces.Employees;

namespace WorkTimeApi.Handlers.Employees
{
    public class ApproveEmployeePropertyEditHandler : IRequestHandler<ApproveEmployeePropertyEditRequest, IResult>
    {
        private readonly IEmployeesService _employeesService;
        private readonly GlobalEmployee _globalEmployee;
        private readonly GlobalUser _globalUser;
        private readonly IMediator _mediator;

        public ApproveEmployeePropertyEditHandler(IEmployeesService employeesService, GlobalEmployee globalEmployee, GlobalUser globalUser, IMediator mediator)
        {
            _employeesService = employeesService;
            _globalEmployee = globalEmployee;
            _globalUser = globalUser;
            _mediator = mediator;
        }

        public async Task<IResult> Handle(ApproveEmployeePropertyEditRequest request, CancellationToken cancellationToken)
        {
            if (!_globalEmployee.Permissions.Contains(DefaultPermissions.Employees.Write))
                return Results.Unauthorized();

            await _employeesService.ApproveEmployeePropertyEditAsync(request);
            var employee = await _employeesService.GetEmployeeAsync(_globalEmployee.Id);

            var notificationConfirm = new EditEmployeeConfirmNotification(
                employee,
                request.PayrollId,
                employee.CompanyId,
                _globalUser.Id,
                $"{_globalUser.FirstName} {_globalUser.LastName}",
                request.TabName);
            await _mediator.Publish(notificationConfirm, cancellationToken);

            return Results.Ok();
        }
    }
}
