{"name": "watchtower", "private": true, "version": "0.0.0", "type": "module", "scripts": {"start": "vite --port 3001", "dev": "vite", "build": "vite build", "lint": "eslint . --ext ts,tsx --report-unused-disable-directives --max-warnings 0", "preview": "vite preview"}, "dependencies": {"@azure/msal-browser": "^3.3.0", "@fortawesome/fontawesome-svg-core": "^6.4.2", "@fortawesome/free-solid-svg-icons": "^6.4.2", "@fortawesome/react-fontawesome": "^0.2.0", "@microsoft/signalr": "^7.0.12", "@react-oauth/google": "^0.11.1", "@reduxjs/toolkit": "^1.9.7", "bootstrap": "^5.3.2", "react": "^18.2.0", "react-bootstrap": "^2.9.1", "react-dom": "^18.2.0", "react-google-recaptcha": "^3.1.0", "react-redux": "^8.1.3", "react-router-dom": "^6.17.0", "react-toastify": "^9.1.3", "styled-components": "^6.1.0"}, "devDependencies": {"@types/react": "^18.2.15", "@types/react-dom": "^18.2.7", "@types/react-google-recaptcha": "^2.1.7", "@typescript-eslint/eslint-plugin": "^6.0.0", "@typescript-eslint/parser": "^6.0.0", "@vitejs/plugin-react": "^4.0.3", "eslint": "^8.45.0", "eslint-plugin-react-hooks": "^4.6.0", "eslint-plugin-react-refresh": "^0.4.3", "typescript": "^5.0.2", "vite": "^4.4.5"}}