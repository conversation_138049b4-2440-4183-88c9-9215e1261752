﻿using System.Globalization;
using WorkTimeApi.Database.Services.Interfaces;

namespace WorkTimeApi.Database.Services
{
    public class CalendarService : ICalendarService
    {
        private readonly IHolidayService _holidayService;

        public CalendarService(IHolidayService holidayService)
        {
            _holidayService = holidayService;
        }

        public async Task<int> CalculateEventDuration(DateTime fromDate, DateTime endDate)
        {
            int workingDaysCount = 0;
            var currentDate = fromDate;
            var monthlyHolidaySummary = await _holidayService.GetHolidaysAsync(currentDate.Year, currentDate.Month);
            if (fromDate > endDate)
                return 0;

            for (DateTime date = fromDate; date <= endDate; date = date.AddDays(1))
            {
                if(currentDate.Month < date.Month)
                {
                    currentDate = currentDate.AddMonths(1);
                    monthlyHolidaySummary = await _holidayService.GetHolidaysAsync(currentDate.Year, currentDate.Month);
                }

                if (date.DayOfWeek != DayOfWeek.Saturday && date.DayOfWeek != DayOfWeek.Sunday
                    && (!monthlyHolidaySummary.Any(d=>DateTime.Parse(d.Date,CultureInfo.InvariantCulture) == date && !d.IsWeekend)))
                    workingDaysCount++;
            }

            return workingDaysCount;
        }
    }
}
