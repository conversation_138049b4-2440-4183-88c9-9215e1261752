﻿using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Http.HttpResults;
using Moq;
using SSO.Common.DTOs;
using SSO.Connections.Interfaces;
using SSO.Handlers;
using SSO.Common.Requests;
using SSO.Services.Interfaces;
using System.Text.Json;

namespace SSO.Tests.SSO.Handlers
{
    public class HeaderLoginHandlerTests
    {
        private const string USER_GUID_STR = "4c34a6a8-4b6a-496c-bcc1-5a249d487b91";

        private readonly Mock<IUserRegistrationsConnection> _userRegistrationsConnectionMock;
        private readonly Mock<IJwtGeneratorService> _jwtGeneratorServiceMock;
        private readonly Mock<IRefreshTokensService> _refreshTokensServiceMock;
        private readonly CancellationToken _cancellationToken;

        public HeaderLoginHandlerTests()
        {
            _userRegistrationsConnectionMock = new Mock<IUserRegistrationsConnection>();
            _jwtGeneratorServiceMock = new Mock<IJwtGeneratorService>();
            _refreshTokensServiceMock = new Mock<IRefreshTokensService>();
            _cancellationToken = new CancellationToken();
        }

        [Fact]
        public async Task HandlerReturnsBadRequestIfHttpContextIsNull()
        {
            // Arrange
            var headerLoginHandler = new HeaderLoginHandler(
                _userRegistrationsConnectionMock.Object,
                _jwtGeneratorServiceMock.Object,
                _refreshTokensServiceMock.Object);

            var request = new HeaderLoginRequest
            {
                HttpRequest = null
            };

            // Act
            var result = await headerLoginHandler.Handle(request, _cancellationToken);
            var badRequestResult = (BadRequest)result;

            // Assert
            Assert.True(badRequestResult.StatusCode == 400);
        }

        [Fact]
        public async Task HandlerReturnsBadRequestWhenWrongCredentialsArePassed()
        {
            // Arrange
            var headerLoginHandler = new HeaderLoginHandler(
                _userRegistrationsConnectionMock.Object,
                _jwtGeneratorServiceMock.Object,
                _refreshTokensServiceMock.Object);

            _userRegistrationsConnectionMock
                .Setup(s => s.TryLoginWithResponseAsync(It.IsAny<string>())).ReturnsAsync(new HttpResponseMessage(System.Net.HttpStatusCode.NotFound));

            var requestMock = new Mock<HeaderLoginRequest>();
            var httpRequestMock = new Mock<HttpRequest>();
            httpRequestMock.Setup(hr => hr.Headers).Returns(new Mock<IHeaderDictionary>().Object);
            requestMock.Object.HttpRequest = httpRequestMock.Object;

            // Act
            var result = await headerLoginHandler.Handle(requestMock.Object, _cancellationToken);
            var badRequestResult = (NotFound<string>)result;

            // Assert
            Assert.True(badRequestResult.StatusCode == 404);
            Assert.True(badRequestResult.Value == "Invalid Credentials!");
        }

        [Fact]
        public async Task HandlerReturnsOkRequestWhenValidAuthorizationIsPassed()
        {
            // Arrange
            var user = new UserDTO
            {
                Id = new Guid(USER_GUID_STR),
                Email = "<EMAIL>",
                FirstName = "Test",
                LastName = "Testov",
                SecondName = "Testov",
            };
            var headerLoginHandler = new HeaderLoginHandler(
                _userRegistrationsConnectionMock.Object,
                _jwtGeneratorServiceMock.Object,
                _refreshTokensServiceMock.Object);
            var accessToken = "actualAccessToken";
            var refreshToken = "actualRefreshToken";

            _userRegistrationsConnectionMock
                .Setup(s => s.TryLoginWithResponseAsync(It.IsAny<string>())).ReturnsAsync(new HttpResponseMessage(System.Net.HttpStatusCode.OK) { Content = new StringContent(JsonSerializer.Serialize(user)) });
            _jwtGeneratorServiceMock
                .Setup(jwt => jwt.GenerateAccessTokenAsync(user)).ReturnsAsync(accessToken);
            _jwtGeneratorServiceMock
                .Setup(jwt => jwt.GenerateRefreshTokenAsync(user)).ReturnsAsync(refreshToken);

            var requestMock = new Mock<HeaderLoginRequest>();
            var defaultHttpContext = new DefaultHttpContext();
            requestMock.Object.HttpRequest = defaultHttpContext.Request;

            // Act
            var result = await headerLoginHandler.Handle(requestMock.Object, _cancellationToken);
            var okResult = (Ok<UserDTO>)result;
            var responseUser = okResult?.Value;

            // Assert
            Assert.NotNull(okResult);
            Assert.NotNull(responseUser);
            Assert.True(okResult.StatusCode == 200);
            Assert.Equal(user, responseUser);
            Assert.Equal($"Bearer {accessToken}", requestMock.Object.HttpRequest.HttpContext.Response.Headers["Authorization"].FirstOrDefault());
            Assert.Equal(refreshToken, requestMock.Object.HttpRequest.HttpContext.Response.Headers["Refresh-Token"].FirstOrDefault());
        }
    }
}
