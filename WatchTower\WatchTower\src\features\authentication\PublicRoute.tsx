import { useEffect, useState } from "react";
import { Navigate } from "react-router-dom";
import { isAuthenticated } from "../../services/authentication/authenticationService";

interface Props {
  children: React.ReactNode | React.ReactNode[];
}

const PublicRoute = ({ children }: Props) => {
  const [authenticated, setAuthenticated] = useState(true);
  const [isLoaded, setIsLoaded] = useState(false);

  useEffect(() => {
    const authed = isAuthenticated();
    setAuthenticated(authed);
    setIsLoaded(true);
  }, [setAuthenticated]);
  return (
    <>
      {isLoaded ? (
        authenticated ? (
          <Navigate to={"/"} />
        ) : (
          children
        )
      ) : (
        // Тука може да се сложи някакъв Spinner / Loader etc
        <></>
      )}
    </>
  );
};

export default PublicRoute;
