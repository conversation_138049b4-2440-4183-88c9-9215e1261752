using MediatR;

namespace WorkTimeApi.Infrastructure.MediatR
{
    public sealed class SafeNotificationPublisher(ILogger<SafeNotificationPublisher> logger) : INotificationPublisher
    {
        public async Task Publish(IEnumerable<NotificationHandlerExecutor> handlerExecutors,
            INotification notification,
            CancellationToken cancellationToken)
        {
            foreach (var handlerExecutor in handlerExecutors)
            {
                try
                {
                    await handlerExecutor.HandlerCallback(notification, cancellationToken).ConfigureAwait(false);
                }
                catch (Exception ex)
                {
                    logger.LogError(ex,
                        "Unhandled exception in notification handler {HandlerType} for {NotificationType}.",
                        handlerExecutor.HandlerInstance.GetType().FullName,
                        notification.GetType().FullName);
                }
            }
        }
    }
}

