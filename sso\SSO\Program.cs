﻿using Gateway.Common.Extensions.LoggerExtensions;
using Gateway.Common.Middlewares;
using Gateway.Extenstions.EndpointExtensions;
using Microsoft.EntityFrameworkCore;
using SSO.Database;
using SSO.Extenstions;
using WatchTower.Common.Extensions.Aspire;
using WatchTower.Common.Extensions.HealthCheck;
using WatchTower.Extenstions.MetricsExtensions;

var builder = WebApplication.CreateBuilder(args);

builder.AddServiceDefaults();

builder.Services.AddMediatR(x =>
{
    x.Lifetime = ServiceLifetime.Scoped;
    x.RegisterServicesFromAssembly(typeof(Program).Assembly);
});
builder.Services.AddEndpointDefinitions(typeof(Program));
builder.Services.InitializeAutoMapper();
builder.Services.AddCors(options =>
{
    options.AddPolicy("corsapp", builder => 
        builder.AllowAnyOrigin()
        .AllowAnyMethod()
        .AllowAnyHeader());
});

builder.Services
    .AddDbContextFactory<SSODbContext>(
        options => options.UseSqlServer(
            builder.Configuration.GetConnectionString("SSOConnection")));

builder.Services.AddHealthChecks()
    .AddCheck<DatabaseHealthCheck<SSODbContext>>("Database");

builder.Services.AddOpenTelemetryMetrics();
builder.Services.AddOpenSearchLogging();

var app = builder.Build();

app.MapDefaultEndpoints();
app.MapPrometheusScrapingEndpoint();
app.UseCors("corsapp");
app.UseEndpointDefinitions();
app.MigrateDatabase();
app.UseRouting();
app.UseMiddleware<RequestIdMiddleware>()
    .UseMiddleware<UserIdMiddleware>();
app.UseMiddlewareExceptionLogging();

app.UseHealthChecks();

app.Run();
