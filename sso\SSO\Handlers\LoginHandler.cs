﻿using MediatR;
using SSO.Common.DTOs;
using SSO.Common.Requests;
using SSO.Connections.Interfaces;
using SSO.Services.Interfaces;
using System.Text;
using System.Text.Json;

namespace SSO.Handlers
{
    public class LoginHandler : IRequestHandler<LoginRequest, IResult>
    {
        private readonly IUserRegistrationsConnection _userRegistrationsConnection;
        private readonly IJwtGeneratorService _jwtGeneratorService;
        private readonly IRefreshTokensService _refreshTokensService;

        public LoginHandler(IUserRegistrationsConnection userRegistrationsConnection,
            IJwtGeneratorService jwtGeneratorService,
            IRefreshTokensService refreshTokensService)
        {
            _userRegistrationsConnection = userRegistrationsConnection;
            _jwtGeneratorService = jwtGeneratorService;
            _refreshTokensService = refreshTokensService;
        }

        public async Task<IResult> Handle(LoginRequest request, CancellationToken cancellationToken)
        {
            if (request.HttpRequest is null)
                return Results.Unauthorized();

            var credentials = Convert.ToBase64String(Encoding.UTF8.GetBytes($"{request.Username}:{request.Password}"));

            var response = await _userRegistrationsConnection.TryLoginWithResponseAsync($"Basic {credentials}");
            if (response.StatusCode == System.Net.HttpStatusCode.NotFound)
                return Results.NotFound("Invalid Credentials!");

            if (!response.IsSuccessStatusCode)
                return Results.BadRequest("Unknown error!");

            var userDTO = JsonSerializer.Deserialize<UserDTO>(await response.Content.ReadAsStringAsync(cancellationToken));
            if (userDTO is null)
                return Results.BadRequest();

            request.HttpRequest.HttpContext.Response.Headers.Append("Authorization", $"Bearer {await _jwtGeneratorService.GenerateAccessTokenAsync(userDTO)}");

            var newRefreshToken = await _jwtGeneratorService.GenerateRefreshTokenAsync(userDTO);

            if (string.IsNullOrEmpty(userDTO.Email))
                return Results.Unauthorized();

            await _refreshTokensService.AddRefreshTokenAsync(newRefreshToken, userDTO.Id, userDTO.Email);
            request.HttpRequest.HttpContext.Response.Headers.Append("Refresh-Token", newRefreshToken);

            return Results.Ok(userDTO);
        }
    }
}
