import styled from "styled-components";
import { IGridViewEntity } from "../models/DTOs/IGridViewEntity";
import Avatar from "./Table/Avatar";
import Label from "./Inputs/Label";

interface GridViewProps<T extends IGridViewEntity>
  extends React.DetailedHTMLProps<
    React.TableHTMLAttributes<HTMLDivElement>,
    HTMLDivElement
  > {
  data: T[];
  size: string;
  sizeHover: string;
  imgSize: number;
  handleClick(item: T): void;
}

const GridViewContainer = styled.div`
  display: flex;
  flex-wrap: wrap;
  justify-content: center;
  width: auto;
  height: auto;
  grid-gap: 0.2rem;
  margin-right: 1rem;
`;

const GridViewDiv = styled.div<{ size: string; sizeHover: string }>`
  ${({ size }) => `
       height: ${size};
       width: ${size};
  `}
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  border-radius: 1.8rem;
  border: 0.2rem solid transparent;
  transition: height 0.3s ease-in-out, width 0.3s ease-in-out,
    top 0.3s ease-in-out, left 0.3s ease-in-out, border 0.3s ease-in-out,
    margin 0.3s ease-in-out;
  box-shadow: 0px 2px 5px var(--container-box-shadow-gridView);
  background: var(--container-backround-gridView);
  margin: 0.5rem;
  cursor: pointer;

  &:hover {
    border: 0.2rem solid var(--container-hover-border-gridView);
    ${({ sizeHover }) => `
       height: ${sizeHover};
       width: ${sizeHover};
  `}
    margin: 0.25rem;
  }
`;

const FirstLabel = styled(Label)`
  font: Segoe UI;
  color: var(--first-label-color-gridView);
  opacity: 1;
  font-size: 1.4rem;
  text-align: center;
  position: relative;
  height: 3.6rem;
  margin: 0.2rem;
  cursor: pointer;
`;

const SecondLabel = styled(Label)`
  font: Segoe UI;
  color: var(--second-label-color-gridView);
  opacity: 1;
  font-size: 0.95rem;
  text-align: center;
  position: relative;
  cursor: pointer;
`;

const GridView = <T extends IGridViewEntity>(props: GridViewProps<T>) => {
  const { data, handleClick, size, sizeHover, imgSize } = props;

  return (
    <GridViewContainer>
      {data && data.length > 0 ? (
        data.map((c) => (
          <GridViewDiv
            onClick={() => handleClick(c)}
            size={size}
            sizeHover={sizeHover}
          >
            <Avatar name={c.firstLabel} photo="" size={imgSize} />
            <FirstLabel>{c.firstLabel}</FirstLabel>
            <SecondLabel>{c.secondLabel}</SecondLabel>
          </GridViewDiv>
        ))
      ) : (
        <label>No data found</label>
      )}
    </GridViewContainer>
  );
};
export default GridView;
