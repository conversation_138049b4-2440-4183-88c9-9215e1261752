﻿using Gateway.Connections.Interfaces;
using MediatR;
using WorkTimeApi.Common.Requests.Templates;

namespace Gateway.Handlers.Templates
{
    public class LoadTemplatesHandler : IRequestHandler<LoadTemplatesRequest, IResult>
	{
		private readonly IWorkTimeApiConnection _workTimeApiConnection;

		public LoadTemplatesHandler(IWorkTimeApiConnection workTimeApiConnection)
		{
			_workTimeApiConnection = workTimeApiConnection;
		}

		public async Task<IResult> Handle(LoadTemplatesRequest request, CancellationToken cancellationToken)
		{
			var response = await _workTimeApiConnection.LoadTemplatesAsync(request);

			if (!response.IsSuccessStatusCode)
				return Results.StatusCode((int)response.StatusCode);

			return Results.Text(await response.Content.ReadAsStringAsync(cancellationToken));
		}
	}
}
