using AutoMapper;
using Moq;
using WorkTimeApi.Database.Repositories.Interfaces.Addresses;
using WorkTimeApi.Services.Addresses;

namespace WorkTimeApi.Tests.Services.Addresses
{
    public class AddressesServiceTests
    {
        [Fact]
        public async Task DeleteEmployeeAddressAsync_CallsRepositoryAndReturnsResult()
        {
            // Arrange
            var repoMock = new Mock<IAddressesRepository>(MockBehavior.Strict);
            var mapper = new MapperConfiguration(cfg => { }).CreateMapper();
            var service = new AddressesService(repoMock.Object, mapper);
            var employeeId = Guid.NewGuid();
            var addressId = Guid.NewGuid();
            repoMock.Setup(r => r.DeleteEmployeeAddressAsync(employeeId, addressId)).ReturnsAsync(true);

            // Act
            var result = await service.DeleteEmployeeAddressAsync(employeeId, addressId);

            // Assert
            Assert.True(result);
            repoMock.Verify(r => r.DeleteEmployeeAddressAsync(employeeId, addressId), Times.Once);
        }
    }
}
