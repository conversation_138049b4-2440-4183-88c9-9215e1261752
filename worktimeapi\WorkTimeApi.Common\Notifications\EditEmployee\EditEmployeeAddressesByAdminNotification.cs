﻿using WorkTimeApi.Common.DTOs.Employees;

namespace WorkTimeApi.Common.Notifications.EditEmployee
{
    public class EditEmployeeAddressesByAdminNotification : BaseNotification<EmployeeDTO>
    {
        public Guid UserId { get; }

        public EditEmployeeAddressesByAdminNotification(EmployeeDTO payload, Guid payrollId, Guid companyId, Guid userId, string creatorName)
            : base(payload, companyId, NotificationsName.Employees.AddressesEditedByAdmin.Push, creatorName)
        {
            UserId = userId;

            if (payload == null)
                return;

            if (payrollId == Guid.Empty)
            {
                Url = $"{companyId}/employees/1/{payload.WorkTimeId}?fromNotification=true";
            }
            else
            {
                Url = $"{companyId}/employees/1/{payload.WorkTimeId}/{payrollId}?fromNotification=true";
            }
        }
    }
}