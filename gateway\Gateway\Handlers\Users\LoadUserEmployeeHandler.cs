﻿using Gateway.Common.Results;
using Gateway.Connections.Interfaces;
using MediatR;
using WorkTimeApi.Common.Requests.Employees;

namespace Gateway.Handlers.Users
{
    public class LoadUserEmployeeHandler (IWorkTimeApiConnection workTimeApiConnection) : IRequestHandler<LoadUserEmployeeRequest, IResult>
    {
        public async Task<IResult> Handle(LoadUserEmployeeRequest request, CancellationToken cancellationToken)
        {
            var response = await workTimeApiConnection.LoadUserEmployeeAsync(request);

            return new HttpResponseMessageResult(response);
        }
    }
}
