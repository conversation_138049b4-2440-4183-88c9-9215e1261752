﻿using Gateway.Common.Requests.Interfaces;
using Gateway.Filters;
using MediatR;

namespace Gateway.Extenstions.MediatorExtensions
{
    public static class MediatorExtensions
    {
        public static WebApplication AuthenticatedGet<TRequest>(
            this WebApplication app,
            string template,
            params string[]? requiredPermissions) where TRequest : IHttpRequest
        {
            app.MapGet(template, async (IMediator mediator,
                HttpRequest httpRequest,
                [AsParameters] TRequest request) =>
            {
                request.HttpRequest = httpRequest;
                return await mediator.Send(request);
            }).AddEndpointFilter<AuthenticationFilter>()
            .AddEndpointFilter(new AuthorizationFilter(requiredPermissions));

            return app;
        }

        public static WebApplication AuthenticatedPost<TRequest>(
            this WebApplication app,
            string template,
            params string[]? requiredPermissions) where TRequest : IHttpRequest
        {
            app.MapPost(template, async (IMediator mediator,
                HttpRequest httpRequest,
                TRequest request) =>
            {
                request.HttpRequest = httpRequest;
                return await mediator.Send(request);
            }).AddEndpointFilter<AuthenticationFilter>()
            .AddEndpointFilter(new AuthorizationFilter(requiredPermissions));

            return app;
        }

        public static WebApplication AuthenticatedDelete<TRequest>(
            this WebApplication app,
            string template,
            params string[]? requiredPermissions) where TRequest : IHttpRequest
        {
            app.MapDelete(template, async (IMediator mediator,
                HttpRequest httpRequest,
                [AsParameters] TRequest request) =>
            {
                request.HttpRequest = httpRequest;
                return await mediator.Send(request);
            }).AddEndpointFilter<AuthenticationFilter>()
            .AddEndpointFilter(new AuthorizationFilter(requiredPermissions));

            return app;
        }

        public static WebApplication AuthenticatedPut<TRequest>(
           this WebApplication app,
           string template,
           params string[]? requiredPermissions) where TRequest : IHttpRequest
        {
            app.MapPut(template, async (IMediator mediator,
                HttpRequest httpRequest,
                TRequest request) =>
            {
                request.HttpRequest = httpRequest;
                return await mediator.Send(request);
            }).AddEndpointFilter<AuthenticationFilter>()
            .AddEndpointFilter(new AuthorizationFilter(requiredPermissions));

            return app;
        }

    }
}
