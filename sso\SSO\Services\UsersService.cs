﻿using AutoMapper;
using SSO.Common.DTOs;
using SSO.Common.Responses;
using SSO.Database.Models;
using SSO.Database.Repositories.Interfaces;
using SSO.Services.Interfaces;

namespace SSO.Services
{
    public class UsersService(IUsersRepository usersRepository, IMapper mapper) : IUsersService
    {
        public Task<List<UserPermissionsDTO>> GetUserPermissionsAsync(Guid userId)
            => usersRepository.GetUserPermissionsAsync(userId);

        public Task<AddSupportChatPermissionsResult> AddSupportChatPermissionsAsync(UserDTO userDTO)
            => usersRepository.AddSupportChatPermissionsAsync(mapper.Map<User>(userDTO));

        public async Task<List<UserDTO>> GetUsersByPermissionAsync(string permission)
        {
            var users = await usersRepository.GetUsersByPermissionAsync(permission);
            return mapper.Map<List<UserDTO>>(users);
        }

        public async Task<UserDTO> GetUserByEmailAsync(string email)
        {
            var user = await usersRepository.FindUserByEmailAsync(email);

            return mapper.Map<UserDTO>(user);
        }
        public Task<List<string>> GetUserCompanyPermissionsAsync(Guid userId, int companyId)
           => usersRepository.GetUserCompanyPermissionsAsync(userId,companyId);

        public async Task<UserDTO> GetUserByIdAsync(Guid id)
        {
            var user = await usersRepository.FindUserByIdAsync(id);

            return mapper.Map<UserDTO>(user);
        }
    }
}
