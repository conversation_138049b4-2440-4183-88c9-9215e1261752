﻿using MediatR;
using WorkTime.Notifications.Services.Interfaces;
using WorkTimeApi.Common.Enums;
using WorkTimeApi.Common.Notifications.Absences;
using WorkTimeApi.Database.Repositories.Interfaces.Companies;
using WorkTimeApi.Database.Repositories.Interfaces.Employees;
using WorkTimeApi.Services.Interfaces.Absences;
using WorkTimeApi.Services.Interfaces.Notifications;

namespace WorkTimeApi.Handlers.Notifications.Absences
{
    public class AbsenceEditDeclinedHandler(ISignalRNotificationService signalRNotificationService,
        INotificationsService notificationsService,
        IAbsencesService absencesService,
        IEmployeesRepository employeesRepository,
        IEmailsNotificationService emailsNotificationService,
        ICompaniesRepository companiesRepository,
        IConfiguration configuration) : INotificationHandler<AbsenceEditDeclinedNotification>
    {
        public async Task Handle(AbsenceEditDeclinedNotification notification, CancellationToken cancellationToken)
        {
            var employee = await absencesService.GetEmployeeDTOByAbsenceIdAsync(notification.Payload.Id, notification.Payload.IsHospital) ?? throw new Exception("Íåâàëèäíî Employee!");
            var company = await companiesRepository.GetByIdAsync(notification.CompanyId) ?? throw new Exception("Невалидна компания!");
            var approver = (await employeesRepository.FindAsync(e => e.UserId == notification.UserId && e.CompanyId == notification.CompanyId)).FirstOrDefault() ?? throw new Exception("Невалидно Employee!");
            var approverName = string.Join(" ", new[] { approver.FirstName, approver.LastName }
                           .Where(name => !string.IsNullOrWhiteSpace(name)));
            var fullName = string.Join(" ", new[] { employee.FirstName, employee.LastName }
            .Where(name => !string.IsNullOrWhiteSpace(name)));

            var worktimeUrl = configuration.GetValue<string>("WorkTimeUrl") ?? throw new Exception("Липсва конфигурация за WorkTimeUrl!");
            var absence = notification.Payload;

            var emailRequest = new
            {
                Name = fullName,
                ApproverName = approverName,
                CompanyName = company.Name,
                AbsenceType = absence.TypeIdentifier.GetDescription(),
                StartDate = absence.FromDate.ToString("dd.MM.yyyy"),
                EndDate = absence.ToDate.ToString("dd.MM.yyyy"),
                Comment = absence.Reference,
                Url = $"{worktimeUrl}{notification.Url}"
            };
            var emailNotifications = emailsNotificationService.SendEmailsAsync(new List<string> { employee.Email }, "absences/edit-declined", emailRequest);

            var savedNotification = await notificationsService.AddNotificationByUserIdAndCompanyIdAsync(employee.WorkTimeId, notification);
            await signalRNotificationService.NotifyUser(employee.UserId, savedNotification);
        }
    }
}