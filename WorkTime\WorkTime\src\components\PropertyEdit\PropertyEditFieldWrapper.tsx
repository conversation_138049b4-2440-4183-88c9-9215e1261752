import React from "react";
import styled from "styled-components";
import { PropertyEditInfo } from "../../hooks/usePropertyEdits";
import ApproveEditCardHoverIcon from "../ApproveEdit/ApproveEditCardHoverIcon";

interface PropertyEditFieldWrapperProps {
  children: React.ReactNode;
  propertyEditInfo: PropertyEditInfo;
  onApprove?: () => Promise<void>;
  onDecline?: () => Promise<void>;
  position?: "top" | "bottom" | "left" | "right";
}

const FieldContainer = styled.div`
  position: relative;
  display: flex;
  align-items: center;
`;

const FieldWrapper = styled.div`
  flex: 1;
`;

const IconWrapper = styled.div`
  position: absolute;
  display: flex;
  right: 0.7rem;
  z-index: 1000;
`;

export const PropertyEditFieldWrapper: React.FC<
  PropertyEditFieldWrapperProps
> = ({
  children,
  propertyEditInfo,
  onApprove,
  onDecline,
  position = "bottom",
}) => {
  const { hasEdit, pendingEdit, newValue } = propertyEditInfo;

  const shouldShowIcon = hasEdit && pendingEdit && onApprove && onDecline;

  return (
    <FieldContainer>
      <FieldWrapper>{children}</FieldWrapper>
      {shouldShowIcon && (
        <IconWrapper>
          <ApproveEditCardHoverIcon
            newValue={newValue || ""}
            onConfirm={onApprove}
            onCancel={onDecline}
            position={position}
          />
        </IconWrapper>
      )}
    </FieldContainer>
  );
};
