﻿using Gateway.Common.Results;
using Gateway.Connections.Interfaces;
using Gateway.Requests.UserRegistration;
using MediatR;
using Microinvest.TransferFiles.Tools.Models.Users;
using WorkTimeApi.Common.Requests.Users;

namespace Gateway.Handlers.UserRegistration
{
    public class EditUserDataHandler(IUserRegistrationConnection userRegistrationConnection, IWorkTimeApiConnection workTimeApiConnection) : IRequestHandler<EditUserDataRequest, IResult>
    {
        public async Task<IResult> Handle(EditUserDataRequest request, CancellationToken cancellationToken)
        {
            var urResponse = await userRegistrationConnection.EditUserDataAsync(new UserDTO
            {
                Id = request.UserId,
                FirstName = request.FirstName,
                SecondName = request.SecondName,
                LastName = request.LastName,
            });

            if (urResponse is null || !urResponse.IsSuccessStatusCode)
                return Results.BadRequest();

            if (string.IsNullOrEmpty(request.Email))
                return new HttpResponseMessageResult(urResponse);

            var workTimeResponse = await workTimeApiConnection.UpdateUserEmailAsync(new UpdateUserEmailRequest
            {
                UserDTO = new WorkTimeApi.Common.DTOs.Users.UserDTO
                {
                    Id = Guid.Parse(request.UserId),
                    FirstName = request.FirstName,
                    SecondName = request.SecondName,
                    LastName = request.LastName,
                    Email = request.Email
                }
            });

            return workTimeResponse is null || !workTimeResponse.IsSuccessStatusCode
                ? Results.BadRequest()
                : new HttpResponseMessageResult(urResponse);
        }
    }
}

