﻿using Gateway.Common.Globals;
using Gateway.Common.Requests.Roles;
using Gateway.Common.Results;
using Gateway.Connections.Interfaces;
using MediatR;
using WorkTimeApi.Common.Requests.Roles;

namespace Gateway.Handlers.SSO
{
    public class GetWorkTimeRoleHandler(IWorkTimeApiConnection workTimeApiConnection, GlobalUser globalUser) : IRequestHandler<GetWorkTimeRoleGatewayRequest, IResult>
    {
        public async Task<IResult> Handle(GetWorkTimeRoleGatewayRequest request, CancellationToken cancellationToken)
        {
            var response = await workTimeApiConnection.GetWorkTimeRoleAsync(new GetWorkTimeRoleRequest() { UserId = globalUser.Id });

            return new HttpResponseMessageResult(response);
        }
    }
}
