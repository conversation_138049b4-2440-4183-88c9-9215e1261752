﻿using System;
using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

#pragma warning disable CA1814 // Prefer jagged arrays over multidimensional

namespace SSO.Database.Migrations
{
    /// <inheritdoc />
    public partial class RemovedPermissions : Migration
    {
        /// <inheritdoc />
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropForeignKey(
                name: "FK_Users_Roles_WorkTimeRoleId",
                table: "Users");

            migrationBuilder.DropIndex(
                name: "IX_Users_WorkTimeRoleId",
                table: "Users");

            migrationBuilder.DeleteData(
                table: "Permissions",
                keyColumn: "Id",
                keyValue: new Guid("1340cdab-a99c-ab23-0fb8-7bd4edcaeaf0"));

            migrationBuilder.DeleteData(
                table: "Permissions",
                keyColumn: "Id",
                keyValue: new Guid("269baed5-dd92-36bc-7fa8-f9c547fcec4d"));

            migrationBuilder.DeleteData(
                table: "Permissions",
                keyColumn: "Id",
                keyValue: new Guid("3b801a4a-f37c-8efb-49cf-faa0fbeb9ea9"));

            migrationBuilder.DeleteData(
                table: "Permissions",
                keyColumn: "Id",
                keyValue: new Guid("5053acf7-2718-0f02-592a-1b32addf5972"));

            migrationBuilder.DeleteData(
                table: "Permissions",
                keyColumn: "Id",
                keyValue: new Guid("621e7ab3-d58f-9d92-2b28-fbc5ebcdf6a2"));

            migrationBuilder.DeleteData(
                table: "Permissions",
                keyColumn: "Id",
                keyValue: new Guid("79a3e9d2-999c-a677-c304-1054d8d29dc0"));

            migrationBuilder.DeleteData(
                table: "Permissions",
                keyColumn: "Id",
                keyValue: new Guid("7dd10ffd-497d-c5ee-a3e4-42ca56a5480c"));

            migrationBuilder.DeleteData(
                table: "Permissions",
                keyColumn: "Id",
                keyValue: new Guid("8571a7bc-4a39-7b25-98a8-55ca4dcf3eed"));

            migrationBuilder.DeleteData(
                table: "Permissions",
                keyColumn: "Id",
                keyValue: new Guid("8a5e2a7d-6d34-bb72-5fc8-b8aeac9f024b"));

            migrationBuilder.DeleteData(
                table: "Permissions",
                keyColumn: "Id",
                keyValue: new Guid("b1815361-f2d2-d704-4129-f7ad010513ea"));

            migrationBuilder.DeleteData(
                table: "Permissions",
                keyColumn: "Id",
                keyValue: new Guid("b207f4fd-afc9-ca30-3900-01f04dd17662"));

            migrationBuilder.DeleteData(
                table: "Permissions",
                keyColumn: "Id",
                keyValue: new Guid("fa6fdc50-e8b4-0314-84cd-6e6cdcd2700e"));

            migrationBuilder.DropColumn(
                name: "WorkTimeRoleId",
                table: "Users");

            migrationBuilder.Sql(
                """
                Delete from Roles where UserRegistrationCompanyId <> -10000 
                """);
        }

        /// <inheritdoc />
        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.AddColumn<Guid>(
                name: "WorkTimeRoleId",
                table: "Users",
                type: "uniqueidentifier",
                nullable: true);

            migrationBuilder.InsertData(
                table: "Permissions",
                columns: new[] { "Id", "Name" },
                values: new object[,]
                {
                    { new Guid("1340cdab-a99c-ab23-0fb8-7bd4edcaeaf0"), "Attendances.Read" },
                    { new Guid("269baed5-dd92-36bc-7fa8-f9c547fcec4d"), "Employees.Coworkerrs.Write" },
                    { new Guid("3b801a4a-f37c-8efb-49cf-faa0fbeb9ea9"), "Employees.Coworkerrs.Read" },
                    { new Guid("5053acf7-2718-0f02-592a-1b32addf5972"), "Structure.Write" },
                    { new Guid("621e7ab3-d58f-9d92-2b28-fbc5ebcdf6a2"), "Documents.Write" },
                    { new Guid("79a3e9d2-999c-a677-c304-1054d8d29dc0"), "Attendances.Write" },
                    { new Guid("7dd10ffd-497d-c5ee-a3e4-42ca56a5480c"), "Employees.Write" },
                    { new Guid("8571a7bc-4a39-7b25-98a8-55ca4dcf3eed"), "Documents.Read" },
                    { new Guid("8a5e2a7d-6d34-bb72-5fc8-b8aeac9f024b"), "Employees.Delete" },
                    { new Guid("b1815361-f2d2-d704-4129-f7ad010513ea"), "Employees.Read" },
                    { new Guid("b207f4fd-afc9-ca30-3900-01f04dd17662"), "Employees.Approve" },
                    { new Guid("fa6fdc50-e8b4-0314-84cd-6e6cdcd2700e"), "Structure.Read" }
                });

            migrationBuilder.CreateIndex(
                name: "IX_Users_WorkTimeRoleId",
                table: "Users",
                column: "WorkTimeRoleId");

            migrationBuilder.AddForeignKey(
                name: "FK_Users_Roles_WorkTimeRoleId",
                table: "Users",
                column: "WorkTimeRoleId",
                principalTable: "Roles",
                principalColumn: "Id");
        }
    }
}
