import styled from "styled-components";
import Container from "../../components/Container";
import VerticalLine from "../../components/VerticalLine";
import Login from "./Login";
import GoogleLogin from "./Providers/GoogleLogin";
import FacebookLogin from "./Providers/FacebookLogin";
import MicrosoftLogin from "./Providers/MicrosoftLogin";
import { useSearchParams } from "react-router-dom";
import Label from "../../components/Inputs/Label";
import Button from "../../components/Inputs/Button";
import { useState } from "react";
import Registration from "./Registration";
import { AuthMode } from "../../models/Enums/AuthMode";
import MainWindowContainer from "../../components/MainWindowContainer";

const MainContainer = styled(MainWindowContainer)`
  display: flex;
  flex-wrap: wrap;
  flex: 1 1 auto;
  justify-content: center;
`;

const DataContainer = styled(Container)`
  display: flex;
  flex-direction: column;
  justify-content: center;
  padding: 2.5rem;
  box-sizing: border-box;
  width: 30rem;

  @media (max-width: 980px) {
    flex: 100%;
    padding: 0 2.5rem 0 2.5rem;
  }
`;

const HeaderContainer = styled(Container)`
  width: 100%;
  height: 3.25rem;
  position: relative;
  display: flex;
  align-items: center;
  margin-bottom: 0.5rem;
`;

const CurrentMenuLabel = styled(Label)`
  position: absolute;
  left: 1rem;
  font-size: 1.5rem;
`;

const NextMenuButton = styled(Button)`
  position: absolute;
  right: 0.25rem;
  background: none;
  color: var(--button-no-background-color);
  font-size: 0.85rem;

  &:hover {
    color: var(--button-no-background-color-hover);
  }
`;

const SeparatorContainer = styled(Container)`
  display: flex;
  flex-direction: column;
  justify-content: center;
  padding-top: 7rem;

  @media (max-width: 600px) {
    display: none;
  }
`;

const Separator = styled(VerticalLine)`
  height: 20rem;
  margin-bottom: 2.5rem;

  @media (max-width: 980px) {
    display: none;
  }
`;

const AuthProvidersContainer = styled(Container)`
  display: flex;
  flex-direction: column;
  justify-content: center;
  box-sizing: border-box;
  width: 25rem;
  padding: 0 2.5rem;

  @media (max-width: 980px) {
    padding: 0 2.5rem 2.5rem 2.5rem;
  }
`;

interface Props {
  initialAuthMode: AuthMode;
}

const AuthContainer = ({ initialAuthMode }: Props) => {
  const [searchParams] = useSearchParams();
  const [authMode, setAuthMode] = useState(initialAuthMode);
  const returnAfterLogin = searchParams.get("returnAfterLogin");

  return (
    <MainContainer>
      <DataContainer>
        <HeaderContainer>
          <CurrentMenuLabel>
            {authMode === AuthMode.Login ? "Login" : "Registration"}
          </CurrentMenuLabel>
          {authMode === AuthMode.Login ? (
            <NextMenuButton
              label="Registration"
              onClick={() => setAuthMode(AuthMode.Registration)}
            ></NextMenuButton>
          ) : (
            <NextMenuButton
              label="Login"
              onClick={() => setAuthMode(AuthMode.Login)}
            ></NextMenuButton>
          )}
        </HeaderContainer>
        {authMode === AuthMode.Login ? (
          <Login returnAfterLogin={returnAfterLogin ?? undefined} />
        ) : (
          <Registration />
        )}
      </DataContainer>
      <SeparatorContainer>
        <Separator />
      </SeparatorContainer>
      <AuthProvidersContainer>
        <GoogleLogin returnAfterLogin={returnAfterLogin ?? undefined} />
        <FacebookLogin returnAfterLogin={returnAfterLogin ?? undefined} />
        <MicrosoftLogin returnAfterLogin={returnAfterLogin ?? undefined} />
      </AuthProvidersContainer>
    </MainContainer>
  );
};

export default AuthContainer;
