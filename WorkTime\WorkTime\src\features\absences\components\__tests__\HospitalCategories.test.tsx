import { hospitalCategories } from "../HospitalCategories";
import { EventType } from "../../../../models/DTOs/absence/EventType";

// Mock the translation function
jest.mock("../../../../services/language/LanguageProvider", () => ({
  useLanguage: () => ({
    translate: (key: string) => key, // Return the key as translation for testing
  }),
}));

// Mock the absence actions
jest.mock("../../../../utils/absenceActions", () => ({
  useAbsenceActions: () => ({
    handleDeleteAbsence: jest.fn(),
    handleApproveAbsence: jest.fn(),
    handleDeclineAbsence: jest.fn(),
  }),
}));

// Mock the menu context
jest.mock("../../../MenuContext", () => ({
  useMenu: () => ({
    selectedEmployee: { id: 1, name: "Test Employee" },
  }),
}));

describe("HospitalCategories", () => {
  describe("Data Structure Tests", () => {
    it("should have exactly 2 main categories", () => {
      expect(hospitalCategories).toHaveLength(2);
    });

    it("should have 'Sick Leave' as first category", () => {
      expect(hospitalCategories[0].key).toBe("Sick Leave");
      expect(hospitalCategories[0].eventType).toBeUndefined();
    });

    it("should have 'Maternity Leave' as second category", () => {
      expect(hospitalCategories[1].key).toBe("Maternity Leave");
      expect(hospitalCategories[1].eventType).toBeUndefined();
    });

    it("should have correct number of sub-options for Sick Leave", () => {
      expect(hospitalCategories[0].subOptions).toHaveLength(8);
    });

    it("should have correct number of sub-options for Maternity Leave", () => {
      expect(hospitalCategories[1].subOptions).toHaveLength(8);
    });

    it("should have unique event types for all sub-options", () => {
      const allEventTypes = hospitalCategories.flatMap(
        (category) => category.subOptions?.map((sub) => sub.eventType) || []
      );
      const uniqueEventTypes = new Set(allEventTypes);
      expect(uniqueEventTypes.size).toBe(allEventTypes.length);
    });

    it("should have event types in correct ranges", () => {
      const sickLeaveEventTypes =
        hospitalCategories[0].subOptions?.map((sub) => sub.eventType) || [];
      const maternityLeaveEventTypes =
        hospitalCategories[1].subOptions?.map((sub) => sub.eventType) || [];

      // Sick Leave should be in 600 range
      sickLeaveEventTypes.forEach((eventType) => {
        expect(eventType).toBeGreaterThanOrEqual(601);
        expect(eventType).toBeLessThanOrEqual(608);
      });

      // Maternity Leave should be in 700 range
      maternityLeaveEventTypes.forEach((eventType) => {
        expect(eventType).toBeGreaterThanOrEqual(701);
        expect(eventType).toBeLessThanOrEqual(708);
      });
    });
  });

  describe("Sick Leave Category Tests", () => {
    const sickLeaveCategory = hospitalCategories[0];

    it("should have no main event type", () => {
      expect(sickLeaveCategory.eventType).toBeUndefined();
    });

    it("should have all required sick leave sub-options", () => {
      const expectedSubOptions = [
        { id: "sick_leave_basic", title: "Sick Leave", eventType: 601 },
        {
          id: "sick_leave_family_care",
          title: "Caring for Sick Family Member",
          eventType: 602,
        },
        {
          id: "sick_leave_work_accident",
          title: "Work Accident",
          eventType: 603,
        },
        {
          id: "sick_leave_occupational_disease",
          title: "Occupational Disease",
          eventType: 604,
        },
        {
          id: "sick_leave_unpaid_disability",
          title: "Unpaid Leave for Temporary Disability",
          eventType: 605,
        },
        {
          id: "sick_leave_pregnancy",
          title: "Sick Leave During Pregnancy",
          eventType: 606,
        },
        {
          id: "sick_leave_after_birth",
          title: "Sick Leave After Birth",
          eventType: 607,
        },
        {
          id: "sick_leave_pregnancy_birth_unpaid",
          title: "Unpaid Leave for Pregnancy and Birth",
          eventType: 608,
        },
      ];

      expectedSubOptions.forEach((expectedSub) => {
        const actualSub = sickLeaveCategory.subOptions?.find(
          (sub) => sub.id === expectedSub.id
        );
        expect(actualSub).toBeDefined();
        expect(actualSub?.title).toBe(expectedSub.title);
        expect(actualSub?.eventType).toBe(expectedSub.eventType);
      });
    });

    it("should have unique IDs for all sub-options", () => {
      const ids = sickLeaveCategory.subOptions?.map((sub) => sub.id) || [];
      const uniqueIds = new Set(ids);
      expect(uniqueIds.size).toBe(ids.length);
    });
  });

  describe("Maternity Leave Category Tests", () => {
    const maternityLeaveCategory = hospitalCategories[1];

    it("should have no main event type", () => {
      expect(maternityLeaveCategory.eventType).toBeUndefined();
    });

    it("should have all required maternity leave sub-options", () => {
      const expectedSubOptions = [
        {
          id: "maternity_leave_one_thirty_five_to_four_ten",
          title: "Maternity Leave 135-410 Days",
          eventType: 701,
        },
        {
          id: "paternity_leave_over_six_months",
          title: "Paternity Leave for Child Care Over 6 Months",
          eventType: 702,
        },
        {
          id: "leave_fifteen_days_birth",
          title: "Leave Up to 15 Days for Child Birth",
          eventType: 703,
        },
        {
          id: "leave_child_care_two_years",
          title: "Leave for Child Care Up to 2 Years",
          eventType: 704,
        },
        {
          id: "leave_adoption_five_years",
          title: "Leave for Child Adoption Up to 5 Years",
          eventType: 705,
        },
        {
          id: "unpaid_leave_adoption_five_years",
          title: "Unpaid Leave for Child Adoption Up to 5 Years",
          eventType: 706,
        },
        {
          id: "paternity_leave_adoption_five_years",
          title: "Paternity Leave for Child Adoption Up to 5 Years",
          eventType: 707,
        },
        {
          id: "leave_child_care_eight_years_father",
          title: "Leave for Child Care Up to 8 Years by Father (Adopter)",
          eventType: 708,
        },
      ];

      expectedSubOptions.forEach((expectedSub) => {
        const actualSub = maternityLeaveCategory.subOptions?.find(
          (sub) => sub.id === expectedSub.id
        );
        expect(actualSub).toBeDefined();
        expect(actualSub?.title).toBe(expectedSub.title);
        expect(actualSub?.eventType).toBe(expectedSub.eventType);
      });
    });

    it("should have unique IDs for all sub-options", () => {
      const ids = maternityLeaveCategory.subOptions?.map((sub) => sub.id) || [];
      const uniqueIds = new Set(ids);
      expect(uniqueIds.size).toBe(ids.length);
    });
  });

  describe("Event Type Mapping Tests", () => {
    it("should map to correct EventType enum values", () => {
      const sickLeaveCategory = hospitalCategories[0];
      const maternityLeaveCategory = hospitalCategories[1];

      // Test Sick Leave mappings
      expect(sickLeaveCategory.subOptions?.[0].eventType).toBe(
        EventType.Болничен
      );
      expect(sickLeaveCategory.subOptions?.[1].eventType).toBe(
        EventType.ГледанеНаБоленЧленОтСемейството
      );
      expect(sickLeaveCategory.subOptions?.[2].eventType).toBe(
        EventType.ТрудоваЗлополука
      );
      expect(sickLeaveCategory.subOptions?.[3].eventType).toBe(
        EventType.ПрофесионалнаБолест
      );
      expect(sickLeaveCategory.subOptions?.[4].eventType).toBe(
        EventType.НеплатенЗаВременнаНеработоспособност
      );
      expect(sickLeaveCategory.subOptions?.[5].eventType).toBe(
        EventType.БолниченПоБременност
      );
      expect(sickLeaveCategory.subOptions?.[6].eventType).toBe(
        EventType.БолниченСледРаждане
      );
      expect(sickLeaveCategory.subOptions?.[7].eventType).toBe(
        EventType.НеплатенЗаБременностИРаждане
      );

      // Test Maternity Leave mappings
      expect(maternityLeaveCategory.subOptions?.[0].eventType).toBe(
        EventType.ОтпускЗаМайкаСледРаждане135До410Ден
      );
      expect(maternityLeaveCategory.subOptions?.[1].eventType).toBe(
        EventType.ОтпускЗаБащаЗаГледанеНаДетеНад6Месеца
      );
      expect(maternityLeaveCategory.subOptions?.[2].eventType).toBe(
        EventType.ОтпускДо15ДниПриРажданеНаДете
      );
      expect(maternityLeaveCategory.subOptions?.[3].eventType).toBe(
        EventType.ОтпускЗаОтглежданеНаДетеДо2Години
      );
      expect(maternityLeaveCategory.subOptions?.[4].eventType).toBe(
        EventType.ОтпускПриОсиновяванеНаДетеДо5ГодишнаВъзраст
      );
      expect(maternityLeaveCategory.subOptions?.[5].eventType).toBe(
        EventType.НеплатенОтпускПриОсиновяванеНаДетеДо5ГодишнаВъзраст
      );
      expect(maternityLeaveCategory.subOptions?.[6].eventType).toBe(
        EventType.ОтпускЗаБащаПриОсиновяванеНаДетеДо5ГодишнаВъзраст
      );
      expect(maternityLeaveCategory.subOptions?.[7].eventType).toBe(
        EventType.ОтпускЗаОтглежданеНаДетеДо8ГодишнаВъзрастОтБащата
      );
    });
  });

  describe("Data Integrity Tests", () => {
    it("should not have any duplicate event types across all categories", () => {
      const allEventTypes = hospitalCategories.flatMap(
        (category) => category.subOptions?.map((sub) => sub.eventType) || []
      );
      const uniqueEventTypes = new Set(allEventTypes);
      expect(uniqueEventTypes.size).toBe(allEventTypes.length);
    });

    it("should not have any duplicate IDs across all categories", () => {
      const allIds = hospitalCategories.flatMap(
        (category) => category.subOptions?.map((sub) => sub.id) || []
      );
      const uniqueIds = new Set(allIds);
      expect(uniqueIds.size).toBe(allIds.length);
    });

    it("should have consistent title and ID naming", () => {
      hospitalCategories.forEach((category) => {
        category.subOptions?.forEach((subOption) => {
          // ID should be lowercase with underscores
          expect(subOption.id).toMatch(/^[a-z_]+$/);

          // Title should be descriptive and not empty
          expect(subOption.title).toBeTruthy();
          expect(subOption.title.length).toBeGreaterThan(0);
        });
      });
    });

    it("should have all required translation keys", () => {
      const requiredTranslationKeys = [
        "Hospital Categories",
        "Sick Leave",
        "Maternity Leave",
        "Caring for Sick Family Member",
        "Work Accident",
        "Occupational Disease",
        "Unpaid Leave for Temporary Disability",
        "Sick Leave During Pregnancy",
        "Sick Leave After Birth",
        "Unpaid Leave for Pregnancy and Birth",
        "Maternity Leave 135-410 Days",
        "Paternity Leave for Child Care Over 6 Months",
        "Leave Up to 15 Days for Child Birth",
        "Leave for Child Care Up to 2 Years",
        "Leave for Child Adoption Up to 5 Years",
        "Unpaid Leave for Child Adoption Up to 5 Years",
        "Paternity Leave for Child Adoption Up to 5 Years",
        "Leave for Child Care Up to 8 Years by Father (Adopter)",
      ];

      // This test ensures that all required translation keys are expected
      // The actual translation testing would be done in integration tests
      expect(requiredTranslationKeys).toHaveLength(18);
    });
  });
});
