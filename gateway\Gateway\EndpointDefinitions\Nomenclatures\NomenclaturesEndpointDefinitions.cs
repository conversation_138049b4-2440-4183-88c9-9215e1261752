﻿using Gateway.Common.Extenstions.EndpointExtensions;
using Gateway.Extenstions.MediatorExtensions;
using WorkTimeApi.Common.Requests.Nomenclatures;

namespace Gateway.EndpointDefinitions.Nomenclatures
{
    public class NomenclatureEndpointDefinitions : IEndpointDefinition
    {
        public void DefineEndpoints(WebApplication app)
        {
            app.AuthenticatedGet<GetTerminationReasonRequest>("/termination-reason")
                .AuthenticatedGet<GetCountriesRequest>("/countries")
                .AuthenticatedGet<GetIncomeTypesRequest>("/income-types")
                .AuthenticatedGet<GetQualificationGroupsRequest>("/qualification-groups")
                .AuthenticatedGet<GetDistrictsRequest>("/default-districts-data")
                .AuthenticatedGet<GetMunicipalitiesRequest>("/default-municipalities-data")
                .AuthenticatedGet<GetCitiesRequest>("/default-cities-data")
                .AuthenticatedGet<GetTZPBsRequest>("/tzpbs")
                .AuthenticatedGet<GetModsRequest>("/mods")
                .AuthenticatedGet<GetNKPDsRequest>("/nkpds")
                .AuthenticatedGet<GetKidsRequest>("/kids")
                .AuthenticatedGet<GetAbsenceTypesRequest>("/absence-types")
                .AuthenticatedGet<GetHospitalTypesRequest>("/hospital-types");
        }

        public void DefineServices(IServiceCollection services)
        {
        }
    }
}
