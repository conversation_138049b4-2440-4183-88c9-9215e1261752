import {
  render,
  screen,
  fireEvent,
  waitFor,
  act,
} from "@testing-library/react";
import Addresses from "../Addresses";
import { PersonalInformationDTO } from "../../../../models/DTOs/payrolls/PersonalInformationDTO";
import { EmployeeDTO } from "../../../../models/DTOs/employees/EmployeeDTO";
import { AddressDTO } from "../../../../models/DTOs/address/AddressDTO";
import { AddressPurpose } from "../../../../models/DTOs/address/AddressDTO";
import { Genders } from "../../../../constants/enum";

// Mock dependencies
const mockToggleMenu = jest.fn();
const mockChangeView = jest.fn();
const mockRegisterCallback = jest.fn();
const mockUnregisterCallback = jest.fn();

jest.mock("../../../MenuContext", () => ({
  useMenu: () => ({
    toggleMenu: mockToggleMenu,
    changeView: mockChangeView,
    registerCallback: mockRegisterCallback,
    unregisterCallback: mockUnregisterCallback,
  }),
}));

jest.mock("../../../DefaultLocationDataContext", () => ({
  useDefaultPlaces: () => ({
    countries: [
      { id: "1", name: "Bulgaria", identifier: 1 },
      { id: "2", name: "Germany", identifier: 2 },
    ],
  }),
}));

// Mock Redux hooks with a mutable employees state
let mockEmployeesState: any = { currentProfile: undefined };
jest.mock("../../../../app/hooks", () => ({
  useAppDispatch: () => jest.fn(),
  useAppSelector: (_selector: any) => mockEmployeesState,
}));

// Mock addresses service to avoid real API calls during delete
jest.mock("../../../../services/addresses/addressesService", () => ({
  deleteEmployeeAddress: jest.fn().mockResolvedValue(undefined),
}));

jest.mock("../../../../services/employees/employeesService", () => ({
  PropertyEditService: {
    hasEditForProperty: jest.fn().mockReturnValue(false),
    getOldValueForProperty: jest.fn().mockReturnValue(null),
    getEditForProperty: jest.fn().mockReturnValue(null),
  },
}));

// Helper function to create mock employee data
const createMockEmployee = (
  overrides: Partial<EmployeeDTO> = {}
): EmployeeDTO => ({
  id: "emp-1",
  workTimeId: "worktime-123",
  firstName: "Jane",
  secondName: "Marie",
  lastName: "Johnson",
  egn: "**********",
  email: "<EMAIL>",
  phone: "+**********",
  workPhone: "+**********",
  idNumber: "ID123456",
  idIssueDate: "2020-01-01",
  idIssuedFrom: "Test Authority",
  gender: Genders.Female,
  birthDate: "1990-01-01",
  birthPlace: "Test City",
  citizenship: "Test Country",
  userId: "user-123",
  companyId: "company-1",
  company: {} as any,
  bankAccounts: [],
  addresses: [],
  ...overrides,
});

// Helper function to create mock address data
const createMockAddress = (
  overrides: Partial<AddressDTO> = {}
): AddressDTO => ({
  id: "addr-1",
  employeeId: "emp-1",
  city: {
    id: "city-1",
    name: "Sofia",
    identifier: 1,
  },
  postalCode: "1000",
  municipality: { id: "mun-1", name: "Sofia", identifier: 1 },
  district: { id: "dist-1", name: "Sofia", identifier: 1 },
  street: "Vitosha Blvd",
  block: "1",
  apartment: "A",
  phone: "+**********",
  workPhone: "+**********",
  email: "<EMAIL>",
  workEmail: "<EMAIL>",
  country: { id: "1", name: "Bulgaria", identifier: 1 },
  purpose: {
    id: "1",
    name: "For Contact",
    identifier: AddressPurpose.ForContact,
  },
  description: "Main address",
  neighborhood: "Center",
  cityName: "Sofia",
  districtName: "Sofia",
  municipalityName: "Sofia",
  region: "Sofia",
  ...overrides,
});

// Helper function to create mock profile data
const createMockProfile = (
  employeeOverrides: Partial<EmployeeDTO> = {},
  addresses: AddressDTO[] = []
): PersonalInformationDTO => ({
  iban: "BG123456789",
  code: "EMP001",
  employee: createMockEmployee(employeeOverrides),
  addresses,
  payrollPersonalData: {} as any,
  employeePropertyEdits: [],
});

describe("Addresses Component - Employee Data Passing", () => {
  beforeEach(() => {
    jest.clearAllMocks();
    mockEmployeesState = { currentProfile: undefined };
  });

  describe("handleEditAddress function", () => {
    test("should pass incomingProfile when calling changeView for edit-employee-addresses", () => {
      const mockEmployee = createMockEmployee({
        firstName: "John",
        secondName: "Michael",
        lastName: "Doe",
        email: "<EMAIL>",
        workTimeId: "worktime-456",
      });

      const mockAddress = createMockAddress({
        id: "addr-1",
        purpose: {
          id: "1",
          name: "For Contact",
          identifier: AddressPurpose.ForContact,
        },
      });

      const mockProfile = createMockProfile(mockEmployee, [mockAddress]);
      mockEmployeesState = { currentProfile: mockProfile };

      render(
        <Addresses
          employeeName="John Michael Doe"
          shouldShowAdminEditButtons={true}
          shouldShowUserEditButtons={false}
        />
      );

      // Find and click the edit button
      const editButton = screen.getAllByTestId("edit-address-button")[0];
      fireEvent.click(editButton);

      expect(mockChangeView).toHaveBeenCalledWith(
        "edit-employee-addresses",
        "other",
        expect.objectContaining({
          addressesList: [mockAddress],
          employeeId: "worktime-456",
          step: "addr-1",
          incomingProfile: mockProfile, // This is the key assertion
        })
      );

      expect(mockToggleMenu).toHaveBeenCalled();
    });

    test("should pass correct employee data for different employee", () => {
      const mockEmployee = createMockEmployee({
        firstName: "Alice",
        secondName: "Rose",
        lastName: "Smith",
        email: "<EMAIL>",
        workTimeId: "worktime-789",
      });

      const mockAddress = createMockAddress({
        id: "addr-2",
        purpose: {
          id: "2",
          name: "For Remote Work",
          identifier: AddressPurpose.ForRemoteWork,
        },
      });

      const mockProfile = createMockProfile(mockEmployee, [mockAddress]);
      mockEmployeesState = { currentProfile: mockProfile };

      render(
        <Addresses
          employeeName="Alice Rose Smith"
          shouldShowAdminEditButtons={true}
          shouldShowUserEditButtons={false}
        />
      );

      const editButton = screen.getAllByTestId("edit-address-button")[0];
      fireEvent.click(editButton);

      expect(mockChangeView).toHaveBeenCalledWith(
        "edit-employee-addresses",
        "other",
        expect.objectContaining({
          addressesList: [mockAddress],
          employeeId: "worktime-789",
          step: "default_1",
          incomingProfile: mockProfile,
        })
      );
    });

    test("should handle case when no address is selected", () => {
      const mockEmployee = createMockEmployee({
        firstName: "Bob",
        lastName: "Wilson",
        email: "<EMAIL>",
        workTimeId: "worktime-101",
      });

      const mockAddress = createMockAddress({
        purpose: { id: "3", name: "Abroad", identifier: AddressPurpose.Abroad },
      });

      const mockProfile = createMockProfile(mockEmployee, [mockAddress]);
      mockEmployeesState = { currentProfile: mockProfile };

      render(
        <Addresses
          employeeName="Bob Wilson"
          shouldShowAdminEditButtons={true}
          shouldShowUserEditButtons={false}
        />
      );

      const editButton = screen.getAllByTestId("edit-address-button")[0];
      fireEvent.click(editButton);

      expect(mockChangeView).toHaveBeenCalledWith(
        "edit-employee-addresses",
        "other",
        expect.objectContaining({
          addressesList: [mockAddress],
          employeeId: "worktime-101",
          step: "default_1", // ForContact default gets injected
          incomingProfile: mockProfile,
        })
      );
    });

    test("should pass employee data even when employee has no email", () => {
      const mockEmployee = createMockEmployee({
        firstName: "Charlie",
        lastName: "Brown",
        email: "", // No email
        workTimeId: "worktime-202",
      });

      const mockAddress = createMockAddress();
      const mockProfile = createMockProfile(mockEmployee, [mockAddress]);
      mockEmployeesState = { currentProfile: mockProfile };

      render(
        <Addresses
          employeeName="Charlie Brown"
          shouldShowAdminEditButtons={true}
          shouldShowUserEditButtons={false}
        />
      );

      const editButton = screen.getAllByTestId("edit-address-button")[0];
      fireEvent.click(editButton);

      expect(mockChangeView).toHaveBeenCalledWith(
        "edit-employee-addresses",
        "other",
        expect.objectContaining({
          addressesList: [mockAddress],
          employeeId: "worktime-202",
          step: "addr-1",
          incomingProfile: mockProfile, // Should still pass the profile
        })
      );
    });

    test("should pass employee data even when employee has no workTimeId", () => {
      const mockEmployee = createMockEmployee({
        firstName: "Diana",
        lastName: "Prince",
        email: "<EMAIL>",
        workTimeId: "", // No workTimeId
      });

      const mockAddress = createMockAddress();
      const mockProfile = createMockProfile(mockEmployee, [mockAddress]);
      mockEmployeesState = { currentProfile: mockProfile };

      render(
        <Addresses
          employeeName="Diana Prince"
          shouldShowAdminEditButtons={true}
          shouldShowUserEditButtons={false}
        />
      );

      const editButton = screen.getAllByTestId("edit-address-button")[0];
      fireEvent.click(editButton);

      expect(mockChangeView).toHaveBeenCalledWith(
        "edit-employee-addresses",
        "other",
        expect.objectContaining({
          addressesList: [mockAddress],
          employeeId: "", // Should pass empty string
          step: "addr-1",
          incomingProfile: mockProfile,
        })
      );
    });
  });

  describe("handleAddNewAddress function", () => {
    test("should pass employeeId when adding new address", () => {
      const mockEmployee = createMockEmployee({
        workTimeId: "worktime-303",
      });

      const mockAddresses = [createMockAddress()];
      const mockProfile = createMockProfile(mockEmployee, mockAddresses);
      mockEmployeesState = { currentProfile: mockProfile };

      render(
        <Addresses
          employeeName="Test Employee"
          shouldShowAdminEditButtons={true}
          shouldShowUserEditButtons={false}
        />
      );

      const addButton = screen.getByText(/(New address|Нов адрес)/);
      fireEvent.click(addButton);

      expect(mockChangeView).toHaveBeenCalledWith("new-address", "other", {
        employeeId: "worktime-303",
        addressesList: mockAddresses,
      });

      expect(mockToggleMenu).toHaveBeenCalled();
    });
  });

  describe("component rendering", () => {
    test("should render with correct employee name", () => {
      const mockEmployee = createMockEmployee({
        firstName: "Eve",
        secondName: "Marie",
        lastName: "Adams",
      });

      const mockProfile = createMockProfile(mockEmployee);
      mockEmployeesState = { currentProfile: mockProfile };

      render(
        <Addresses
          employeeName="Eve Marie Adams"
          shouldShowAdminEditButtons={true}
          shouldShowUserEditButtons={false}
        />
      );

      // The component should render without errors
      expect(screen.getByText(/^(New address|Нов адрес)$/)).toBeInTheDocument();
    });

    test("should handle empty addresses list", () => {
      const mockEmployee = createMockEmployee();
      const mockProfile = createMockProfile(mockEmployee, []); // Empty addresses
      mockEmployeesState = { currentProfile: mockProfile };

      render(
        <Addresses
          employeeName="Test Employee"
          shouldShowAdminEditButtons={true}
          shouldShowUserEditButtons={false}
        />
      );

      expect(screen.getByText(/^(New address|Нов адрес)$/)).toBeInTheDocument();
    });
  });

  describe("delete icon behavior", () => {
    const contactAddress = createMockAddress({
      id: "addr-contact",
      purpose: {
        id: "1",
        name: "For Contact",
        identifier: AddressPurpose.ForContact,
      },
    });

    const remoteWorkAddress = createMockAddress({
      id: "addr-remote",
      purpose: {
        id: "3",
        name: "For Remote Work",
        identifier: AddressPurpose.ForRemoteWork,
      },
      description: "Remote",
    });

    test("delete icon is hidden by default and shown only for selected deletable address", () => {
      const mockProfile = createMockProfile(createMockEmployee(), [
        contactAddress,
        remoteWorkAddress,
      ]);
      mockEmployeesState = { currentProfile: mockProfile };

      render(
        <Addresses
          employeeName="Test Employee"
          shouldShowAdminEditButtons={false}
          shouldShowUserEditButtons={false}
        />
      );

      // Initially 'For contact' is selected → no delete icon
      expect(
        document.querySelectorAll('[data-testid^="delete-address-"]').length
      ).toBe(0);

      // Select 'For remote work' → delete icon should appear only for that item
      fireEvent.click(
        screen.getByText(/(For remote work|За дистанционна работа)/)
      );
      expect(
        document.querySelectorAll('[data-testid^="delete-address-"]').length
      ).toBe(1);
    });

    test("clicking delete icon removes the selected address from the list", async () => {
      const mockProfile = createMockProfile(createMockEmployee(), [
        contactAddress,
        remoteWorkAddress,
      ]);
      mockEmployeesState = { currentProfile: mockProfile };

      render(
        <Addresses
          employeeName="Test Employee"
          shouldShowAdminEditButtons={false}
          shouldShowUserEditButtons={false}
        />
      );

      // Select deletable item (For remote work)
      fireEvent.click(
        screen.getByText(/(For remote work|За дистанционна работа)/)
      );

      const deleteButton = document.querySelector(
        '[data-testid^="delete-address-"]'
      ) as HTMLElement;
      expect(deleteButton).toBeInTheDocument();

      // Delete and verify it's removed
      await act(async () => {
        fireEvent.click(deleteButton);
      });
      await waitFor(() =>
        expect(
          screen.queryByText(/(For remote work|За дистанционна работа)/)
        ).not.toBeInTheDocument()
      );
      // 'For contact' remains
      expect(screen.getByText(/(For contact|За контакт)/)).toBeInTheDocument();
    });
  });
});
