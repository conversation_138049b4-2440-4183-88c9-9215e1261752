﻿<Project Sdk="Microsoft.NET.Sdk.Web">

  <PropertyGroup>
    <TargetFramework>net8.0</TargetFramework>
    <Nullable>enable</Nullable>
    <ImplicitUsings>enable</ImplicitUsings>
    <Configurations>Debug;Release;Docker;Testing</Configurations>
  </PropertyGroup>
<ItemGroup>
    <PackageReference Include="AutoMapper" Version="13.0.1" />
    <PackageReference Include="Microinvest.TransferFiles.Helpers" Version="2.6.1" />
  </ItemGroup>

  <ItemGroup Condition="'$(Configuration)'=='Debug'">
	  <ProjectReference Include="..\..\sso\SSO.Common\SSO.Common.csproj" />
	  <ProjectReference Include="..\..\WatchTower\watchtowerapi\WatchTower.Common\WatchTower.Common.csproj" />
	  <ProjectReference Include="..\..\worktimeapi\WorkTimeApi.Common\WorkTimeApi.Common.csproj" />
  </ItemGroup>
  <ItemGroup Condition="'$(Configuration)'!='Debug'">
	<PackageReference Include="SSO.Common" Version="0.0.8-preview.20250715114722" />
	<PackageReference Include="WatchTower.Common" Version="0.0.1-preview.20250917153352" />
	<PackageReference Include="WorkTimeApi.Common" Version="1.0.4-preview.20250919130621" />
  </ItemGroup>
  <ItemGroup>
  	<ProjectReference Include="..\Gateway.Common\Gateway.Common.csproj" />
  </ItemGroup>
</Project>
