interface ImageProps
  extends React.DetailedHTMLProps<
    React.ImgHTMLAttributes<HTMLImageElement>,
    HTMLImageElement
  > {
  size?: "mini" | "small" | "medium" | "large";
}

const Image = (props: ImageProps) => {
  const { alt, size, style } = props;

  let height = undefined;
  let width = undefined;

  if (size !== undefined) {
    switch (size) {
      case "mini":
        height = 1;
        width = 1;
        break;
      case "small":
        height = 2;
        width = 2;
        break;
      case "medium":
        height = 4;
        width = 4;
        break;
      case "large":
        height = 6;
        width = 6;
        break;
    }
  }

  return (
    <img
      alt={alt}
      style={{
        ...style,
        height: `${height ?? style?.height}rem`,
        width: `${width ?? style?.width}rem`,
      }}
      {...props}
    />
  );
};

export default Image;
