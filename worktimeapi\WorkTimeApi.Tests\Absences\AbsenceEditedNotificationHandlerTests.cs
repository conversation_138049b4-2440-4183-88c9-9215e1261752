using MediatR;
using Moq;
using WorkTime.Notifications.Services.Interfaces;
using WorkTimeApi.Common.DTOs.Absences;
using WorkTimeApi.Common.Enums;
using WorkTimeApi.Common.Notifications.Absences;
using WorkTimeApi.Handlers.Notifications.Absences.MainHandlers;

namespace WorkTimeApi.Tests.Absences;

public class AbsenceEditedNotificationHandlerTests
{
    private static AbsenceHospitalDTO CreateAbsence(AbsenceStatus status)
    {
        return new AbsenceHospitalDTO
        {
            Id = Guid.NewGuid(),
            FromDate = DateTime.UtcNow.Date,
            ToDate = DateTime.UtcNow.Date.AddDays(1),
            TypeIdentifier = EventType.ПлатенГодишенОтпуск,
            Status = status
        };
    }

    private static AbsenceHospitalDTO CreateOldAbsence()
    {
        return new AbsenceHospitalDTO
        {
            Id = Guid.NewGuid(),
            FromDate = DateTime.UtcNow.Date.AddDays(-1),
            ToDate = DateTime.UtcNow.Date,
            TypeIdentifier = EventType.ПлатенГодишенОтпуск,
            Status = AbsenceStatus.Pending,
            Reference = "Old comment"
        };
    }

    [Fact]
    public async Task Handle_DeletedByUserAfterApproval_Publishes_AbsenceEditedByEmployee()
    {
        var mediator = new Mock<IMediator>();
        var signalR = new Mock<ISignalRNotificationService>();
        var handler = new AbsenceEditedNotificationHandler(signalR.Object, mediator.Object);
        var notification = new AbsenceEditNotification(CreateAbsence(AbsenceStatus.DeletedByUserAfterApproval), Guid.NewGuid(), Guid.NewGuid(), "creator", Guid.NewGuid(), null, false);

        await handler.Handle(notification, CancellationToken.None);

        mediator.Verify(m => m.Publish(It.IsAny<AbsenceEditedByEmployeeNotification>(), It.IsAny<CancellationToken>()), Times.Once);
        signalR.VerifyNoOtherCalls();
    }

    [Fact]
    public async Task Handle_DeletedByAdmin_Publishes_AbsenceDeletedByAdmin()
    {
        var mediator = new Mock<IMediator>();
        var signalR = new Mock<ISignalRNotificationService>();
        var handler = new AbsenceEditedNotificationHandler(signalR.Object, mediator.Object);
        var notification = new AbsenceEditNotification(CreateAbsence(AbsenceStatus.DeletedByAdmin), Guid.NewGuid(), Guid.NewGuid(), "creator", Guid.NewGuid());

        await handler.Handle(notification, CancellationToken.None);

        mediator.Verify(m => m.Publish(It.IsAny<AbsenceDeletedByAdminNotification>(), It.IsAny<CancellationToken>()), Times.Once);
        signalR.VerifyNoOtherCalls();
    }

    [Fact]
    public async Task Handle_Deleted_Publishes_AbsenceDeleted()
    {
        var mediator = new Mock<IMediator>();
        var signalR = new Mock<ISignalRNotificationService>();
        var handler = new AbsenceEditedNotificationHandler(signalR.Object, mediator.Object);
        var notification = new AbsenceEditNotification(CreateAbsence(AbsenceStatus.Deleted), Guid.NewGuid(), Guid.NewGuid(), "creator", Guid.NewGuid());

        await handler.Handle(notification, CancellationToken.None);

        mediator.Verify(m => m.Publish(It.IsAny<AbsenceDeletedNotification>(), It.IsAny<CancellationToken>()), Times.Once);
        signalR.VerifyNoOtherCalls();
    }

    [Fact]
    public async Task Handle_Approved_Publishes_Confirm()
    {
        var mediator = new Mock<IMediator>();
        var signalR = new Mock<ISignalRNotificationService>();
        var handler = new AbsenceEditedNotificationHandler(signalR.Object, mediator.Object);
        var notification = new AbsenceEditNotification(CreateAbsence(AbsenceStatus.Approved), Guid.NewGuid(), Guid.NewGuid(), "creator", Guid.NewGuid());

        await handler.Handle(notification, CancellationToken.None);

        mediator.Verify(m => m.Publish(It.IsAny<AbsenceConfirmNotification>(), It.IsAny<CancellationToken>()), Times.Once);
        mediator.Verify(m => m.Publish(It.IsAny<AbsenceConfirmedEditedByEmployeeNotification>(), It.IsAny<CancellationToken>()), Times.Never);
        signalR.VerifyNoOtherCalls();
    }

    [Fact]
    public async Task Handle_Approved_AndActive_Publishes_Confirm_And_AdminsFlow()
    {
        var mediator = new Mock<IMediator>();
        var signalR = new Mock<ISignalRNotificationService>();
        var handler = new AbsenceEditedNotificationHandler(signalR.Object, mediator.Object);
        var notification = new AbsenceEditNotification(CreateAbsence(AbsenceStatus.Approved), Guid.NewGuid(), Guid.NewGuid(), "creator", Guid.NewGuid(), isActiveAbsence: true);

        await handler.Handle(notification, CancellationToken.None);

        mediator.Verify(m => m.Publish(It.IsAny<AbsenceConfirmNotification>(), It.IsAny<CancellationToken>()), Times.Never);
        mediator.Verify(m => m.Publish(It.IsAny<AbsenceConfirmedEditedByEmployeeNotification>(), It.IsAny<CancellationToken>()), Times.Once);
        signalR.VerifyNoOtherCalls();
    }

    [Fact]
    public async Task Handle_Approved_AndActive_WithOldAbsence_Publishes_AbsenceConfirmedEditedByEmployee_WithOldAbsence()
    {
        var mediator = new Mock<IMediator>();
        var signalR = new Mock<ISignalRNotificationService>();
        var handler = new AbsenceEditedNotificationHandler(signalR.Object, mediator.Object);

        var oldAbsence = CreateOldAbsence();
        var notification = new AbsenceEditNotification(
            CreateAbsence(AbsenceStatus.Approved),
            Guid.NewGuid(),
            Guid.NewGuid(),
            "creator",
            Guid.NewGuid(),
            oldAbsence,
            isActiveAbsence: true);

        await handler.Handle(notification, CancellationToken.None);

        mediator.Verify(m => m.Publish(It.Is<AbsenceConfirmedEditedByEmployeeNotification>(
            n => n.OldAbsence == oldAbsence), It.IsAny<CancellationToken>()), Times.Once);
        mediator.Verify(m => m.Publish(It.IsAny<AbsenceConfirmNotification>(), It.IsAny<CancellationToken>()), Times.Never);
        signalR.VerifyNoOtherCalls();
    }

    [Fact]
    public async Task Handle_Declined_Publishes_AbsenceDeclined()
    {
        var mediator = new Mock<IMediator>();
        var signalR = new Mock<ISignalRNotificationService>();
        var handler = new AbsenceEditedNotificationHandler(signalR.Object, mediator.Object);
        var notification = new AbsenceEditNotification(CreateAbsence(AbsenceStatus.Declined), Guid.NewGuid(), Guid.NewGuid(), "creator", Guid.NewGuid());

        await handler.Handle(notification, CancellationToken.None);

        mediator.Verify(m => m.Publish(It.IsAny<AbsenceDeclinedNotification>(), It.IsAny<CancellationToken>()), Times.Once);
        signalR.VerifyNoOtherCalls();
    }

    [Fact]
    public async Task Handle_EditedByAdmin_Publishes_AbsenceEditedByAdmin()
    {
        var mediator = new Mock<IMediator>();
        var signalR = new Mock<ISignalRNotificationService>();
        var handler = new AbsenceEditedNotificationHandler(signalR.Object, mediator.Object);
        var notification = new AbsenceEditNotification(CreateAbsence(AbsenceStatus.EditedByAdmin), Guid.NewGuid(), Guid.NewGuid(), "creator", Guid.NewGuid());

        await handler.Handle(notification, CancellationToken.None);

        mediator.Verify(m => m.Publish(It.IsAny<AbsenceEditedByAdminNotification>(), It.IsAny<CancellationToken>()), Times.Once);
        signalR.VerifyNoOtherCalls();
    }

    [Fact]
    public async Task Handle_EditedByAdmin_WithOldAbsence_Publishes_AbsenceEditedByAdmin_WithOldAbsence()
    {
        var mediator = new Mock<IMediator>();
        var signalR = new Mock<ISignalRNotificationService>();
        var handler = new AbsenceEditedNotificationHandler(signalR.Object, mediator.Object);

        var oldAbsence = CreateOldAbsence();
        var notification = new AbsenceEditNotification(
            CreateAbsence(AbsenceStatus.EditedByAdmin),
            Guid.NewGuid(),
            Guid.NewGuid(),
            "creator",
            Guid.NewGuid(),
            oldAbsence);

        await handler.Handle(notification, CancellationToken.None);

        mediator.Verify(m => m.Publish(It.Is<AbsenceEditedByAdminNotification>(
            n => n.OldAbsence == oldAbsence), It.IsAny<CancellationToken>()), Times.Once);
        signalR.VerifyNoOtherCalls();
    }

    [Fact]
    public async Task Handle_EditedByEmployee_Publishes_AbsenceEditedByEmployee()
    {
        var mediator = new Mock<IMediator>();
        var signalR = new Mock<ISignalRNotificationService>();
        var handler = new AbsenceEditedNotificationHandler(signalR.Object, mediator.Object);
        var notification = new AbsenceEditNotification(CreateAbsence(AbsenceStatus.EditedByEmployee), Guid.NewGuid(), Guid.NewGuid(), "creator", Guid.NewGuid(), isActiveAbsence: true);

        await handler.Handle(notification, CancellationToken.None);

        mediator.Verify(m => m.Publish(It.IsAny<AbsenceEditedByEmployeeNotification>(), It.IsAny<CancellationToken>()), Times.Once);
        signalR.VerifyNoOtherCalls();
    }

    [Fact]
    public async Task Handle_Default_Broadcasts_SignalR()
    {
        var mediator = new Mock<IMediator>();
        var signalR = new Mock<ISignalRNotificationService>();
        var handler = new AbsenceEditedNotificationHandler(signalR.Object, mediator.Object);
        var notification = new AbsenceEditNotification(CreateAbsence((AbsenceStatus)999), Guid.NewGuid(), Guid.NewGuid(), "creator", Guid.NewGuid());

        await handler.Handle(notification, CancellationToken.None);

        signalR.Verify(s => s.BroadcastAsync(notification), Times.Once);
        mediator.VerifyNoOtherCalls();
    }
}
