﻿using Microsoft.IdentityModel.Tokens;
using SSO.Services.Interfaces;
using System.IdentityModel.Tokens.Jwt;
using System.Security.Claims;
using System.Text;

namespace SSO.Services
{
    public class JWTValidator : IJWTValidator
    {
        private readonly IConfiguration _configuration;

        public JWTValidator(IConfiguration configuration)
        {
            _configuration = configuration;
        }

        public ClaimsPrincipal? ValidateToken(string? authToken)
        {
            if (authToken is null)
                return null;

            var tokenHandler = new JwtSecurityTokenHandler();
            var validationParameters = GetValidationParameters();

            try
            {
                return tokenHandler.ValidateToken(authToken, validationParameters, out _);
            }
            catch
            {
                return null;
            }
        }

        public IEnumerable<Claim>? ValidateGoogleToken(string? googleToken)
        {
            if (googleToken is null)
                return null;

            var tokenHandler = new JwtSecurityTokenHandler();
            var validationParameters = new TokenValidationParameters()
            {
                ValidateLifetime = true,
            };

            try
            {
                return tokenHandler.ReadJwtToken(googleToken)?.Claims;
            }
            catch
            {
                return null;
            }
        }

        private TokenValidationParameters GetValidationParameters()
        {
            return new TokenValidationParameters
            {
                ValidateLifetime = true,
                ValidateAudience = true,
                ValidateIssuer = true,
                ValidIssuer = _configuration["JwtIssuer"],
                ValidAudience = _configuration["JwtAudience"],
                IssuerSigningKey = new SymmetricSecurityKey(Encoding.UTF8.GetBytes(_configuration["JwtSecret"] ?? ""))
            };
        }
    }
}
