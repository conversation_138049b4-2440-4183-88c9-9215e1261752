﻿using AutoMapper;
using Moq;
using SSO.Database.Models;
using SSO.Database.Repositories.Interfaces;
using SSO.Mapper;
using SSO.Services;
using SSO.Services.Interfaces;

namespace SSO.Tests.SSO.Services
{
    public class RefreshTokenServiceTests
    {
        private const string USER_GUID_STR = "4c34a6a8-4b6a-496c-bcc1-5a249d487b91";
        private const string USER_EMAIL = "<EMAIL>";

        private readonly Mock<IRefreshTokenRepository> _refreshTokenRepositoryMock;
        private readonly Mock<IUsersRepository> _userRepositoryMock;

        public RefreshTokenServiceTests()
        {
            _refreshTokenRepositoryMock = new Mock<IRefreshTokenRepository>();
            _userRepositoryMock = new Mock<IUsersRepository>();
        }

        [Fact]
        public async Task AddRefreshTokenReturnsDTOWithId()
        {
            // Arrange
            var token = "refreshtokencontent";
            var id = Guid.NewGuid();

            var user = new User
            {
                Id = new Guid(USER_GUID_STR),
                Email = USER_EMAIL
            };

            var refreshToken = new RefreshToken
            {
                Id = id,
                Token = token,
                IsActive = true,
                User = user
            };

            _userRepositoryMock
                .Setup(ur => ur.FindUserByIdAsync(new Guid(USER_GUID_STR))).ReturnsAsync(user);

            _userRepositoryMock
                .Setup(ur => ur.UpdateUserAsnyc(user)).ReturnsAsync(new User
                {
                    Email = user.Email,
                    Id = user.Id,
                    RefreshTokens = new List<RefreshToken> { refreshToken }
                });

            var config = new MapperConfiguration(cfg => cfg.AddProfile<MapperProfile>());
            var mapper = config.CreateMapper();
            IRefreshTokensService refreshTokensService = new RefreshTokensService(_refreshTokenRepositoryMock.Object, _userRepositoryMock.Object, mapper);

            // Act
            var tokenDTO = await refreshTokensService.AddRefreshTokenAsync(token, new Guid(USER_GUID_STR), USER_EMAIL);

            // Assert
            Assert.NotNull(tokenDTO);
            Assert.Equal(token, tokenDTO.Token);
            Assert.True(tokenDTO.IsActive);
        }

        [Fact]
        public async Task CheckIfRefreshTokenIsActiveReturnsTrueForActiveToken()
        {
            // Arrange
            var token = "refreshtokencontent";
            var id = Guid.NewGuid();

            var user = new User
            {
                Id = new Guid(USER_GUID_STR),
                Email = USER_EMAIL
            };

            _userRepositoryMock
                .Setup(ur => ur.FindUserByIdAsync(new Guid(USER_GUID_STR))).ReturnsAsync(user);

            _refreshTokenRepositoryMock
                .Setup(rtr => rtr.GetByTokenAsync(token))
                .ReturnsAsync(new RefreshToken
                {
                    Id = id,
                    Token = token,
                    IsActive = true,
                    User = user
                });

            var config = new MapperConfiguration(cfg => cfg.AddProfile<MapperProfile>());
            var mapper = config.CreateMapper();
            IRefreshTokensService refreshTokensService = new RefreshTokensService(_refreshTokenRepositoryMock.Object, _userRepositoryMock.Object, mapper);

            // Act
            var isTokenActive = await refreshTokensService.CheckIfRefreshTokenIsActiveAsync(token);

            // Assert
            Assert.True(isTokenActive);
        }

        [Fact]
        public async Task CheckIfRefreshTokenIsActiveReturnsFalseForInactiveRefreshToken()
        {
            // Arrange
            var token = "refreshtokencontent";
            var id = Guid.NewGuid();

            var user = new User
            {
                Id = new Guid(USER_GUID_STR),
                Email = USER_EMAIL
            };

            _userRepositoryMock
                .Setup(ur => ur.FindUserByIdAsync(new Guid(USER_GUID_STR))).ReturnsAsync(user);

            _refreshTokenRepositoryMock
                .Setup(rtr => rtr.GetByTokenAsync(token))
                .ReturnsAsync(new RefreshToken
                {
                    Id = id,
                    Token = token,
                    IsActive = false,
                    User = user
                });

            var config = new MapperConfiguration(cfg => cfg.AddProfile<MapperProfile>());
            var mapper = config.CreateMapper();
            IRefreshTokensService refreshTokensService = new RefreshTokensService(_refreshTokenRepositoryMock.Object, _userRepositoryMock.Object, mapper);

            // Act
            var isTokenActive = await refreshTokensService.CheckIfRefreshTokenIsActiveAsync(token);

            // Assert
            Assert.False(isTokenActive);
        }
    }
}
