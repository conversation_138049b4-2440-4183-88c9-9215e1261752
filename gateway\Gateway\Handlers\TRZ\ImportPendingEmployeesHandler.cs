﻿using Gateway.Common.Results;
using Gateway.Connections.Interfaces;
using MediatR;
using WorkTimeApi.Common.Requests.TRZ;

namespace Gateway.Handlers.TRZ
{
    public class ImportPendingEmployeesHandler : IRequestHandler<ImportPendingEmployeesRequest, IResult>
    {
        private readonly IWorkTimeApiConnection _workTimeApiConnection;

        public ImportPendingEmployeesHandler(IWorkTimeApiConnection workTimeApiConnection)
        {
            _workTimeApiConnection = workTimeApiConnection;
        }

        public async Task<IResult> Handle(ImportPendingEmployeesRequest request, CancellationToken cancellationToken)
        {
            var importPendingEmployeePayrollsResponse = await _workTimeApiConnection.ImportPendingEmployeesAsync(request);

            return new HttpResponseMessageResult(importPendingEmployeePayrollsResponse);
        }
    }
}
