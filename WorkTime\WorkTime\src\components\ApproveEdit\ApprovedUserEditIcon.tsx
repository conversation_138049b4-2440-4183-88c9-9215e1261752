import React from "react";
import styled from "styled-components";
import approvedHoverIcon from "../../assets/images/dot-icons/approved-hover.svg";
import approvedIcon from "../../assets/images/dot-icons/approved.svg";

const IconContainer = styled.div`
  position: relative;
  display: inline-block;
  margin-left: 0.5rem;
`;

const HoverIcon = styled.div`
  width: 1.5rem;
  height: 1.5rem;
  cursor: pointer;
  background: url(${approvedIcon}) center/contain no-repeat;
  transition: all 0.2s ease;

  &:hover,
  ${IconContainer}:hover & {
    background: url(${approvedHoverIcon}) center/contain no-repeat;
  }
`;

interface ApprovedUserEditIconProps {
  onDelete?: () => void;
}

const ApprovedUserEditIcon: React.FC<ApprovedUserEditIconProps> = ({
  onDelete,
}) => {
  const handleClick = () => {
    if (onDelete) {
      onDelete();
    }
  };

  return (
    <IconContainer data-testid="approved-user-edit-icon">
      <HoverIcon data-testid="hover-icon" onClick={handleClick} />
    </IconContainer>
  );
};

export default ApprovedUserEditIcon;
