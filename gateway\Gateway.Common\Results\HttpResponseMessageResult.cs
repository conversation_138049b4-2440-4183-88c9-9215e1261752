﻿using Microsoft.AspNetCore.Http;
using Microsoft.Net.Http.Headers;
using System.Text.Json;

namespace Gateway.Common.Results
{
    public class HttpResponseMessageResult : IResult
    {
        private readonly HttpResponseMessage _responseMessage;

        public HttpResponseMessageResult(HttpResponseMessage responseMessage)
        {
            _responseMessage = responseMessage;
        }

        public async Task ExecuteAsync(HttpContext context)
        {
            var response = context.Response;
            response.StatusCode = (int)_responseMessage.StatusCode;

            foreach (var header in _responseMessage.Headers)
            {
                response.Headers[header.Key] = header.Value.ToArray();
            }

            foreach (var header in _responseMessage.Content.Headers)
            {
                if (header.Key != HeaderNames.ContentLength && header.Key != "Transfer-Encoding")
                {
                    response.Headers[header.Key] = header.Value.ToArray();
                }
            }

            response.Headers.Remove("Transfer-Encoding");

            if (_responseMessage.Content != null)
            {
                var contentString = await _responseMessage.Content.ReadAsStringAsync();
                response.ContentType = "application/json";

                if (!string.IsNullOrWhiteSpace(contentString) && IsValidJson(contentString))
                {
                    using var jsonDoc = JsonDocument.Parse(contentString);
                    var options = new JsonWriterOptions
                    {
                        Indented = true
                    };

                    using var memoryStream = new MemoryStream();
                    using var writer = new Utf8JsonWriter(memoryStream, options);

                    if (jsonDoc.RootElement.ValueKind == JsonValueKind.Array)
                    {
                        writer.WriteStartArray();

                        foreach (var property in jsonDoc.RootElement.EnumerateArray())
                        {
                            if (property.ValueKind == JsonValueKind.Object)
                            {
                                writer.WriteStartObject();

                                foreach (var item in property.EnumerateObject())
                                {
                                    var propertyName = JsonNamingPolicy.CamelCase.ConvertName(item.Name);
                                    writer.WritePropertyName(propertyName);
                                    item.Value.WriteTo(writer);
                                }

                                writer.WriteEndObject();
                            }
                            else
                            {
                                property.WriteTo(writer);
                            }
                        }

                        writer.WriteEndArray();
                    }
                    else if (jsonDoc.RootElement.ValueKind == JsonValueKind.Object)
                    {
                        writer.WriteStartObject();

                        foreach (var item in jsonDoc.RootElement.EnumerateObject())
                        {
                            var propertyName = JsonNamingPolicy.CamelCase.ConvertName(item.Name);
                            writer.WritePropertyName(propertyName);
                            item.Value.WriteTo(writer);
                        }

                        writer.WriteEndObject();
                    }

                    writer.Flush();

                    response.Headers[HeaderNames.ContentLength] = memoryStream.Length.ToString();
                    memoryStream.Seek(0, SeekOrigin.Begin);

                    await memoryStream.CopyToAsync(response.Body);
                }
            }
        }

        private bool IsValidJson(string strInput)
        {
            strInput = strInput.Trim();
            if ((strInput.StartsWith("{") && strInput.EndsWith("}")) ||
                (strInput.StartsWith("[") && strInput.EndsWith("]")))
            {
                try
                {
                    var obj = JsonDocument.Parse(strInput);
                    return true;
                }
                catch (Exception)
                {
                    return false;
                }
            }

            return false;
        }
    }
}
