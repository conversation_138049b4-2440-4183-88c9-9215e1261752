import { render, fireEvent, screen } from "@testing-library/react";
import { ViewMode } from "../../../models/Enums/ViewMode";
import MonthsElement from "../MonthElement";
import "@testing-library/jest-dom";

describe("<MonthsElement />", () => {
  it("should display months if active prop is MonthsView", () => {
    render(
      <MonthsElement
        handleMonthsClick={jest.fn()}
        active={ViewMode.MonthsView}
      />
    );
    const monthContainer = screen.getByTestId("months-container");
    expect(monthContainer).toBeInTheDocument();
  });

  it("should call handleMonthsClick with correct month index when a month is clicked", () => {
    const mockHandleMonthsClick = jest.fn();
    render(
      <MonthsElement
        handleMonthsClick={mockHandleMonthsClick}
        active={ViewMode.MonthsView}
      />
    );

    const month = screen.getByText("Яну");
    fireEvent.click(month);
    expect(mockHandleMonthsClick).toHaveBeenCalledWith(0);
  });
});
