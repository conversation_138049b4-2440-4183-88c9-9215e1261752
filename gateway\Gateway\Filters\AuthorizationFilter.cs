﻿using Gateway.Common.Globals;
using Gateway.Connections.Interfaces;
using WorkTimeApi.Common.DTOs.Users;

namespace Gateway.Filters
{
    public class AuthorizationFilter(string[]? requiredPermissions) : IEndpointFilter
    {

        public async ValueTask<object?> InvokeAsync(EndpointFilterInvocationContext context, EndpointFilterDelegate next)
        {
            var serviceProider = context.HttpContext.RequestServices;
            var httpContextAccessor = serviceProider.GetService<IHttpContextAccessor>();
            var worktimeConnection = serviceProider.GetService<IWorkTimeApiConnection>();
            var globalUser = serviceProider.GetService<GlobalUser>();
            var globalEmployee = serviceProider.GetService<GlobalEmployee>();

            if (httpContextAccessor is null || httpContextAccessor.HttpContext is null || worktimeConnection is null)
                return TypedResults.Unauthorized();

            var refreshToken = httpContextAccessor.HttpContext.Response.Headers["refresh-token"].ToString();
            if (refreshToken is null)
                return TypedResults.Unauthorized();

            var companyUserRegistrationId = httpContextAccessor.HttpContext.Request.Headers["company-id"].ToString();
            if (Guid.TryParse(companyUserRegistrationId, out var companyId))
            {
                var permissionsResponse = await worktimeConnection.GetUserPermissionsAsync(globalUser.Id, companyId);
                if (!permissionsResponse.IsSuccessStatusCode)
                    return TypedResults.Unauthorized();

                var userPermissions = await permissionsResponse.Content.ReadFromJsonAsync<UserEmployeePermissionsDTO>();
                if (userPermissions is null || userPermissions.EmployeeId is null || userPermissions.Permissions is null)
                    return TypedResults.Unauthorized();

                globalEmployee.Id = userPermissions.EmployeeId.Value;
                globalEmployee.CompanyId = companyId;
                globalEmployee.Permissions = [.. userPermissions.Permissions];

                bool isPermitted = requiredPermissions is null || !requiredPermissions.Any()
                    || requiredPermissions.Any(requiredPermission => globalEmployee.Permissions.Contains(requiredPermission));
                if (!isPermitted)
                    return TypedResults.Unauthorized();
            }

            return await next.Invoke(context);
        }
    }
}
