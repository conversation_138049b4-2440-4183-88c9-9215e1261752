﻿using Gateway.Common.Models;
using Microsoft.AspNetCore.Http;
using Microsoft.Extensions.Logging;

namespace Gateway.Common.Middlewares.Security
{
    public class CheckAllowedOriginIpsMiddleware
    {
        private readonly RequestDelegate _next;
        private readonly OriginSettings _originSettings;
        private readonly ILogger<CheckAllowedOriginIpsMiddleware> _logger;

        public CheckAllowedOriginIpsMiddleware(RequestDelegate next, 
            OriginSettings originSettings,
            ILogger<CheckAllowedOriginIpsMiddleware> logger)
        {
            _next = next;
            _originSettings = originSettings;
            _logger = logger;
        }

        public async Task InvokeAsync(HttpContext context)
        {
            if (_originSettings.AllowedEndpoints.Any(path => context.Request.Path.StartsWithSegments(path, StringComparison.OrdinalIgnoreCase)))
            {
                await _next(context);
                return;
            }

            var allowedOriginIps = _originSettings.AllowedOriginIps;
            var remoteIp = GetRequestIpAddress(context);

            if (allowedOriginIps.Count == 0)
            {
                await _next(context);
            }
            else if (allowedOriginIps.Contains(remoteIp))
            {
                await _next(context);
            }
            else
            {
                context.Response.StatusCode = StatusCodes.Status403Forbidden;
                return;
            }
        }

        private string GetRequestIpAddress(HttpContext context)
        {
            _logger.LogInformation("Requester's IP address is {ip-address}", context.Connection.RemoteIpAddress?.ToString());

            if (context.Request.Headers.TryGetValue("X-Forwarded-For", out var forwardedHeader))
            {
                return forwardedHeader.ToString();
            }

            return context.Connection.RemoteIpAddress?.ToString() ?? "Invalid IP Address";
        }
    }
}
