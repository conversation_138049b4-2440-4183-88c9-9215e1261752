﻿using WorkTimeApi.Common.DTOs.Companies;
using WorkTimeApi.Common.Requests.Absences;
using WorkTimeApi.Common.Requests.Companies;
using WorkTimeApi.Common.Requests.Employees;
using WorkTimeApi.Common.Requests.Notifications;
using WorkTimeApi.Common.Requests.OnboardingDocuments;
using WorkTimeApi.Common.Requests.Payrolls;
using WorkTimeApi.Common.Requests.Roles;
using WorkTimeApi.Common.Requests.StructureLevels;
using WorkTimeApi.Common.Requests.Templates;
using WorkTimeApi.Common.Requests.TRZ;
using WorkTimeApi.Common.Requests.Users;

namespace Gateway.Connections.Interfaces
{
    public interface IWorkTimeApiConnection
    {
        Task<HttpResponseMessage> AddPayrollAsync(AddPayrollRequest basicPayrollRequest);

        Task<HttpResponseMessage> DeletePayrollAsync(DeletePayrollRequest deletePayrollRequest);

        Task<HttpResponseMessage> AddTemplateAsync(AddTemplateRequest addTemplatesRequest);

        Task<HttpResponseMessage> LoadTemplatesAsync(LoadTemplatesRequest loadTemplateRequest);

        Task<HttpResponseMessage> AddOnboardingDocumentAsync(AddOnboardingDocumentRequest addOnboardingDocumentRequest);

        Task<HttpResponseMessage> GetCompaniesAsync(GetCompaniesRequest getCompaniesRequest);

        Task<HttpResponseMessage> CreateCompanyAsync(CreateCompanyRequest createCompanyRequest);

        Task<HttpResponseMessage> AddStructureLevelAsync(AddStructureLevelRequest addStructureLevelRequest);

        Task<HttpResponseMessage> GetStructureLevelsAsync(Guid companyId);

        Task<HttpResponseMessage> DeleteStructureLevelAsync(Guid structureLevelId);

        Task<HttpResponseMessage> AddEmployeeAsync(AddEmployeeRequest content);

        Task<HttpResponseMessage> CreateEmployeeForCompanyAsync(CreateEmployeeForCompanyRequest createEmployeeRequest);

        Task<HttpResponseMessage> AddEmployeeCompanyAsync(AddEmployeeCompanyRequest addEmployeeCompanyRequest);

        Task<HttpResponseMessage> GetCompanyByIdAsync(Guid companyId);

        Task<HttpResponseMessage> UpdateUserHasSignedInAsync(UpdateUserHasSignedInRequest updateUserHasSignedInRequest);

        Task<HttpResponseMessage> LoadEmployeesAsync(LoadEmployeesRequest loadEmployeesRequest);

        Task<HttpResponseMessage> LoadCoworkersAsync(LoadCoworkersRequest loadEmployeesRequest);

        Task<HttpResponseMessage> GetUserAsync(GetUserRequest loadUserRequest);

        Task<HttpResponseMessage> LoadEmployeePayrollListAsync(LoadEmployeePayrollRequest loadEmployeePayrollRequest);

        Task<HttpResponseMessage> LoadPendingEmployeePayrollListAsync(LoadPendingEmployeePayrollRequest loadEmployeePayrollRequest);

        Task<HttpResponseMessage> GetContractsAsync();

        Task<HttpResponseMessage> GetTerminationReasonAsync();

        Task<HttpResponseMessage> GetCountriesAsync();

        Task<HttpResponseMessage> GetIncomeTypesAsync();

        Task<HttpResponseMessage> GetQualificationGroupsAsync();

        Task<HttpResponseMessage> GetTZPBsAsync();

        Task<HttpResponseMessage> GetNKPDsAsync();

        Task<HttpResponseMessage> ImportCompanyAsync(ImportTRZCompanyRequest importCompanyRequest);

        Task<HttpResponseMessage> GetDistrictsAsync();

        Task<HttpResponseMessage> GetMunicipalitiesAsync();

        Task<HttpResponseMessage> GetCitiesAsync();

        Task<HttpResponseMessage> AddTRZEmployeesAsync(AddTRZEmployeesRequest content);

        Task<HttpResponseMessage> UpdateTRZDepartmentsAsync(UpdateTRZDepartmentsRequest request);

        Task<HttpResponseMessage> GetMODsAsync();

        Task<HttpResponseMessage> GetKidsAsync();

        Task<HttpResponseMessage> AddTRZPendingEventsAsync(AddTRZPendingEventsRequest addTRZPendingEventsRequest);

        Task<HttpResponseMessage> AddTRZEventsAsync(AddTRZEventsRequest addTRZAbsencesRequest);

        Task<HttpResponseMessage> GetTRZEventsAsync(GetTRZEventsRequest getTRZEventsRequest);

        Task<HttpResponseMessage> ImportTRZEventAsync(ImportTRZEventRequest importTRZEventRequest);

        Task<HttpResponseMessage> GetAbsenceTypesAsync();

        Task<HttpResponseMessage> GetHospitalTypesAsync();

        Task<HttpResponseMessage> GetTRZEmployeesAsync(GetTRZEmployeesRequest getTRZEmployeesRequest);

        Task<HttpResponseMessage> ImportPendingEmployeesAsync(ImportPendingEmployeesRequest importPendingEmployeesRequest);

        Task<HttpResponseMessage> GetAbsencesAndHospitalsAsync(AbsencesAndHospitalsRequest absencesAndHospitalsRequest);

        Task<HttpResponseMessage> UpdateCompanyInvitationAsync(CompanyInvitationRequest companyInvitationRequest);

        Task<HttpResponseMessage> GetCompanyByEmailAndBulstatAsync(string email, string bulstat);

        Task<HttpResponseMessage> EditCompanyAsync(EditCompanyRequest editedCompanyResponse);

        Task<HttpResponseMessage> GetGeneralRolesAsync();

        Task<HttpResponseMessage> GetEmployeesByCompanyIdAndPermissionAsync(GetEmployeesByCompanyIdAndPermissionRequest getEmployeesByCompanyIdAndPermissionRequest);

        Task<HttpResponseMessage> GetWorkTimeRoleAsync(GetWorkTimeRoleRequest getWorkTimeRoleRequest);

        Task<HttpResponseMessage> SetWorkTimeRoleAsync(SetWorkTimeRoleRequest request);

        Task<HttpResponseMessage> ChangeEmployeeRoleAsync(ChangeEmployeeRoleRequest request);

        Task<HttpResponseMessage> ShareCompanyAsync(ShareCompanyRequest addRoleToEmployee);

        Task<HttpResponseMessage> AddUserWithEmployeeAsync(AddUserWithEmployeeRequest addUserRequest);

        Task<HttpResponseMessage> AddUserAsync(AddUserRequest addUserRequest);

        Task<HttpResponseMessage> LoadUserEmployeeAsync(LoadUserEmployeeRequest loadUserEmployeeRequest);

        Task<HttpResponseMessage> GetUserEmployeePermissionsAsync(Guid userId, Guid companyId);

        Task<HttpResponseMessage> GetUserPermissionsAsync(Guid userId, Guid companyId);

        Task<HttpResponseMessage> AddNewEmployeePayrollAsync(AddNewEmployeePayrollRequest addNewEmployeePayrollRequest);

        Task<HttpResponseMessage> LoadAllUserNotificationsAsync(GetAllUserNotifications getNotificationsRequest);

        Task<HttpResponseMessage> ReadNotificationAsync(ReadNotificationRequest readNotificationRequest);

        Task<HttpResponseMessage> UpdateUserEmailAsync(UpdateUserEmailRequest updateUserEmailRequest);

        Task<HttpResponseMessage> ConfirmEmailByCodeAsync(string email, string code);
    }
}
