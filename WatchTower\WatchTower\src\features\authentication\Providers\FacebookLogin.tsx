import { useContext } from "react";
import { facebookLogin } from "../../../services/authentication/authenticationService";
import OAuthButton from "../../../components/Inputs/OAuthButton";
import FacebookLogo from "../../../assets/images/logos/facebook-logo.png";
import { useNavigate } from "react-router-dom";
import { AuthContext } from "../AuthContext";

interface Props {
  returnAfterLogin?: string;
}

const FacebookLogin = ({ returnAfterLogin }: Props) => {
  const navigate = useNavigate();
  const { setUser } = useContext(AuthContext);

  const handleFacebookLogin = () => {
    FB.login(
      function (response: any) {
        if (response.authResponse) {
          facebookLogin(response.authResponse.accessToken).then((email) => {
            if (email) {
              setUser({ email: email, hasSignedIn: true });
              navigate(returnAfterLogin ?? "/");
            }
          });
        }
      },
      { scope: "email" }
    );
  };

  return (
    <OAuthButton
      logo={FacebookLogo}
      content="Sign In with Facebook account"
      onClick={handleFacebookLogin}
    />
  );
};

export default FacebookLogin;
