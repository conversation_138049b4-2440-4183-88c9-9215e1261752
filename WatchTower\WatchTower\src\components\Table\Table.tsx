import styled from "styled-components";
import { IEntity } from "../../models/DTOs/IEntity";
import TableHeader from "./TableHeader";
import TableRows from "./TableRow";
import TableFooter from "./TableFooter";
import { useState } from "react";
import useTable from "./useTable";

export type ColumnDefinitionType<T, K extends keyof T> = {
  key: K;
  value: string;
};

interface TableProps<T, K extends keyof T>
  extends React.DetailedHTMLProps<
    React.TableHTMLAttributes<HTMLTableElement>,
    HTMLTableElement
  > {
  data: Array<T>;
  columns: Array<ColumnDefinitionType<T, K>>;
  handleDelete(idToDelete: string): Promise<void>;
  filterColumns: (columnKey: string, searchText: string) => void;
}

const TableWrapper = styled.table`
  border-collapse: collapse;
  margin-top: 1.25rem;
`;

const Wrapper = styled.div`
  display: table;
  margin-left: auto;
  margin-right: auto;
`;

const Table = <T extends IEntity, K extends keyof T>({
  data,
  columns,
  handleDelete,
  filterColumns,
}: TableProps<T, K>): JSX.Element => {
  const [page, setPage] = useState(1);
  const { slice, range, setSlice } = useTable(data, page, 15);

  return (
    <Wrapper>
      <TableWrapper>
        <TableHeader
          columns={columns}
          data={slice}
          filterColumns={filterColumns}
          setTableData={setSlice}
        />
        <TableRows data={slice} columns={columns} handleDelete={handleDelete} />
      </TableWrapper>
      <TableFooter
        range={range}
        slice={slice}
        setPage={setPage}
        page={page}
        setCurrentPage={setPage}
      />
    </Wrapper>
  );
};

export default Table;
