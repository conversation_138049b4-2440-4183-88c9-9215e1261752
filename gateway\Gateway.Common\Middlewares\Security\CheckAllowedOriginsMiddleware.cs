﻿using Gateway.Common.Models;
using Microsoft.AspNetCore.Http;

namespace Gateway.Common.Middlewares.Security
{
    public class CheckAllowedOriginsMiddleware
    {
        private readonly RequestDelegate _next;
        private readonly OriginSettings _originSettings;

        public CheckAllowedOriginsMiddleware(RequestDelegate next, OriginSettings originSettings)
        {
            _next = next;
            _originSettings = originSettings;
        }

        public async Task InvokeAsync(HttpContext context)
        {
            if (_originSettings.AllowedEndpoints.Any(path => context.Request.Path.StartsWithSegments(path, StringComparison.OrdinalIgnoreCase)))
            {
                await _next(context);
                return;
            }

            var allowedOrigins = _originSettings.AllowedOrigins;

            if (allowedOrigins.Count == 0)
            { 
                await _next(context);
            }
            else if (context.Request.Headers.TryGetValue("Origin", out var origin)
                && allowedOrigins.Contains(origin.ToString()))
            {
                await _next(context);
            }
            else
            {
                context.Response.StatusCode = StatusCodes.Status403Forbidden;
                return;
            }
        }
    }
}
