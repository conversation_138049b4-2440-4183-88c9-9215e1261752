﻿using System;
using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

#pragma warning disable CA1814 // Prefer jagged arrays over multidimensional

namespace SSO.Database.Migrations
{
    /// <inheritdoc />
    public partial class SupportChatPermissions : Migration
    {
        /// <inheritdoc />
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.InsertData(
                table: "Permissions",
                columns: new[] { "Id", "Name" },
                values: new object[,]
                {
                    { new Guid("38e30952-737d-3746-6792-0bea04e26f6d"), "SupportChatService.Users" },
                    { new Guid("bdc4a978-a9f4-66df-cace-520dd9889f13"), "SupportChatService.Chats" }
                });
        }

        /// <inheritdoc />
        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DeleteData(
                table: "Permissions",
                keyColumn: "Id",
                keyValue: new Guid("38e30952-737d-3746-6792-0bea04e26f6d"));

            migrationBuilder.DeleteData(
                table: "Permissions",
                keyColumn: "Id",
                keyValue: new Guid("bdc4a978-a9f4-66df-cace-520dd9889f13"));
        }
    }
}
