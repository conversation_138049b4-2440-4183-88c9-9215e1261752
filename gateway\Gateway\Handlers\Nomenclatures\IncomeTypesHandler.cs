﻿using Gateway.Common.Results;
using Gateway.Connections.Interfaces;
using MediatR;
using WorkTimeApi.Common.Requests.Nomenclatures;

namespace Gateway.Handlers.Nomenclatures
{
	public class IncomeTypesHandler : IRequestHandler<GetIncomeTypesRequest, IResult>
	{
		private readonly IWorkTimeApiConnection _workTimeApiConnection;

		public IncomeTypesHandler(IWorkTimeApiConnection workTimeApiConnection)
		{
			_workTimeApiConnection = workTimeApiConnection;
		}

		public async Task<IResult> Handle(GetIncomeTypesRequest request, CancellationToken cancellationToken)
		{
			var incomeTypes = await _workTimeApiConnection.GetIncomeTypesAsync();

			return new HttpResponseMessageResult(incomeTypes);
		}
	}
}
