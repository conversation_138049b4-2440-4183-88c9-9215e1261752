﻿namespace Gateway.Connections.Handlers
{
    public class AddOriginHeaderHandler : DelegatingHandler
    {
        protected override async Task<HttpResponseMessage> SendAsync(HttpRequestMessage request, CancellationToken cancellationToken)
        {
            if (!request.Headers.Contains("Origin"))
            {
                request.Headers.Add("Origin", "WorkTime.Gateway");
            }

            return await base.SendAsync(request, cancellationToken);
        }
    }
}