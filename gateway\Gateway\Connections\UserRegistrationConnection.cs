﻿using Gateway.Common.Globals;
using Gateway.Connections.Interfaces;
using Microinvest.TransferFiles.Tools.Models.Users;
using System.Net.Http.Headers;
using System.Text;
using UR = Microinvest.TransferFiles.Tools.Models.Companies;

namespace Gateway.Connections
{
	public class UserRegistrationConnection : IUserRegistrationConnection
	{
		private readonly HttpClient _httpClient;
		private readonly GlobalUser _globalUser;

		public UserRegistrationConnection(IConfiguration configuration, HttpClient httpClient, GlobalUser globalUser)
		{
			_httpClient = httpClient;
			_globalUser = globalUser;
			_httpClient.BaseAddress = new Uri(configuration["UserRegistrationsBaseUrl"]!);
		}

		public Task<HttpResponseMessage> RegisterWithTemplateAsync(RegistrationWithTemplateDTO registrationWithTemplateDTO)
		{
			return _httpClient.PostAsJsonAsync("api/users/register-with-template", registrationWithTemplateDTO);
		}

		public Task<HttpResponseMessage> ConfirmEmailAsync(ConfirmEmailDTO confirmEmailDTO)
		{
			return _httpClient.PostAsJsonAsync("api/users/confirm-email-by-code", confirmEmailDTO);
		}

		public Task<HttpResponseMessage> CreateNewCompanyAsync(UR.CompanyDTO companyDTO)
		{
			var bytes = Encoding.UTF8.GetBytes(_globalUser.Email);
			_httpClient.DefaultRequestHeaders.Authorization = new AuthenticationHeaderValue("Basic", Convert.ToBase64String(bytes));
			_httpClient.DefaultRequestHeaders.Add("CompanyName", Convert.ToBase64String(Encoding.UTF8.GetBytes(companyDTO.Name ?? "")));
			_httpClient.DefaultRequestHeaders.Add("CompanyEIK", Convert.ToBase64String(Encoding.UTF8.GetBytes(companyDTO.Bulstat ?? "")));
            _httpClient.DefaultRequestHeaders.Add("ContractName", Convert.ToBase64String(Encoding.UTF8.GetBytes(companyDTO.ContactName ?? "")));

			return _httpClient.PostAsync($"api/companies/AddCompanyIfNotExist", null);
		}

		public Task<HttpResponseMessage> ApproveEmployeeForCompany(string email, string bulstat)
		{
			_httpClient.DefaultRequestHeaders.Authorization = new AuthenticationHeaderValue("Basic", Convert.ToBase64String(Encoding.UTF8.GetBytes(email)));
			_httpClient.DefaultRequestHeaders.Add("CompanyEIK", Convert.ToBase64String(Encoding.UTF8.GetBytes(bulstat)));

			return _httpClient.PostAsync($"api/companies/ApproveEmployeeForCompany?employeeEmail={email}", null);
		}

		public Task<HttpResponseMessage> ConfirmedRegisterEmployeeByEmailAsync(UserDTO userDTO)
		{
			return _httpClient.PostAsJsonAsync("api/users/email-register-employee", userDTO);
        } 

        public Task<HttpResponseMessage> GetSenderaCompanyUsersAsync(string email, string companyEIK)
        {
            return _httpClient.GetAsync($"api/companies/company-users?email={email}&companyEIK={companyEIK}");
        }        

        public Task<HttpResponseMessage> GetSenderaUserAsync(string email)
		{
			_httpClient.DefaultRequestHeaders.Authorization = new AuthenticationHeaderValue("Basic", Convert.ToBase64String(Encoding.UTF8.GetBytes(email)));
			_httpClient.DefaultRequestHeaders.Add("UserToJoin", Convert.ToBase64String(Encoding.UTF8.GetBytes(email)));
			return _httpClient.GetAsync($"api/users/userByEmail");
		}

		public Task<HttpResponseMessage> EditCompanyAsync(UR.CompanyDTO companyDTO)
		{
			return _httpClient.PutAsJsonAsync("api/companies", companyDTO);
		}

        public Task<HttpResponseMessage> ChangePasswordAsync(UserDTO userDTO)
        {
            return _httpClient.PostAsJsonAsync($"api/users/change-password-by-token", userDTO);
        }

        public Task<HttpResponseMessage> GetSenderaUserByIdAsync(string userId)
        {
            _httpClient.DefaultRequestHeaders.Authorization = new AuthenticationHeaderValue("Basic", Convert.ToBase64String(Encoding.UTF8.GetBytes(userId)));
            _httpClient.DefaultRequestHeaders.Add("SenderaUser", Convert.ToBase64String(Encoding.UTF8.GetBytes(userId)));
            return _httpClient.GetAsync($"api/users/sendera-user");
        }

        public Task<HttpResponseMessage> GetIsUserValidAsync(string email,string password)
        {
            _httpClient.DefaultRequestHeaders.Authorization = new AuthenticationHeaderValue("Basic", Convert.ToBase64String(Encoding.UTF8.GetBytes($"{email}:{password}")));
            return _httpClient.GetAsync($"api/users/isuservalid");
        }

        public Task<HttpResponseMessage> EditUserDataAsync(UserDTO userDTO)
        {
            return _httpClient.PostAsJsonAsync($"api/users/edit-user-data", userDTO);
        }

        public Task<HttpResponseMessage> RegisterUserByCodeAsync(UserDTO userDTO)
        {
            return _httpClient.PostAsJsonAsync($"api/users/register-by-code",userDTO);
        }

        public Task<HttpResponseMessage> GetUserByUserNameAsync(string userName)
        {
            return _httpClient.GetAsync($"api/users/user-by-userName?userName={userName}");
        }

        public Task<HttpResponseMessage> ConfirmEmailByCodeAsync(string email, string code)
		{
            return _httpClient.GetAsync($"api/users/confirm-email-by-worktime-code?email={email}&code={code}");
        }
    }
}
