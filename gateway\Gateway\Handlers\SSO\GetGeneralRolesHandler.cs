using Gateway.Connections.Interfaces;
using MediatR;
using WorkTimeApi.Common.DTOs.Roles;
using WorkTimeApi.Common.Requests.Roles;

namespace Gateway.Handlers.SSO
{
    public class GetGeneralRolesHandler(IWorkTimeApiConnection worktimeConnection) : IRequestHandler<GetGeneralRolesRequest, IResult>
    {
        public async Task<IResult> Handle(GetGeneralRolesRequest request, CancellationToken cancellationToken)
        {
            var response = await worktimeConnection.GetGeneralRolesAsync();

            if (!response.IsSuccessStatusCode)
                return Results.BadRequest(response.Content);

            var roles = await response.Content.ReadFromJsonAsync<List<RoleDTO>>(cancellationToken: cancellationToken);

            return Results.Ok(roles);
        }
    }
}