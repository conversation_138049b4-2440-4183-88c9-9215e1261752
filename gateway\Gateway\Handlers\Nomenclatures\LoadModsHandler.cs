﻿using Gateway.Connections.Interfaces;
using MediatR;
using WorkTimeApi.Common.Requests.Nomenclatures;

namespace Gateway.Handlers.Nomenclatures
{
    public class LoadModsHandler : IRequestHandler<GetModsRequest, IResult>
    {
        private readonly IWorkTimeApiConnection _workTimeApiConnection;

        public LoadModsHandler(IWorkTimeApiConnection workTimeApiConnection)
        {
            _workTimeApiConnection = workTimeApiConnection;
        }

        public async Task<IResult> Handle(GetModsRequest request, CancellationToken cancellationToken)
        {
            var response = await _workTimeApiConnection.GetMODsAsync();

            if (!response.IsSuccessStatusCode)
                return Results.StatusCode((int)response.StatusCode);

            return Results.Text(await response.Content.ReadAsStringAsync(cancellationToken));
        }
    }
}

