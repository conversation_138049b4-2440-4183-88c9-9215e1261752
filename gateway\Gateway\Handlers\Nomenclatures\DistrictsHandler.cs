﻿using Gateway.Common.Results;
using Gateway.Connections.Interfaces;
using MediatR;
using WorkTimeApi.Common.Requests.Nomenclatures;

namespace Gateway.Handlers.Nomenclatures
{
    public class DistrictsHandler : IRequestHandler<GetDistrictsRequest, IResult>
    {
        private readonly IWorkTimeApiConnection _workTimeApiConnection;

        public DistrictsHandler(IWorkTimeApiConnection workTimeApiConnection)
        {
            _workTimeApiConnection = workTimeApiConnection;
        }

        public async Task<IResult> Handle(GetDistrictsRequest request, CancellationToken cancellationToken)
        {
            var districts = await _workTimeApiConnection.GetDistrictsAsync();
            return new HttpResponseMessageResult(districts);
        }
    }
}