﻿using MediatR;
using WorkTimeApi.Common.Requests.Payrolls;
using WorkTimeApi.Services.Interfaces.Payrolls;

namespace WorkTimeApi.Handlers.Payrolls
{
    public class LoadPayrollDataHandler(IPayrollsService payrollService) : IRequestHandler<LoadPayrollDataRequest, IResult>
    {
        public async Task<IResult> Handle(LoadPayrollDataRequest request, CancellationToken cancellationToken)
        {
            var result = await payrollService.LoadPayrollDataAsync(request.PayrollId);
            return Results.Ok(result);
        }
    }
}
