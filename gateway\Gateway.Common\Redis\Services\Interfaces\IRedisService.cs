﻿namespace Gateway.Common.Redis.Services.Interfaces
{
    public interface IRedisService<T>
    {
        Action<T>? OnAdded { get; set; }

        Action<T>? OnRemoved { get; set; }

        Task AddAsync(List<T> records);

        List<T> GetAll();

        T? GetBy<PERSON>ey(string key);

        T? GetBy<PERSON>ey(int key);

        List<T> GetManyBy<PERSON>ey(string key);

        Task<bool> RemoveByKeysAsync(List<string> keys);
    }
}
