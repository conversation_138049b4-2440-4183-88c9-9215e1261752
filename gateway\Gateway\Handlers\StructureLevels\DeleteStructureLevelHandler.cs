﻿using Gateway.Connections.Interfaces;
using WorkTimeApi.Common.Requests.StructureLevels;
using MediatR;

namespace Gateway.Handlers.StructureLevels
{
	public class DeleteStructureLevelHandler : IRequestHandler<DeleteStructureLevelRequest, IResult>
	{
		private readonly IWorkTimeApiConnection _workTimeApiConnection;

		public DeleteStructureLevelHandler(IWorkTimeApiConnection workTimeApiConnection)
		{
			_workTimeApiConnection = workTimeApiConnection;
		}

		public async Task<IResult> Handle(DeleteStructureLevelRequest request, CancellationToken cancellationToken)
		{
			var response = await _workTimeApiConnection.DeleteStructureLevelAsync(request.StructureLevelId);

			if (!response.IsSuccessStatusCode)
				return Results.StatusCode((int)response.StatusCode);

			return Results.Text(await response.Content.ReadAsStringAsync(cancellationToken));
		}
	}
}
