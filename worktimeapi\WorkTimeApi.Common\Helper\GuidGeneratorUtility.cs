﻿using System.Security.Cryptography;
using System.Text;

namespace WorkTimeApi.Common.Helper
{
    public static class GuidGeneratorUtility
    {
        public static Guid GenerateGuid(string input)
        {
            using (MD5 md5 = MD5.Create())
            {
                byte[] hash = md5.ComputeHash(Encoding.Default.GetBytes(input));
                return new Guid(hash);
            }
        }
    }
}