import { translate } from "../services/language/Translator";

export const formatDate = (date?: Date | null | string) => {
  if (!date) return "";
  const dateObj = typeof date === "string" ? new Date(date) : date;
  if (isNaN(dateObj.getTime())) return "";
  const day = dateObj.getDate().toString().padStart(2, "0");
  const month = (dateObj.getMonth() + 1).toString().padStart(2, "0");
  const year = dateObj.getFullYear();
  return `${day}.${month}.${year}`;
};

export const formatExperience = (years?: number, months?: number, days?: number) => {
  const parts: string[] = [];
  if (years && years > 0) parts.push(`${years} ${translate("y")}`);
  if (months && months > 0) parts.push(`${months} ${translate("m")}`);
  if (days && days > 0) parts.push(`${days} ${translate("d")}`);
  return parts.join(" ");
};
