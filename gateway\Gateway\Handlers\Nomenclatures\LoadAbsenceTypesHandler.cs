﻿using Gateway.Common.Results;
using Gateway.Connections.Interfaces;
using MediatR;
using WorkTimeApi.Common.Requests.Nomenclatures;

namespace Gateway.Handlers.Nomenclatures
{
    public class LoadAbsenceTypesHandler : IRequestHandler<GetAbsenceTypesRequest, IResult>
    {
        private readonly IWorkTimeApiConnection _workTimeApiConnection;

        public LoadAbsenceTypesHandler(IWorkTimeApiConnection workTimeApiConnection)
        {
            _workTimeApiConnection = workTimeApiConnection;
        }

        public async Task<IResult> Handle(GetAbsenceTypesRequest request, CancellationToken cancellationToken) 
        {
            var response = await _workTimeApiConnection.GetAbsenceTypesAsync();

            return new HttpResponseMessageResult(response);
        }
    }
}
