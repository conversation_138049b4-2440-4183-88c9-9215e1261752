import { ChangeEvent, useState } from "react";
import Textbox from "./Textbox";
import { isValidEmail } from "../../services/emailService";

interface Props
  extends React.DetailedHTMLProps<
    React.AllHTMLAttributes<HTMLDivElement>,
    HTMLDivElement
  > {
  label: string;
  value: string | number;
  handleChange: (e: ChangeEvent<HTMLInputElement>) => void;
  alertMessage?: string;
}

const EmailBox = ({
  handleChange,
  label,
  value,
  alertMessage,
  name,
}: Props) => {
  const [isValid, setIsValid] = useState(true);

  const handleEmailChange = (e: ChangeEvent<HTMLInputElement>) => {
    setIsValid(isValidEmail(e.currentTarget.value));

    handleChange(e);
  };

  return (
    <Textbox
      handleChange={handleEmailChange}
      label={label}
      value={value}
      name={name}
      validation={{
        isValid: isValid,
        alertMessage: alertMessage ?? "Invalid Email",
      }}
    />
  );
};

export default EmailBox;
