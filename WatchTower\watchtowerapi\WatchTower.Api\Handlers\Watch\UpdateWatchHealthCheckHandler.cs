﻿using MediatR;
using WatchTower.Common.Requests;
using WatchTower.Services.Interfaces;

namespace WatchTower.Api.Handlers.Watch
{
    public class UpdateWatchHealthCheckHandler : IRequestHandler<UpdateWatchHealthCheckRequest, IResult>
    {
        private readonly IWatchHealthCheckService _watchHealthCheckService;

        public UpdateWatchHealthCheckHandler(IWatchHealthCheckService watchHealthCheckService)
        {
            _watchHealthCheckService = watchHealthCheckService;
        }

        public async Task<IResult> Handle(UpdateWatchHealthCheckRequest request, CancellationToken cancellationToken)
        {
            var watchHealthCheckRequest = await _watchHealthCheckService.UpdateAsync(request, cancellationToken);

            return Results.Ok(watchHealthCheckRequest);
        }
    }
}
