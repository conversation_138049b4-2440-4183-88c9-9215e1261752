﻿using Gateway.Common.Extenstions.EndpointExtensions;
using Gateway.Extenstions.MediatorExtensions;
using WorkTimeApi.Common.Requests.Employees;

namespace Gateway.EndpointDefinitions.Payrolls
{
    public class EmployeesEndpointDefinitions : IEndpointDefinition
    {
        public void DefineEndpoints(WebApplication app)
        {
            app.AuthenticatedPost<CreateEmployeeForCompanyRequest>("/companies/{companyId}/employees")
                .AuthenticatedGet<LoadEmployeesRequest>("/employees")
                .AuthenticatedGet<GetCoworkersRequest>("employees/coworkers")
                .AuthenticatedGet<LoadEmployeePayrollRequest>("/employee-payrolls")
                .AuthenticatedPost<AddNewEmployeePayrollRequest>("/employee-payroll");
        }

        public void DefineServices(IServiceCollection services)
        {
        }
    }
}
