﻿using AutoMapper;
using Gateway.Connections.Interfaces;
using Gateway.Requests.UserRegistration;
using MediatR;
using Microinvest.TransferFiles.Tools.Models.Users;
using System.Net;

namespace Gateway.Handlers.UserRegistration
{
	public class RegistrationHandler : IRequestHandler<RegistrationRequest, IResult>
	{
		private readonly IUserRegistrationConnection _userRegistrationConnection;
		private readonly IMapper _mapper;

		public RegistrationHandler(IUserRegistrationConnection userRegistrationConnection, IMapper mapper)
		{
			_userRegistrationConnection = userRegistrationConnection;
			_mapper = mapper;
		}

		public async Task<IResult> Handle(RegistrationRequest request, CancellationToken cancellationToken)
		{
			var response = await _userRegistrationConnection.RegisterWithTemplateAsync(_mapper.Map<RegistrationWithTemplateDTO>(request));

            if (response.StatusCode != HttpStatusCode.OK)
				return Results.StatusCode((int)response.StatusCode);

            return Results.Text(await response.Content.ReadAsStringAsync(cancellationToken));
		}
	}
}
