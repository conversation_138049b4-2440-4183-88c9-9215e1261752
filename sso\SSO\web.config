﻿<?xml version="1.0" encoding="UTF-8"?>
<configuration>
	<system.webServer>
		<handlers>
			<add name="aspNetCore" path="*" verb="*" modules="AspNetCoreModuleV2" resourceType="Unspecified" />
		</handlers>
		<aspNetCore processPath="dotnet" arguments=".\SSO.dll" hostingModel="outofprocess" >
			<environmentVariables>
				<environmentVariable name="ASPNETCORE_ENVIRONMENT" value="Production" />
			</environmentVariables>
		</aspNetCore>
	</system.webServer>
</configuration>
