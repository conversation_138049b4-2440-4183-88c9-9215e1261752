﻿using MediatR;
using SSO.Common.Requests;
using SSO.Services.Interfaces;

namespace SSO.Handlers
{
    public class LogoutHandler : IRequestHandler<LogoutRequest, IResult>
    {
        private readonly IRefreshTokensService _refreshTokensService;

        public LogoutHandler(IRefreshTokensService refreshTokensService)
        {
            _refreshTokensService = refreshTokensService;
        }

        public async Task<IResult> Handle(LogoutRequest request, CancellationToken cancellationToken)
        {
            if (request.HttpRequest is null)
                return Results.BadRequest();

            var refreshToken = request.HttpRequest.HttpContext.Response.Headers["Refresh-Token"];
            await _refreshTokensService.DeactivateTokenAsync(refreshToken.ToString());
            request.HttpRequest.HttpContext.Response.Headers.Remove("Authorization");
            request.HttpRequest.HttpContext.Response.Headers.Remove("Refresh-Token");

            return Results.Ok();
        }
    }
}
