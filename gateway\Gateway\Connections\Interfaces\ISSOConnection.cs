﻿using SSO.Common.Requests;

namespace Gateway.Connections.Interfaces
{
    public interface ISSOConnection
    {
        Task<HttpResponseMessage> LoginAsync(string authorizationHeader);

        Task<HttpResponseMessage> HeaderLoginAsync(string authorizationHeader);

        Task<HttpResponseMessage> LogoutAsync(string authorizationHeader, string refreshTokenHeader);

        Task<HttpResponseMessage> AuthenticateAsync(string authorizationHeader, string refreshTokenHeader);

        Task<HttpResponseMessage> GoogleLoginAsync(string googleToken);

        Task<HttpResponseMessage> FacebookLoginAsync(string facebookToken);

        Task<HttpResponseMessage> MicrosoftLoginAsync(string microsoftToken);

        Task<HttpResponseMessage> ResetPasswordAsync(ResetPasswordRequest resetPasswordRequest);

        Task<HttpResponseMessage> ChangeForgottenPasswordAsync(ChangeForgottenPasswordRequest changeForgottenPasswordRequest);

        Task<HttpResponseMessage> GetUserPermissionsByUserRegistrationsCompanyIdAsync(int companyId, string token);

        Task<HttpResponseMessage> ValidateRefreshToken(ValidateRefreshTokenRequest validateRefreshTokenRequest);

        Task<HttpResponseMessage> ValidateRecaptchaToken(RecaptchaValidationRequest recaptchaValidationRequest);
    }
}
