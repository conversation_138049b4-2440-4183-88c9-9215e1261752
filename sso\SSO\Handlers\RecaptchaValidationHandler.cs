﻿using MediatR;
using SSO.Common.Requests;
using SSO.Connections.Interfaces;

namespace SSO.Handlers
{
    public class RecaptchaValidationHandler : IRequestHandler<RecaptchaValidationRequest, IResult>
    {
        private readonly IGoogleValidatorConnection _googleValidatorConnection;

        public RecaptchaValidationHandler(IGoogleValidatorConnection googleValidatorConnection)
        {
            _googleValidatorConnection = googleValidatorConnection;
        }

        public async Task<IResult> Handle(RecaptchaValidationRequest request, CancellationToken cancellationToken)
        {
            var response = await _googleValidatorConnection.ValidateRecaptchaToken(request.Token);
            return response == null || !response.IsSuccessStatusCode ? Results.BadRequest() : Results.Ok();
        }
    }
}
