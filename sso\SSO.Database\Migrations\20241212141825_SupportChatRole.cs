﻿using System;
using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace SSO.Database.Migrations
{
    /// <inheritdoc />
    public partial class SupportChatRole : Migration
    {
        /// <inheritdoc />
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.InsertData(
                table: "Roles",
                columns: new[] { "Id", "Name", "UserRegistrationCompanyId" },
                values: new object[] { new Guid("611a6042-bf7f-f2dc-6213-265d9187bcab"), "SupportChatServiceRole", -10000 });

            migrationBuilder.InsertData(
                table: "PermissionRole",
                columns: new[] { "PermissionsId", "RolesId" },
                values: new object[,]
                {
                    { new Guid("38e30952-737d-3746-6792-0bea04e26f6d"), new Guid("611a6042-bf7f-f2dc-6213-265d9187bcab") }, // Link "SupportChatService.Users" with "SupportChatServiceRole"
                    { new Guid("bdc4a978-a9f4-66df-cace-520dd9889f13"), new Guid("611a6042-bf7f-f2dc-6213-265d9187bcab") }  // Link "SupportChatService.Chats" with "SupportChatServiceRole"
                });
        }

        /// <inheritdoc />
        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DeleteData(
                table: "PermissionRole",
                keyColumns: new[] { "PermissionsId", "RolesId" },
                keyValues: new object[,]
                {
                    { new Guid("38e30952-737d-3746-6792-0bea04e26f6d"), new Guid("611a6042-bf7f-f2dc-6213-265d9187bcab") },
                    { new Guid("bdc4a978-a9f4-66df-cace-520dd9889f13"), new Guid("611a6042-bf7f-f2dc-6213-265d9187bcab") }
                });

            migrationBuilder.DeleteData(
                table: "Roles",
                keyColumn: "Id",
                keyValue: new Guid("611a6042-bf7f-f2dc-6213-265d9187bcab"));
        }
    }
}
