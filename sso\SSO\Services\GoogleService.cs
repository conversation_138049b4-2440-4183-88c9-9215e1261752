﻿using SSO.Connections.Interfaces;
using SSO.Models;
using SSO.Services.Interfaces;
using System.Text.Json;

namespace SSO.Services
{
    public class GoogleService : IGoogleService
    {
        private readonly IGoogleValidatorConnection _googleValidatorConnection;

        public GoogleService(IGoogleValidatorConnection googleValidatorConnection)
        {
            _googleValidatorConnection = googleValidatorConnection;
        }

        public async Task<GoogleDTO?> GetGoogleDataAsync(string googleToken)
        {
            var response = await _googleValidatorConnection.ValidateGoogleTokenAsync(googleToken);
            if (!response.IsSuccessStatusCode)
                return null;

            return JsonSerializer.Deserialize<GoogleDTO>(await response.Content.ReadAsStringAsync());
        }
    }
}
