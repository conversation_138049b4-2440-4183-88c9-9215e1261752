﻿using System;
using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

#pragma warning disable CA1814 // Prefer jagged arrays over multidimensional

namespace WorkTimeApi.Database.Migrations
{
    /// <inheritdoc />
    public partial class NotificationTypesAdded : Migration
    {
        /// <inheritdoc />
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.InsertData(
                table: "NotificationGroup",
                columns: new[] { "Id", "Name" },
                values: new object[] { new Guid("946c62eb-1e53-54c5-f93b-2b78a77c8b1b"), "Employees" });

            migrationBuilder.InsertData(
                table: "NotificationTypes",
                columns: new[] { "Id", "Name", "NotificationGroupId", "PermissionId" },
                values: new object[,]
                {
                    { new Guid("128ee027-ea26-db74-5183-c41bb0218a01"), "Notifications.Employees.AddressesUpdated.Email", new Guid("946c62eb-1e53-54c5-f93b-2b78a77c8b1b"), new Guid("7dd10ffd-497d-c5ee-a3e4-42ca56a5480c") },
                    { new Guid("26efe49f-52fa-2c72-f3f7-e9000797dff6"), "Notifications.Employees.EditedByAdmin.Email", new Guid("946c62eb-1e53-54c5-f93b-2b78a77c8b1b"), null },
                    { new Guid("666de694-7dda-1e4c-fa68-6e827822aeff"), "Notifications.Employees.Updated.Push", new Guid("946c62eb-1e53-54c5-f93b-2b78a77c8b1b"), new Guid("7dd10ffd-497d-c5ee-a3e4-42ca56a5480c") },
                    { new Guid("9c125211-363c-a12a-c657-60dff07cb6b4"), "Notifications.Employees.AddressesEditedByAdmin.Email", new Guid("946c62eb-1e53-54c5-f93b-2b78a77c8b1b"), null },
                    { new Guid("a7844298-9ec2-98cb-7949-b3196367962b"), "Notifications.Employees.Updated.Email", new Guid("946c62eb-1e53-54c5-f93b-2b78a77c8b1b"), new Guid("7dd10ffd-497d-c5ee-a3e4-42ca56a5480c") },
                    { new Guid("a99a9df8-95f4-91b2-eff6-68ce79c4dbb6"), "Notifications.Employees.AddressesUpdated.Push", new Guid("946c62eb-1e53-54c5-f93b-2b78a77c8b1b"), new Guid("7dd10ffd-497d-c5ee-a3e4-42ca56a5480c") },
                    { new Guid("c651caf8-2883-5f13-4725-ba460ed3e174"), "Notifications.Employees.EditedByAdmin.Push", new Guid("946c62eb-1e53-54c5-f93b-2b78a77c8b1b"), null },
                    { new Guid("e3830b70-9f86-66e3-8c11-fa06ae781adc"), "Notifications.Employees.AddressesEditedByAdmin.Push", new Guid("946c62eb-1e53-54c5-f93b-2b78a77c8b1b"), null }
                });
        }

        /// <inheritdoc />
        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DeleteData(
                table: "NotificationTypes",
                keyColumn: "Id",
                keyValue: new Guid("128ee027-ea26-db74-5183-c41bb0218a01"));

            migrationBuilder.DeleteData(
                table: "NotificationTypes",
                keyColumn: "Id",
                keyValue: new Guid("26efe49f-52fa-2c72-f3f7-e9000797dff6"));

            migrationBuilder.DeleteData(
                table: "NotificationTypes",
                keyColumn: "Id",
                keyValue: new Guid("666de694-7dda-1e4c-fa68-6e827822aeff"));

            migrationBuilder.DeleteData(
                table: "NotificationTypes",
                keyColumn: "Id",
                keyValue: new Guid("9c125211-363c-a12a-c657-60dff07cb6b4"));

            migrationBuilder.DeleteData(
                table: "NotificationTypes",
                keyColumn: "Id",
                keyValue: new Guid("a7844298-9ec2-98cb-7949-b3196367962b"));

            migrationBuilder.DeleteData(
                table: "NotificationTypes",
                keyColumn: "Id",
                keyValue: new Guid("a99a9df8-95f4-91b2-eff6-68ce79c4dbb6"));

            migrationBuilder.DeleteData(
                table: "NotificationTypes",
                keyColumn: "Id",
                keyValue: new Guid("c651caf8-2883-5f13-4725-ba460ed3e174"));

            migrationBuilder.DeleteData(
                table: "NotificationTypes",
                keyColumn: "Id",
                keyValue: new Guid("e3830b70-9f86-66e3-8c11-fa06ae781adc"));

            migrationBuilder.DeleteData(
                table: "NotificationGroup",
                keyColumn: "Id",
                keyValue: new Guid("946c62eb-1e53-54c5-f93b-2b78a77c8b1b"));
        }
    }
}
