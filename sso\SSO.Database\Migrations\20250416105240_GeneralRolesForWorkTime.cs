﻿using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace SSO.Database.Migrations
{
    /// <inheritdoc />
    public partial class GeneralRolesForWorkTime : Migration
    {
        /// <inheritdoc />
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.InsertData(
                            table: "Roles",
                            columns: ["Id", "Name", "UserRegistrationCompanyId"],
                            values: new object[,]
                            {
                                { Guid.NewGuid(), "grOwner", -20000 },
                                { Guid.NewGuid(), "grAccountingHouseManager", -20000 },
                                { Guid.NewGuid(), "grAccountingHouseSpecialist", -20000 },
                                { Guid.NewGuid(), "grCompanyManager", -20000 },
                                { Guid.NewGuid(), "grHumanResources", -20000 },
                                { Guid.NewGuid(), "grAccountant", -20000 },
                                { Guid.NewGuid(), "grITSupport", -20000 },
                                { Guid.NewGuid(), "grDepartmentManager", -20000 },
                                { Guid.NewGuid(), "grTeamLead", -20000 }
                            });
        }

        /// <inheritdoc />
        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DeleteData(
                table: "Roles",
                keyColumn: "Name",
                keyValues:
                [
                    "grOwner",
                    "grAccountingHouseManager",
                    "grAccountingHouseSpecialist",
                    "grCompanyManager",
                    "grHumanResources",
                    "grAccountant",
                    "grITSupport",
                    "grDepartmentManager",
                    "grTeamLead"
                ]);
        }
    }
}
