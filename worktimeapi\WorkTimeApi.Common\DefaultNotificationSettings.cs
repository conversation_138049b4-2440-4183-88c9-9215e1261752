﻿using WorkTimeApi.Common.Helper;

namespace WorkTimeApi.Common
{
    public class DefaultNotificationSettings
    {
        public static class AbsenceAddedByEmployee
        {
            public static string Push => GuidGeneratorUtility.GenerateGuid(NotificationsName.Absences.AddedByEmployee.Push).ToString();

            public static string Email => GuidGeneratorUtility.GenerateGuid(NotificationsName.Absences.AddedByEmployee.Email).ToString();
        }

        public static class AbsenceEditedByEmployee
        {
            public static string Push => GuidGeneratorUtility.GenerateGuid(NotificationsName.Absences.EditedByEmployee.Push).ToString();

            public static string Email => GuidGeneratorUtility.GenerateGuid(NotificationsName.Absences.EditedByEmployee.Email).ToString();
        }

        public static class EditedConfirmedAbsenceByEmployee
        {
            public static string Push => GuidGeneratorUtility.GenerateGuid(NotificationsName.Absences.EditedConfirmedAbsenceByEmployee.Push).ToString();

            public static string Email => GuidGeneratorUtility.GenerateGuid(NotificationsName.Absences.EditedConfirmedAbsenceByEmployee.Email).ToString();
        }

        public static class EditedConfirmedAbsenceByEmployeeDeclined
        {
            public static string Push => GuidGeneratorUtility.GenerateGuid(NotificationsName.Absences.EditedConfirmedAbsenceByEmployeeDeclined.Push).ToString();

            public static string Email => GuidGeneratorUtility.GenerateGuid(NotificationsName.Absences.EditedConfirmedAbsenceByEmployeeDeclined.Email).ToString();
        }

        public static class AbsenceEditedByAdmin
        {
            public static string Push => GuidGeneratorUtility.GenerateGuid(NotificationsName.Absences.EditedByAdmin.Push).ToString();

            public static string Email => GuidGeneratorUtility.GenerateGuid(NotificationsName.Absences.EditedByAdmin.Email).ToString();
        }

        public static class AbsenceApproved
        {
            public static string Push => GuidGeneratorUtility.GenerateGuid(NotificationsName.Absences.Approved.Push).ToString();

            public static string Email => GuidGeneratorUtility.GenerateGuid(NotificationsName.Absences.Approved.Email).ToString();
        }

        public static class AbsenceDeclined
        {
            public static string Push => GuidGeneratorUtility.GenerateGuid(NotificationsName.Absences.Declined.Push).ToString();

            public static string Email => GuidGeneratorUtility.GenerateGuid(NotificationsName.Absences.Declined.Email).ToString();
        }

        public static class DeleteRequestByEmployee
        {
            public static string Email => GuidGeneratorUtility.GenerateGuid(NotificationsName.Absences.DeleteRequestByEmployee.Email).ToString();

            public static string Push = GuidGeneratorUtility.GenerateGuid(NotificationsName.Absences.DeleteRequestByEmployee.Push).ToString();
        }

        public static class AbsenceEditConfirmed
        {
            public static string Email => GuidGeneratorUtility.GenerateGuid(NotificationsName.Absences.Edit.Email).ToString();

            public static string Push => GuidGeneratorUtility.GenerateGuid(NotificationsName.Absences.Edit.Push).ToString();
        }

        public static class AbsenceDeleteConfirmed
        {
            public static string Email => GuidGeneratorUtility.GenerateGuid(NotificationsName.Absences.Delete.Email).ToString();

            public static string Push => GuidGeneratorUtility.GenerateGuid(NotificationsName.Absences.Delete.Push).ToString();
        }

        public static class DeleteByAdmin
        {
            public static string Email => GuidGeneratorUtility.GenerateGuid(NotificationsName.Absences.DeleteByAdmin.Email).ToString();

            public static string Push => GuidGeneratorUtility.GenerateGuid(NotificationsName.Absences.DeleteByAdmin.Push).ToString();
        }

        public static class DeleteDeclined
        {
            public static string Email => GuidGeneratorUtility.GenerateGuid(NotificationsName.Absences.DeleteDeclined.Email).ToString();

            public static string Push => GuidGeneratorUtility.GenerateGuid(NotificationsName.Absences.DeleteDeclined.Push).ToString();
        }

        public static class HospitalsAddedByEmployee
        {
            public static string Email => GuidGeneratorUtility.GenerateGuid(NotificationsName.Hospitals.AddedByEmployee.Email).ToString();

            public static string Push => GuidGeneratorUtility.GenerateGuid(NotificationsName.Hospitals.AddedByEmployee.Push).ToString();
        }

        public static class HospitalsApproved
        {
            public static string Email => GuidGeneratorUtility.GenerateGuid(NotificationsName.Hospitals.Approved.Email).ToString();

            public static string Push => GuidGeneratorUtility.GenerateGuid(NotificationsName.Hospitals.Approved.Push).ToString();
        }

        public static class PendingEmployeesUpdated
        {
            public static string Push => GuidGeneratorUtility.GenerateGuid(NotificationsName.PendingEmployees.Updated.Push).ToString();
        }

        public static class PayrollsAdded
        {  
            public static string Push => GuidGeneratorUtility.GenerateGuid(NotificationsName.Payrolls.Added.Push).ToString();
        }

        public static class EmployeesEditConfirmed
        {
            public static string Email => GuidGeneratorUtility.GenerateGuid(NotificationsName.Employees.EditConfirmed.Email).ToString();

            public static string Push => GuidGeneratorUtility.GenerateGuid(NotificationsName.Employees.EditConfirmed.Push).ToString();
        }

        public static class EmployeesEditDeclined
        {
            public static string Email => GuidGeneratorUtility.GenerateGuid(NotificationsName.Employees.EditDeclined.Email).ToString();

            public static string Push => GuidGeneratorUtility.GenerateGuid(NotificationsName.Employees.EditDeclined.Push).ToString();
        }

        public static class EmployeesEditedByEmployee
        {
            public static string Email => GuidGeneratorUtility.GenerateGuid(NotificationsName.Employees.EditedByEmployee.Email).ToString();

            public static string Push => GuidGeneratorUtility.GenerateGuid(NotificationsName.Employees.EditedByEmployee.Push).ToString();
        }

        public static class EmployeesEditedByAdmin
        {
            public static string Email => GuidGeneratorUtility.GenerateGuid(NotificationsName.Employees.EditedByAdmin.Email).ToString();

            public static string Push => GuidGeneratorUtility.GenerateGuid(NotificationsName.Employees.EditedByAdmin.Push).ToString();
        }

        public static class EmployeesAddressesEditedByEmployee
        {
            public static string Email => GuidGeneratorUtility.GenerateGuid(NotificationsName.Employees.AddressesEditedByEmployee.Email).ToString();

            public static string Push => GuidGeneratorUtility.GenerateGuid(NotificationsName.Employees.AddressesEditedByEmployee.Push).ToString();
        }

        public static class EmployeesAddressesEditedByAdmin
        {
            public static string Email => GuidGeneratorUtility.GenerateGuid(NotificationsName.Employees.AddressesEditedByAdmin.Email).ToString();

            public static string Push => GuidGeneratorUtility.GenerateGuid(NotificationsName.Employees.AddressesEditedByAdmin.Push).ToString();
        }
    }
}
