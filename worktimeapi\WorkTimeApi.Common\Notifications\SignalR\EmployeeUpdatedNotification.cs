namespace WorkTimeApi.Common.Notifications.SignalR
{
    public class EmployeeUpdatedPayload
    {
        public Guid EmployeeId { get; set; }

        public Guid PayrollId { get; set; }
    }

    public class EmployeeUpdatedNotification(EmployeeUpdatedPayload payload, Guid companyId, Guid userId, string creatorName = "")
        : BaseNotification<EmployeeUpdatedPayload>(payload, companyId, NotificationsName.Employees.Updated.Push, creatorName)
    {
        public Guid UserId { get; } = userId;
    }
}
