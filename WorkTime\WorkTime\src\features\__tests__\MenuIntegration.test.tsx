import { render, screen } from "@testing-library/react";
import { <PERSON><PERSON>erRouter } from "react-router-dom";
import Menu from "../Menu";
import { Genders } from "../../constants/enum";

// Mock all dependencies
const mockToggleMenu = jest.fn();
const mockCloseMenu = jest.fn();
const mockNavigate = jest.fn();
const mockDispatch = jest.fn();

jest.mock("react-router-dom", () => ({
  ...jest.requireActual("react-router-dom"),
  useNavigate: () => mockNavigate,
  useLocation: () => ({ pathname: "/employees" }),
}));

jest.mock("../../app/hooks", () => ({
  useAppDispatch: () => mockDispatch,
}));

jest.mock("../../services/authentication/authenticationService", () => ({
  logout: jest.fn(),
}));

jest.mock("../../services/companies/companiesService", () => ({
  initCompany: jest
    .fn()
    .mockResolvedValue({ id: "company-1", name: "Test Company" }),
}));

jest.mock("../../services/users/userService", () => ({
  initUserEmployee: jest.fn().mockResolvedValue({ id: "user-1" }),
}));

const useMenuMock = jest.fn();
jest.mock("../MenuContext", () => ({
  useMenu: () => useMenuMock(),
}));

jest.mock("../../components/PopUp/ActionModalContext", () => ({
  useModal: () => ({ isModalOpen: false }),
}));

jest.mock("../companies/CompanyContext", () => ({
  useCompany: () => ({
    company: { id: "company-1", name: "Test Company" },
    setCompany: jest.fn(),
    resetCompany: jest.fn(),
  }),
}));

jest.mock("../UserEmployeeContext", () => ({
  useUserEmployee: () => ({
    setUserEmployee: jest.fn(),
    resetUserEmployee: jest.fn(),
  }),
}));

jest.mock("../authentication/AuthContext", () => ({
  useAuth: () => ({
    user: {
      firstName: "Current",
      secondName: "User",
      lastName: "Name",
      email: "<EMAIL>",
    },
    resetUser: jest.fn(),
  }),
}));

jest.mock("../absences/AbsenceContext", () => ({
  useAbsence: () => ({
    selectedAbsence: null,
    isEditing: false,
  }),
}));

// Mock all side menu components
jest.mock("../employees/editEmployee/EditEmployeeDataSideMenu", () => () => (
  <div data-testid="edit-employee-side-menu">Edit Employee Data</div>
));

jest.mock(
  "../employees/editEmployee/EditEmployeeAddressesSideMenu",
  () => () =>
    (
      <div data-testid="edit-employee-addresses-side-menu">
        Edit Employee Addresses
      </div>
    )
);

jest.mock("../../components/ArrowTip", () => () => (
  <div data-testid="menu-arrow-tip">Arrow Tip</div>
));

// Mock ProfileSideMenu to avoid internal context usage in tests
jest.mock("../employees/employeeProfile/ProfileSideMenu", () => () => (
  <div data-testid="menu-profile-side-menu">Profile Side Menu</div>
));

jest.mock("../../services/language/Translator", () => ({ getString }: any) => (
  <span data-testid={`translator-${getString}`}>{getString}</span>
));

describe("Menu Component - Integration Tests", () => {
  beforeEach(() => {
    jest.clearAllMocks();
    // default menu context
    useMenuMock.mockReturnValue({
      currentPage: "/employees",
      closeMenu: mockCloseMenu,
      activeView: "edit-employee",
      menuOpenedFrom: "other",
      viewData: {
        incomingProfile: {
          iban: "BG123456789",
          code: "EMP001",
          employee: {
            id: "emp-1",
            workTimeId: "worktime-123",
            firstName: "John",
            secondName: "Michael",
            lastName: "Doe",
            email: "<EMAIL>",
          },
          addresses: [],
          payrollPersonalData: {},
          employeePropertyEdits: [],
        },
      },
    });
  });

  describe("Employee Header Display Integration", () => {
    test("should display selected employee data instead of current user data for edit-employee view", () => {
      render(
        <BrowserRouter>
          <Menu isOpen={true} onToggleMenu={mockToggleMenu} />
        </BrowserRouter>
      );

      // Should show the selected employee's data, not the current user's data
      expect(screen.getByTestId("menu-edit-employee-name")).toHaveTextContent(
        "John Michael Doe"
      );
      expect(
        screen.getByTestId("menu-edit-employee-contact")
      ).toHaveTextContent("<EMAIL>");

      // Should NOT show current user's data
      expect(screen.queryByText("Current User Name")).not.toBeInTheDocument();
      expect(
        screen.queryByText("<EMAIL>")
      ).not.toBeInTheDocument();
    });

    test("should display selected employee data for edit-employee-addresses view", () => {
      // Configure the menu context for edit-employee-addresses view
      useMenuMock.mockReturnValue({
        currentPage: "/employees",
        closeMenu: mockCloseMenu,
        activeView: "edit-employee-addresses",
        menuOpenedFrom: "other",
        viewData: {
          incomingProfile: {
            iban: "BG123456789",
            code: "EMP001",
            employee: {
              id: "emp-2",
              workTimeId: "worktime-456",
              firstName: "Jane",
              secondName: "Marie",
              lastName: "Smith",
              email: "<EMAIL>",
            },
            addresses: [],
            payrollPersonalData: {},
            employeePropertyEdits: [],
          },
        },
      });

      render(
        <BrowserRouter>
          <Menu isOpen={true} onToggleMenu={mockToggleMenu} />
        </BrowserRouter>
      );

      // Should show the selected employee's data for addresses view
      expect(screen.getByTestId("menu-edit-employee-name")).toHaveTextContent(
        "Jane Marie Smith"
      );
      expect(
        screen.getByTestId("menu-edit-employee-contact")
      ).toHaveTextContent("<EMAIL>");

      // Should render the correct side menu component
      expect(
        screen.getByTestId("edit-employee-addresses-side-menu")
      ).toBeInTheDocument();
    });

    test("should fallback to current user data when not in edit mode", () => {
      // Configure the menu context for profile view
      useMenuMock.mockReturnValue({
        currentPage: "/employees",
        closeMenu: mockCloseMenu,
        activeView: "profile",
        menuOpenedFrom: "other",
        viewData: null,
      });

      render(
        <BrowserRouter>
          <Menu isOpen={true} onToggleMenu={mockToggleMenu} />
        </BrowserRouter>
      );

      // Should show current user's data when not editing
      expect(screen.getByTestId("menu-user-name")).toHaveTextContent(
        "Current User Name"
      );
      expect(screen.getByTestId("menu-user-email")).toHaveTextContent(
        "<EMAIL>"
      );
    });

    test("should handle missing employee data gracefully", () => {
      // Configure the menu context with missing employee data
      useMenuMock.mockReturnValue({
        currentPage: "/employees",
        closeMenu: mockCloseMenu,
        activeView: "edit-employee",
        menuOpenedFrom: "other",
        viewData: {
          incomingProfile: {
            iban: "BG123456789",
            code: "EMP001",
            employee: null,
            addresses: [],
            payrollPersonalData: {},
            employeePropertyEdits: [],
          },
        },
      });

      render(
        <BrowserRouter>
          <Menu isOpen={true} onToggleMenu={mockToggleMenu} />
        </BrowserRouter>
      );

      // Should show fallback text
      expect(
        screen.getByTestId("translator-Edit employee")
      ).toBeInTheDocument();
      expect(
        screen.queryByTestId("menu-edit-employee-name")
      ).not.toBeInTheDocument();
      expect(
        screen.queryByTestId("menu-edit-employee-contact")
      ).not.toBeInTheDocument();
    });
  });

  describe("Data Consistency Tests", () => {
    test("should maintain consistent employee data across different views", () => {
      const testEmployee = {
        id: "emp-consistency",
        workTimeId: "worktime-consistency",
        firstName: "Consistency",
        secondName: "Test",
        lastName: "Employee",
        email: "<EMAIL>",
        phone: "+**********",
        workPhone: "+**********",
        idNumber: "ID123456",
        idIssueDate: "2020-01-01",
        idIssuedFrom: "Test Authority",
        gender: Genders.Male,
        birthDate: "1990-01-01",
        birthPlace: "Test City",
        citizenship: "Test Country",
        userId: "user-consistency",
        companyId: "company-1",
        company: {},
        bankAccounts: [],
        addresses: [],
      };

      const testProfile = {
        iban: "BG123456789",
        code: "EMP001",
        employee: testEmployee,
        addresses: [],
        payrollPersonalData: {},
        employeePropertyEdits: [],
      };

      // Test edit-employee view
      useMenuMock.mockReturnValue({
        currentPage: "/employees",
        closeMenu: mockCloseMenu,
        activeView: "edit-employee",
        menuOpenedFrom: "other",
        viewData: { incomingProfile: testProfile },
      });

      const { rerender } = render(
        <BrowserRouter>
          <Menu isOpen={true} onToggleMenu={mockToggleMenu} />
        </BrowserRouter>
      );

      expect(screen.getByTestId("menu-edit-employee-name")).toHaveTextContent(
        "Consistency Test Employee"
      );
      expect(
        screen.getByTestId("menu-edit-employee-contact")
      ).toHaveTextContent("<EMAIL>");

      // Test edit-employee-addresses view with same data
      useMenuMock.mockReturnValue({
        currentPage: "/employees",
        closeMenu: mockCloseMenu,
        activeView: "edit-employee-addresses",
        menuOpenedFrom: "other",
        viewData: { incomingProfile: testProfile },
      });

      rerender(
        <BrowserRouter>
          <Menu isOpen={true} onToggleMenu={mockToggleMenu} />
        </BrowserRouter>
      );

      expect(screen.getByTestId("menu-edit-employee-name")).toHaveTextContent(
        "Consistency Test Employee"
      );
      expect(
        screen.getByTestId("menu-edit-employee-contact")
      ).toHaveTextContent("<EMAIL>");
    });
  });
});
