﻿using Gateway.Common.Results;
using Gateway.Connections.Interfaces;
using Gateway.Extenstions.RequestExtensions;
using MediatR;
using SSO.Common.Requests;
using WorkTimeApi.Common.Requests.Employees;
using UserDTO = Microinvest.TransferFiles.Tools.Models.Users.UserDTO;
using WorktimeUserDTO = WorkTimeApi.Common.DTOs.Users.UserDTO;

namespace Gateway.Handlers.SSO
{
    public class HeaderLoginHandler : IRequestHandler<HeaderLoginRequest, IResult>
    {
        private readonly ISSOConnection _ssoConnection;
        private readonly IWorkTimeApiConnection _workTimeApiConnection;
        private readonly IUserRegistrationConnection _userRegistrationConnection;

        public HeaderLoginHandler(ISSOConnection ssoConnection
            , IWorkTimeApiConnection workTimeApiConnection
            , IUserRegistrationConnection userRegistrationConnection)
        {
            _ssoConnection = ssoConnection;
            _workTimeApiConnection = workTimeApiConnection;
            _userRegistrationConnection = userRegistrationConnection;
        }

        public async Task<IResult> Handle(HeaderLoginRequest request, CancellationToken cancellationToken)
        {
            if (request.HttpRequest is null)
                return Results.BadRequest();

            var authorizationHeader = request.HttpRequest.Headers.Authorization;

            if (string.IsNullOrWhiteSpace(authorizationHeader))
                return Results.BadRequest();

            var ssoResponse = await _ssoConnection.HeaderLoginAsync(authorizationHeader!);
            request.AddResponseAuthorizationHeaders(ssoResponse);

            var addEmployeeRequest = await ssoResponse.Content.ReadFromJsonAsync<AddEmployeeRequest>(cancellationToken: cancellationToken);
            if (addEmployeeRequest is null)
                return Results.BadRequest();

            var user = await _userRegistrationConnection.GetSenderaUserAsync(addEmployeeRequest.Email);

            var userDTO = await user.Content.ReadFromJsonAsync<UserDTO>(cancellationToken);

            var worktimeUser = userDTO == null
                ? new WorktimeUserDTO
                {
                    Email = addEmployeeRequest.Email,
                    FirstName = addEmployeeRequest.FirstName,
                    SecondName = addEmployeeRequest.SecondName,
                    LastName = addEmployeeRequest.LastName
                }
                : new WorktimeUserDTO
                {
                    Email = addEmployeeRequest.Email,
                    FirstName = userDTO.FirstName,
                    SecondName = userDTO.SecondName,
                    LastName = userDTO.LastName,
                    Code = userDTO.Code != 0 ? userDTO.Code : null,
                    CodePassword = userDTO.Password,
                    Id = new Guid(userDTO.Id)
                };

            var addUserResponse = await _workTimeApiConnection.AddUserAsync(new WorkTimeApi.Common.Requests.Users.AddUserRequest { User = worktimeUser });

            return new HttpResponseMessageResult(addUserResponse);
        }
    }
}
