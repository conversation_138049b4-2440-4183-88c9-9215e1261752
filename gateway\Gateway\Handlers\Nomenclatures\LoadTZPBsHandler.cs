﻿using Gateway.Connections.Interfaces;
using MediatR;
using WorkTimeApi.Common.Requests.Nomenclatures;

namespace Gateway.Handlers.Nomenclatures
{
    public class LoadTZPBsHandler : IRequestHandler<GetTZPBsRequest, IResult>
    {
        private readonly IWorkTimeApiConnection _workTimeApiConnection;

        public LoadTZPBsHandler(IWorkTimeApiConnection workTimeApiConnection)
        {
            _workTimeApiConnection = workTimeApiConnection;
        }

        public async Task<IResult> Handle(GetTZPBsRequest request, CancellationToken cancellationToken)
        {
            var response = await _workTimeApiConnection.GetTZPBsAsync();

            if (!response.IsSuccessStatusCode)
                return Results.StatusCode((int)response.StatusCode);

            return Results.Text(await response.Content.ReadAsStringAsync(cancellationToken));
        }
    }
}
