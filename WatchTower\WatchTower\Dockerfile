# Stage 1
FROM node:14-alpine as build
WORKDIR /watchtower
ARG VITE_GATEWAY_API
ARG VITE_WATCH_TOWER_API
ENV VITE_GATEWAY_API $VITE_GATEWAY_API
ENV VITE_WATCH_TOWER_API $VITE_WATCH_TOWER_API
COPY package.json /watchtower/package.json
RUN npm install --silent
COPY . /watchtower
RUN npm install vite
RUN npm run build

# Stage 2
FROM nginx:1.25.2-alpine
COPY --from=build /watchtower/dist /usr/share/nginx/html
COPY nginx.conf /etc/nginx/conf.d/default.conf
CMD ["nginx", "-g", "daemon off;"]