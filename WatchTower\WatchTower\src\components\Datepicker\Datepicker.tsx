import React, { useEffect, useRef, useState } from "react";
import MonthsElement from "./MonthElement";
import DateElement from "./DateElement";
import YearsElement from "./YearsElement";
import Container from "../Container";
import Translator from "../../services/language/Translator";
import {
  Button,
  ContainerDropDown,
  ContainerNavButtons,
  ContainerNavBar,
  MainContainer,
} from "./Styles";
import { ViewMode } from "../../models/Enums/ViewMode";
import Textbox from "../Inputs/Textbox";

interface DatepickerProps {
  onSelectDate: (date: Date | null) => void;
}

const Datepicker: React.FC<DatepickerProps> = ({ onSelectDate }) => {
  const months = [
    "strJan",
    "strFeb",
    "strMar",
    "strApr",
    "strMay",
    "strJun",
    "strJul",
    "strAug",
    "strSep",
    "strOct",
    "strNov",
    "strDec",
  ];

  const [date, setDate] = useState(new Date());
  const [selectedDay, setSelectedDay] = useState(date.getDate());
  const [selectedMonth, setSelectedMonth] = useState(date.getMonth());
  const [selectedYear, setSelectedYear] = useState(date.getFullYear());
  const [selectedGroupIndex, setSelectedGroupIndex] = useState(10);
  const [datepickerView, setDatepickerView] = useState(ViewMode.DatesView);
  const [textInput, setTextInput] = useState("");
  const [isDatePickerActive, setDatePickerActive] = useState(false);
  const datepickerRef = useRef<HTMLDivElement>(null);

  const handleDatePickerClick = () => {
    if (!isDatePickerActive) setDatePickerActive(true);
  };

  const handleMonthClick = () => {
    if (datepickerView === ViewMode.MonthsView) {
      setDatepickerView(ViewMode.DatesView);
    } else setDatepickerView(ViewMode.MonthsView);
  };

  const handleYearClick = () => {
    if (datepickerView === ViewMode.YearsView) {
      setDatepickerView(ViewMode.MonthsView);
    } else setDatepickerView(ViewMode.YearsView);
  };

  const goToNextMonth = () => {
    let newMonth = selectedMonth + 1;
    let newYear = selectedYear;

    if (newMonth > 11) {
      newMonth = 0;
      newYear += 1;
    }

    setSelectedMonth(newMonth);
    setSelectedYear(newYear);
  };

  const goToPrevMonth = () => {
    let newMonth = selectedMonth - 1;
    let newYear = selectedYear;

    if (newMonth < 0) {
      newMonth = 11;
      newYear -= 1;
    }

    setSelectedMonth(newMonth);
    setSelectedYear(newYear);
  };

  const goToNextYear = () => {
    if (datepickerView === ViewMode.YearsView) changeGroupIndex(1);
    else setSelectedYear(selectedYear + 1);
  };

  const changeGroupIndex = (number: number) => {
    setSelectedGroupIndex(selectedGroupIndex + number);
  };

  const goToPrevYear = () => {
    if (datepickerView === ViewMode.YearsView) changeGroupIndex(-1);
    else setSelectedYear(selectedYear - 1);
  };

  const handleDateClick = (day: number, index: number = 0) => {
    const newDate = new Date(selectedYear, selectedMonth + index, day);
    setDate(date);
    setSelectedDay(day);
    setSelectedMonth(selectedMonth + index);
    setSelectedYear(selectedYear);
    setTextInput(formatDate(newDate));
    onSelectDate(newDate);
    setDatePickerActive(false);
  };

  const handleMonthsClick = (selectedMonth: number) => {
    setSelectedMonth(selectedMonth);
    setSelectedYear(selectedYear);
    handleMonthClick();
  };

  const handleYearsClick = (selectedYear: number) => {
    setSelectedMonth(selectedMonth);
    setSelectedYear(selectedYear);
    handleYearClick();
  };

  const formatDate = (d: Date) => {
    const day = d.getDate() < 10 ? "0" + d.getDate() : d.getDate();
    const month = (d.getMonth() + 1 < 10 ? "0" : "") + (d.getMonth() + 1);
    const year = d.getFullYear();

    return `${day}/${month}/${year}`;
  };

  const mthElement = (
    <Container
      data-testid="selected-month"
      className="month"
      onClick={handleMonthClick}
    >
      <Translator getString={months[selectedMonth]} />
    </Container>
  );

  const yearElement = (
    <Container
      data-testid="selected-year"
      className="year"
      onClick={handleYearClick}
    >
      {selectedYear}
    </Container>
  );

  const nextMthElement = (
    <ContainerNavButtons onClick={goToNextMonth}>›</ContainerNavButtons>
  );

  const prevMthElement = (
    <ContainerNavButtons onClick={goToPrevMonth}>‹</ContainerNavButtons>
  );

  const prevYearElement = (
    <ContainerNavButtons onClick={goToPrevYear}>‹</ContainerNavButtons>
  );

  const nextYearElement = (
    <ContainerNavButtons onClick={goToNextYear}>›</ContainerNavButtons>
  );

  const assignInput = (inputValue: string, currentYear: number): string => {
    const currentMonth = (new Date().getMonth() + 1)
      .toString()
      .padStart(2, "0");

    let formattedValue = inputValue.replace(/\D/g, "");

    if (formattedValue.length > 0) {
      if (formattedValue.length < 2) {
        formattedValue = "0" + formattedValue;
      }
      if (formattedValue.length <= 2) {
        formattedValue += currentMonth;
      }
      if (formattedValue.length <= 4) {
        formattedValue = formattedValue.replace(
          /(\d{2})(\d{2})(\d{0,4})/,
          `$1/$2/${currentYear}$3`
        );
      } else if (formattedValue.length === 5) {
        formattedValue = formattedValue.replace(
          /(\d{2})(\d{2})(\d{0,4})/,
          `$1/$2/200$3`
        );
      } else if (formattedValue.length === 6) {
        formattedValue = formattedValue.replace(
          /(\d{2})(\d{2})(\d{0,4})/,
          `$1/$2/20$3`
        );
      } else if (formattedValue.length === 8) {
        formattedValue = formattedValue.replace(
          /(\d{2})(\d{2})(\d{0,4})/,
          `$1/$2/$3`
        );
      }
    }

    return formattedValue;
  };

  const daysInMonth = (month: number, year: number) => {
    return new Date(year, month, 0).getDate();
  };

  const formatInputValue = (inputValue: string) => {
    const currentYear = new Date().getFullYear();

    let formattedValue = assignInput(inputValue, currentYear);

    let day = parseInt(formattedValue.slice(0, 2));
    let month = parseInt(formattedValue.slice(3, 5));
    let year = parseInt(formattedValue.slice(6, 10));

    const amountDays = daysInMonth(month, year);

    if (month < 1) {
      formattedValue = formattedValue.replace(
        /(\d{2})\/(\d{2})\/(\d{0,4})/,
        `$1/01/$3`
      );
    } else if (month > 12) {
      month = 12;
      formattedValue = formattedValue.replace(
        /(\d{2})\/(\d{2})\/(\d{0,4})/,
        `$1/${month}/$3`
      );
    }

    if (day > amountDays) {
      formattedValue = formattedValue.replace(/(\d{2})/, `${amountDays}`);
    }

    if (year < 1) {
      formattedValue = formattedValue.replace(
        /(\d{2})\/(\d{2})\/(\d{0,4})/,
        `$1/$2/${currentYear}`
      );
    }

    return formattedValue;
  };

  const handleKeyPress = (event: React.KeyboardEvent<HTMLInputElement>) => {
    if (event.key === "Enter") {
      const formattedValue = formatInputValue(textInput);

      const parts = formattedValue.split("/");

      if (parts.length == 3) {
        const day = parseInt(parts[0], 10);
        const month = parseInt(parts[1], 10) - 1;
        const year = parseInt(parts[2], 10);

        setSelectedDay(day);
        setSelectedMonth(month);
        setSelectedYear(year);

        setTextInput(formattedValue);

        const newDate = new Date(year, month, day);
        onSelectDate(newDate);
      }

      setDatepickerView(ViewMode.DatesView);
      setDatePickerActive(false);
    }
  };

  const handleDateChange = (event: React.ChangeEvent<HTMLInputElement>) => {
    setTextInput(event.target.value);
  };

  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (
        datepickerRef.current &&
        !datepickerRef.current.contains(event.target as Node)
      ) {
        setDatePickerActive(false);
        setDatepickerView(ViewMode.DatesView);
      }
    };

    document.addEventListener("click", handleClickOutside);

    return () => {
      document.removeEventListener("click", handleClickOutside);
    };
  }, []);

  return (
    <MainContainer ref={datepickerRef}>
      <Textbox
        data-testid="datepicker-input-element"
        handleChange={handleDateChange}
        value={textInput}
        onKeyDown={handleKeyPress}
        type="text"
        label="strGetInputDate"
        placeholder={""}
      />
      <Button onClick={handleDatePickerClick}></Button>
      <ContainerDropDown active={isDatePickerActive}>
        <ContainerNavBar>
          {prevMthElement}
          {mthElement}
          {nextMthElement}
          {prevYearElement}
          {yearElement}
          {nextYearElement}
        </ContainerNavBar>
        <DateElement
          active={datepickerView}
          selectedDay={selectedDay}
          selectedMonth={selectedMonth}
          selectedYear={selectedYear}
          handleDateClick={handleDateClick}
        />
        <MonthsElement
          active={datepickerView}
          handleMonthsClick={handleMonthsClick}
        />
        <YearsElement
          active={datepickerView}
          handleYearsClick={handleYearsClick}
          selectedGroupIndex={selectedGroupIndex}
        />
      </ContainerDropDown>
    </MainContainer>
  );
};

export default Datepicker;
