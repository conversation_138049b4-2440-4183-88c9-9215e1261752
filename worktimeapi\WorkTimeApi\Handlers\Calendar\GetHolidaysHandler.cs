﻿using MediatR;
using WorkTimeApi.Common.Requests.Calendar;
using WorkTimeApi.Database.Services.Interfaces;

namespace WorkTimeApi.Handlers.Calendar
{
    public class GetHolidaysHandler : IRequestHandler<GetHolidaysRequest, IResult>
    {
        private readonly IHolidayService _holidayService;

        public GetHolidaysHandler(IHolidayService holidayService)
        {
            _holidayService = holidayService;
        }

        public async Task<IResult> Handle(GetHolidaysRequest request, CancellationToken cancellationToken)
        {
            var monthlyHolidaySummary = await _holidayService.GetHolidaysAsync(request.Year, request.Month);

            return Results.Ok(monthlyHolidaySummary);
        }
    }
}