﻿namespace Gateway.Extenstions.HttpClientExtensions
{
    public static class HttpClientExtension
    {
        public static async Task<HttpResponseMessage> GetAuthAsync(this HttpClient client, string requestUri, IHttpContextAccessor httpContextAccessor)
        {
            var context = httpContextAccessor.HttpContext;
            var authorizationHeader = context?.Request.Headers["Authorization"].FirstOrDefault();
            var refreshTokenHeader = context?.Request.Headers["Refresh-Token"].FirstOrDefault();
            var companyIdHeader = context?.Request.Headers["company-id"].FirstOrDefault();

            if (!string.IsNullOrEmpty(authorizationHeader))
            {
                client.DefaultRequestHeaders.Remove("Authorization");
                client.DefaultRequestHeaders.TryAddWithoutValidation("Authorization", authorizationHeader);
            }

            if (!string.IsNullOrEmpty(refreshTokenHeader))
            {
                client.DefaultRequestHeaders.Remove("refresh-token");
                client.DefaultRequestHeaders.TryAddWithoutValidation("refresh-token", refreshTokenHeader);
            }

            if (!string.IsNullOrEmpty(companyIdHeader))
            {
                client.DefaultRequestHeaders.Remove("company-id");
                client.DefaultRequestHeaders.TryAddWithoutValidation("company-id", companyIdHeader);
            }

            return await client.GetAsync(requestUri);
        }

        public static Task<HttpResponseMessage> PostAsJsonAuthAsync(this HttpClient client, string requestUri, object content, IHttpContextAccessor httpContextAccessor)
        {
            var context = httpContextAccessor.HttpContext;
            var authorizationHeader = context?.Request.Headers["Authorization"].FirstOrDefault();
            var refreshTokenHeader = context?.Request.Headers["Refresh-Token"].FirstOrDefault();
            var companyIdHeader = context?.Request.Headers["company-id"].FirstOrDefault();

            if (!string.IsNullOrEmpty(authorizationHeader))
            {
                client.DefaultRequestHeaders.Remove("Authorization");
                client.DefaultRequestHeaders.TryAddWithoutValidation("Authorization", authorizationHeader);
            }

            if (!string.IsNullOrEmpty(refreshTokenHeader))
            {
                client.DefaultRequestHeaders.Remove("refresh-token");
                client.DefaultRequestHeaders.TryAddWithoutValidation("refresh-token", refreshTokenHeader);
            }

            if (!string.IsNullOrEmpty(companyIdHeader))
            {
                client.DefaultRequestHeaders.Remove("company-id");
                client.DefaultRequestHeaders.TryAddWithoutValidation("company-id", companyIdHeader);
            }

            return client.PostAsJsonAsync(requestUri, content);
        }
    }
}
