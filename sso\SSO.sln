﻿
Microsoft Visual Studio Solution File, Format Version 12.00
# Visual Studio Version 17
VisualStudioVersion = 17.0.32126.317
MinimumVisualStudioVersion = 10.0.40219.1
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "SSO", "SSO\SSO.csproj", "{A4EC5D7F-9157-47BA-8FF0-7E62D54318D0}"
EndProject
Project("{2150E333-8FDC-42A3-9474-1A3956D46DE8}") = "Solution Items", "Solution Items", "{3860F5EA-F0B8-4A37-A31B-E9C91A5719F0}"
	ProjectSection(SolutionItems) = preProject
		.editorconfig = .editorconfig
		deploy-image-script.ps1 = deploy-image-script.ps1
		docker-compose.yml = docker-compose.yml
		Dockerfile = Dockerfile
		README.MD = README.MD
	EndProjectSection
EndProject
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "SSO.Database", "SSO.Database\SSO.Database.csproj", "{BCDB1B67-1AA5-40D3-B693-1620DB02D874}"
EndProject
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "SSO.Common", "SSO.Common\SSO.Common.csproj", "{7AE8FC2C-0259-4579-8644-123CA56F7E21}"
EndProject
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "SSO.Tests", "SSO.Tests\SSO.Tests.csproj", "{FF610E1E-FD60-44C6-BABF-05054AE3BFE6}"
EndProject
Global
	GlobalSection(SolutionConfigurationPlatforms) = preSolution
		Debug|Any CPU = Debug|Any CPU
		Docker|Any CPU = Docker|Any CPU
		Release|Any CPU = Release|Any CPU
		Testing|Any CPU = Testing|Any CPU
	EndGlobalSection
	GlobalSection(ProjectConfigurationPlatforms) = postSolution
		{A4EC5D7F-9157-47BA-8FF0-7E62D54318D0}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{A4EC5D7F-9157-47BA-8FF0-7E62D54318D0}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{A4EC5D7F-9157-47BA-8FF0-7E62D54318D0}.Docker|Any CPU.ActiveCfg = Docker|Any CPU
		{A4EC5D7F-9157-47BA-8FF0-7E62D54318D0}.Docker|Any CPU.Build.0 = Docker|Any CPU
		{A4EC5D7F-9157-47BA-8FF0-7E62D54318D0}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{A4EC5D7F-9157-47BA-8FF0-7E62D54318D0}.Release|Any CPU.Build.0 = Release|Any CPU
		{A4EC5D7F-9157-47BA-8FF0-7E62D54318D0}.Testing|Any CPU.ActiveCfg = Testing|Any CPU
		{A4EC5D7F-9157-47BA-8FF0-7E62D54318D0}.Testing|Any CPU.Build.0 = Testing|Any CPU
		{BCDB1B67-1AA5-40D3-B693-1620DB02D874}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{BCDB1B67-1AA5-40D3-B693-1620DB02D874}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{BCDB1B67-1AA5-40D3-B693-1620DB02D874}.Docker|Any CPU.ActiveCfg = Docker|Any CPU
		{BCDB1B67-1AA5-40D3-B693-1620DB02D874}.Docker|Any CPU.Build.0 = Docker|Any CPU
		{BCDB1B67-1AA5-40D3-B693-1620DB02D874}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{BCDB1B67-1AA5-40D3-B693-1620DB02D874}.Release|Any CPU.Build.0 = Release|Any CPU
		{BCDB1B67-1AA5-40D3-B693-1620DB02D874}.Testing|Any CPU.ActiveCfg = Testing|Any CPU
		{BCDB1B67-1AA5-40D3-B693-1620DB02D874}.Testing|Any CPU.Build.0 = Testing|Any CPU
		{7AE8FC2C-0259-4579-8644-123CA56F7E21}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{7AE8FC2C-0259-4579-8644-123CA56F7E21}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{7AE8FC2C-0259-4579-8644-123CA56F7E21}.Docker|Any CPU.ActiveCfg = Docker|Any CPU
		{7AE8FC2C-0259-4579-8644-123CA56F7E21}.Docker|Any CPU.Build.0 = Docker|Any CPU
		{7AE8FC2C-0259-4579-8644-123CA56F7E21}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{7AE8FC2C-0259-4579-8644-123CA56F7E21}.Release|Any CPU.Build.0 = Release|Any CPU
		{7AE8FC2C-0259-4579-8644-123CA56F7E21}.Testing|Any CPU.ActiveCfg = Testing|Any CPU
		{7AE8FC2C-0259-4579-8644-123CA56F7E21}.Testing|Any CPU.Build.0 = Testing|Any CPU
		{FF610E1E-FD60-44C6-BABF-05054AE3BFE6}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{FF610E1E-FD60-44C6-BABF-05054AE3BFE6}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{FF610E1E-FD60-44C6-BABF-05054AE3BFE6}.Docker|Any CPU.ActiveCfg = Docker|Any CPU
		{FF610E1E-FD60-44C6-BABF-05054AE3BFE6}.Docker|Any CPU.Build.0 = Docker|Any CPU
		{FF610E1E-FD60-44C6-BABF-05054AE3BFE6}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{FF610E1E-FD60-44C6-BABF-05054AE3BFE6}.Release|Any CPU.Build.0 = Release|Any CPU
		{FF610E1E-FD60-44C6-BABF-05054AE3BFE6}.Testing|Any CPU.ActiveCfg = Testing|Any CPU
		{FF610E1E-FD60-44C6-BABF-05054AE3BFE6}.Testing|Any CPU.Build.0 = Testing|Any CPU
	EndGlobalSection
	GlobalSection(SolutionProperties) = preSolution
		HideSolutionNode = FALSE
	EndGlobalSection
	GlobalSection(ExtensibilityGlobals) = postSolution
		SolutionGuid = {2D465832-7C49-41F4-9335-15DF1B69B428}
	EndGlobalSection
EndGlobal
