﻿using MediatR;
using SSO.Common.Requests;
using SSO.Services.Interfaces;

namespace SSO.Handlers
{
    public class ValidateRefreshTokenHandler : IRequestHandler<ValidateRefreshTokenRequest, IResult>
    {
        private readonly IJWTValidator _jwtValidator;

        public ValidateRefreshTokenHandler(IJWTValidator jwtValidator)
        {
            _jwtValidator = jwtValidator;
        }

        public Task<IResult> Handle(ValidateRefreshTokenRequest request, CancellationToken cancellationToken)
        {
            var claimsPrincipal = _jwtValidator.ValidateToken(request.RefreshToken);
            return Task.FromResult(claimsPrincipal is null
                ? Results.BadRequest()
                : Results.Ok());
        }
    }
}
