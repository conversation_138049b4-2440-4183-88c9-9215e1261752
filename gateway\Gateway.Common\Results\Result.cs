﻿namespace Gateway.Common.Results
{
    public readonly struct Result<TValue>
    {
        private Result(TValue value)
        {
            IsError = false;
            IsWarning = false;
            Value = value;
            ValidationError = default;
            ValidationWarning = default;
        }

        private Result(ValidationResult validationError)
        {
            IsError = true;
            IsWarning = false;
            Value = default;
            ValidationError = validationError;
            ValidationWarning = default;
        }

        public Result(TValue value, ValidationResult validationWarnings)
        {
            IsError = false;
            IsWarning = validationWarnings.HasValidationWarnings;
            Value = value;
            ValidationError = default;
            ValidationWarning = validationWarnings;
        }

        public readonly bool IsError { get; }

        public readonly bool IsWarning { get; }

        public readonly bool IsSuccess => !IsError;

        public readonly TValue? Value { get; }

        public readonly ValidationResult ValidationError { get; }

        public readonly ValidationResult ValidationWarning { get; }

        public static implicit operator Result<TValue>(TValue value) => new(value);

        public static implicit operator Result<TValue>(ValidationResult validationError) => new(validationError);

        public TResult Match<TResult>(Func<TValue, TResult> success, Func<ValidationResult, TResult> failure)
            => IsError ? failure(ValidationError) : success(Value!);
    }
}
