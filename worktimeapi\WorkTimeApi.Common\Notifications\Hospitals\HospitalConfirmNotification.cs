﻿using WorkTimeApi.Common.DTOs.Absences;

namespace WorkTimeApi.Common.Notifications.Hospitals
{
    public class HospitalConfirmNotification : BaseNotification<AbsenceHospitalDTO>
    {
        public Guid UserId { get; }

        public HospitalConfirmNotification(AbsenceHospitalDTO payload, Guid companyId, Guid userId, string creatorName)
            : base(payload, companyId, NotificationsName.Hospitals.Approved.Push, creatorName)
        {
            UserId = userId;

            if (payload != null)
            {
                Url = $"{companyId}/attendance?date={payload.FromDate:MM.yyyy}&absenceId={payload.Id}&fromNotification=true";
            }
        }
    }
}
