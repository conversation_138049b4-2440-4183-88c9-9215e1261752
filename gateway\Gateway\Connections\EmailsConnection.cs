using Gateway.Common.Models.DTOs.Requests;
using Gateway.Connections.Interfaces;
using WorkTimeApi.Common.Requests.Employees;

namespace Gateway.Connections
{
    public class EmailsConnection : IEmailsConnection
    {
        private readonly HttpClient _httpClient;
        private readonly string _userRegistrationsApiUrl;

        public EmailsConnection(HttpClient httpClient, IConfiguration configuration)
        {
            _httpClient = httpClient;
            _userRegistrationsApiUrl = configuration["EmailsApiBaseUrl"] ?? throw new ArgumentException("���� �������� �������� �� EmailsApiBaseUrl � appsettings");
        }

        public async Task<HttpResponseMessage> ShareCompanyAsync(SendCompanyInvitationEmailRequest shareCompanyRequest)
        {
            var response = await _httpClient.PostAsJsonAsync(_userRegistrationsApiUrl + "worktime/company-invitation", shareCompanyRequest);

            var content = await response.Content.ReadAsStringAsync();

            return response;
        }

        public async Task<HttpResponseMessage> EmployeeRequestAsync(SendEmployeeCompanyRequest employeeCompanyRequest)
        {
            var response = await _httpClient.PostAsJsonAsync(_userRegistrationsApiUrl + "worktime/employee-request", employeeCompanyRequest);

            var content = await response.Content.ReadAsStringAsync();

            return response;
        }
    }
}