using Gateway.Common.Requests;
using MediatR;
using Microsoft.AspNetCore.Http;
using System.ComponentModel.DataAnnotations;

namespace WorkTimeApi.Common.Requests.Absences
{
    public class GetCompanyAbsencesByExportStatusesRequest : BaseRequest, IRequest<IResult>
    {
        public Guid CompanyId { get; set; }

        [RegularExpression(@"^(0[1-9]|1[0-2])\.(19|20)\d{2}$",
            ErrorMessage = "Month must be in format MM.yyyy (e.g., 08.2025)")]
        public string Month { get; set; } = string.Empty;

        public bool Exported { get; set; }
    }
}
