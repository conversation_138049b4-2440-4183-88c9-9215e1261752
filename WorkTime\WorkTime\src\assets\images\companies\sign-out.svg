<svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" width="13" height="15" viewBox="0 0 13 15">
  <defs>
    <linearGradient id="linear-gradient" y1="0.5" x2="1" y2="0.5" gradientUnits="objectBoundingBox">
      <stop offset="0" stop-color="#fff" stop-opacity="0.302"/>
      <stop offset="1" stop-color="#fff" stop-opacity="0"/>
    </linearGradient>
  </defs>
  <g id="Sign_out" data-name="Sign out" transform="translate(0.5 0.5)">
    <path id="door" d="M17,4H9.5A1.64,1.64,0,0,0,8,5.75v10.5A1.64,1.64,0,0,0,9.5,18H17" transform="translate(-8 -4)" stroke="#d4dce6" stroke-linecap="round" stroke-width="1" fill-rule="evenodd" fill="url(#linear-gradient)"/>
    <line id="arrow" x2="8" transform="translate(3.5 7)" fill="none" stroke="#c6d1de" stroke-linecap="round" stroke-width="1"/>
    <path id="arrow_head" data-name="arrow head" d="M11,8l2.446,1.939a1.292,1.292,0,0,1,0,2.121L11,14" transform="translate(-2 -4)" fill="none" stroke="#c6d1de" stroke-linecap="round" stroke-width="1" fill-rule="evenodd"/>
  </g>
</svg>
