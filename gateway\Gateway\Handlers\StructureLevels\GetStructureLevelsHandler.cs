﻿using Gateway.Connections.Interfaces;
using WorkTimeApi.Common.Requests.StructureLevels;
using MediatR;

namespace Gateway.Handlers.StructureLevels
{
	public class GetStructureLevelsHandler : IRequestHandler<GetStructureLevelsRequest, IResult>
	{
		private readonly IWorkTimeApiConnection _workTimeApiConnection;

		public GetStructureLevelsHandler(IWorkTimeApiConnection workTimeApiConnection)
		{
			_workTimeApiConnection = workTimeApiConnection;
		}

		public async Task<IResult> Handle(GetStructureLevelsRequest request, CancellationToken cancellationToken)
		{
			var response = await _workTimeApiConnection.GetStructureLevelsAsync(request.CompanyId);

			if (!response.IsSuccessStatusCode)
				return Results.StatusCode((int)response.StatusCode);

			return Results.Text(await response.Content.ReadAsStringAsync(cancellationToken));
		}
	}
}
