﻿using Serilog.Sinks.OpenSearch;
using Serilog;
using System.Reflection;
using Microsoft.Extensions.DependencyInjection;

namespace Gateway.Common.Extensions.LoggerExtensions
{
    public static class LoggerExtension
    {
        public static void AddOpenSearchLogging(this IServiceCollection services, bool isInformationIncluded = false)
        {
            var environmentName = Environment.GetEnvironmentVariable("ASPNETCORE_ENVIRONMENT");

            var loggerConfig = new LoggerConfiguration()
                    .Enrich.FromLogContext()
                    .Enrich.WithProperty("Environment", environmentName ?? "Unknown")
                    .Enrich.WithProperty("ApplicationName", Assembly.GetEntryAssembly()?.GetName().Name);

            loggerConfig = isInformationIncluded ? loggerConfig.MinimumLevel.Information() : loggerConfig.MinimumLevel.Error();

            Log.Logger = loggerConfig
                    .WriteTo.OpenSearch(new OpenSearchSinkOptions(new Uri("http://192.168.60.78:9200"))
                    {
                        IndexFormat = $"{DateTime.Now:yyyy-MM-dd}",
                        AutoRegisterTemplate = true
                    })
                    .CreateLogger();

            services.AddLogging(loggingBuilder => loggingBuilder.AddSerilog(dispose: true));
        }
    }
}
