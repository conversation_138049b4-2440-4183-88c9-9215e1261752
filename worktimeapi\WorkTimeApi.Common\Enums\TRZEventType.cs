﻿using System.ComponentModel;

namespace WorkTimeApi.Common.Enums
{
    public enum TRZEventType
    {
        [Description("Платен годишен отпуск")]
        ПлатенГодишенОтпуск = 1,

        [Description("Платен годишен отпуск за минала година")]
        ПлатенГодишенОтпускЗаМиналаГодина = 2,

        [Description("Неплатен отпуск с осигурителен стаж от осигурител")]
        НеплатенСОсигурителенСтажОтОсигурител = 3,

        [Description("Неплатен отпуск с осигурителен стаж от осигурен")]
        НеплатенСОсигурителенСтажОтОсигурен = 4,

        [Description("Неплатен отпуск за отглеждане на дете до 8 години")]
        НеплатенЗаДетеДо8 = 5,

        [Description("Неплатен отпуск без осигурителен стаж от осигурител")]
        НеплатенБезСтажОтОсигурител = 6,

        [Description("Неплатен отпуск без осигурителен стаж от осигурен")]
        НеплатенБезСтажОтОсигурен = 7,

        [Description("Самоотлъчване")]
        Самоотлъчване = 8,

        [Description("Болничен")]
        Болничен = 9,

        [Description("Гледане на болен член от семейството")]
        ГледанеНаБоленЧлен = 10,

        [Description("Трудова злополука")]
        ТрудоваЗлополука = 11,

        [Description("Професионална болест")]
        ПрофесионалнаБолест = 12,

        [Description("Неплатен отпуск за временна нетрудоспособност")]
        НеплатенПоНетрудоспособност = 13,

        [Description("Болничен по бременност")]
        БолниченПоБременност = 14,

        [Description("Болничен след раждане")]
        БолниченСледРаждане = 15,

        [Description("Неплатен отпуск за бременност и раждане")]
        НеплатенЗаБременност = 16,

        [Description("Отпуск за майка след раждане от 135 до 410")]
        ОтпускМайка135До410 = 17,

        [Description("Отпуск за баща за гледане на дете над 6 месеца")]
        ОтпускБащаНад6Месеца = 18,

        [Description("Отпуск до 15 дни при раждане на дете")]
        Отпуск15ДниРаждане = 19,

        [Description("Отпуск за отглеждане на дете до 2 години")]
        ОтпускЗаДетеДо2 = 20,

        [Description("Платен отпуск по други чл от КТ")]
        ПлатенПоДругиЧленове = 21,

        [Description("Отпуск при осиновяване на дете до 5-годишна възраст")]
        ОтпускОсиновяванеДо5 = 22,

        [Description("Неплатен отпуск при осиновяване на дете до 5-годишна възраст")]
        НеплатенОсиновяванеДо5 = 23,

        [Description("Отпуск за баща при осиновяване на дете до 5-годишна възраст")]
        БащаОсиновителДо5 = 24,

        [Description("Платен отпуск по чл. 173а, ал. 1 от КТ")]
        ПлатенПоЧл173а = 25,

        [Description("Отпуск за отглеждане на дете до 8-годишна възраст от бащата (осиновителя)")]
        БащаГледаДетеДо8 = 26
    }
}
