﻿using Gateway.Common.Extenstions.EndpointExtensions;
using Microsoft.AspNetCore.Builder;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.OpenApi.Models;
using System.Reflection;

namespace Gateway.EndpointDefinitions.Swagger
{
    public class SwaggerEndpointDefinition : IEndpointDefinition
    {
        public void DefineEndpoints(WebApplication app)
        {
            app.UseSwagger();
            app.UseSwaggerUI(c => c.SwaggerEndpoint("/swagger/v1/swagger.json", Assembly.GetEntryAssembly()?.GetName().Name));
        }

        public void DefineServices(IServiceCollection services)
        {
            services.AddEndpointsApiExplorer();
            services.AddSwaggerGen(c =>
            {
                c.SwaggerDoc("v1", new OpenApiInfo { Title = Assembly.GetEntryAssembly()?.GetName().Name , Version = "v1" });

                c.AddSecurityDefinition("AuthorizationScheme", new OpenApiSecurityScheme
                {
                    Description = "Попълва се стойност за header-a authorization",
                    Type = SecuritySchemeType.ApiKey,
                    Name = "Authorization",
                    In = ParameterLocation.Header,
                    Scheme = "AuthorizationScheme"
                });

                c.AddSecurityDefinition("RefreshTokenScheme", new OpenApiSecurityScheme
                {
                    Description = "Попълва се стойност за header-a Refresh-Token",
                    Type = SecuritySchemeType.ApiKey,
                    Name = "Refresh-Token",
                    In = ParameterLocation.Header,
                    Scheme = "RefreshTokenScheme"
                });

                var scheme = new OpenApiSecurityScheme
                {
                    Reference = new OpenApiReference
                    {
                        Type = ReferenceType.SecurityScheme,
                        Id = "AuthorizationScheme"
                    },
                    In = ParameterLocation.Header
                };

                var refreshTokenScheme = new OpenApiSecurityScheme
                {
                    Reference = new OpenApiReference
                    {
                        Type = ReferenceType.SecurityScheme,
                        Id = "RefreshTokenScheme"
                    },
                    In = ParameterLocation.Header
                };

                var requirement = new OpenApiSecurityRequirement
                {
                    { scheme, new List<string>() },
                    { refreshTokenScheme, new List<string>() }
                };

                c.AddSecurityRequirement(requirement);
            });
        }
    }
}
