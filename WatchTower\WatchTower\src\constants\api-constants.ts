const gatewayApi = () => {
  switch (import.meta.env.VITE_GATEWAY_API) {
    case "Dev":
      return "https://localhost:7056/";
    case "LocalDocker":
      return "http://host.docker.internal:8080/";
    case "Testing":
      return "http://*************:8080/";
    case "Production":
      return "https://worktime.bg/gateway-api/";
    default:
      return "http://localhost:7056/";
  }
};

const watchTowerApi = () => {
  switch (import.meta.env.VITE_WATCH_TOWER_API) {
    case "Dev":
      return "http://localhost:5030/";
    case "LocalDocker":
      return "http://host.docker.internal:8085/";
    case "Testing":
      return "http://*************:8085/";
    default:
      return "http://localhost:5030/";
  }
};

export const GATEWAY_API_PATH = gatewayApi();
export const WATCH_TOWER_API_PATH = watchTowerApi();
