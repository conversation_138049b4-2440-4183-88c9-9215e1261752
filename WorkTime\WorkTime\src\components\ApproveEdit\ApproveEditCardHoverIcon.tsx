import React, { useRef, useState } from "react";
import { createPortal } from "react-dom";
import styled from "styled-components";
import attentionHoverIcon from "../../assets/images/dot-icons/attention-hover.svg";
import attentionIcon from "../../assets/images/dot-icons/attention.svg";
import ApproveEditCard from "./ApproveEditCard";

interface EditCardHoverIconProps {
  newValue: string | undefined;
  onCancel?: () => Promise<void>;
  onConfirm?: () => Promise<void>;
  position?: "top" | "bottom" | "left" | "right";
}

const IconContainer = styled.div`
  position: relative;
  display: inline-block;
  margin-left: 0.5rem;
`;

const HoverIcon = styled.div`
  width: 1.5rem;
  height: 1.5rem;
  cursor: pointer;
  background: url(${attentionIcon}) center/contain no-repeat;
  transition: all 0.2s ease;

  &:hover,
  ${IconContainer}:hover & {
    background: url(${attentionHoverIcon}) center/contain no-repeat;
  }
`;

const CardWrapper = styled.div<{
  position: "top" | "bottom" | "left" | "right";
  iconRect?: DOMRect;
  isVisible: boolean;
}>`
  position: fixed;
  z-index: 10000;
  opacity: ${({ isVisible }) => (isVisible ? 1 : 0)};
  visibility: ${({ isVisible }) => (isVisible ? "visible" : "hidden")};
  pointer-events: ${({ isVisible }) => (isVisible ? "auto" : "none")};
  transition: opacity 0.2s ease, visibility 0.2s ease;

  ${({ position, iconRect }) => {
    if (!iconRect) return "";

    switch (position) {
      case "top":
        return `
          top: ${iconRect.top}px;
          left: ${iconRect.left + iconRect.width}px;
          transform: translate(-100%, -100%);
          padding-bottom: 0.5rem;
        `;
      case "bottom":
        return `
          top: ${iconRect.bottom}px;
          padding-top: 0.5rem;
          left: ${iconRect.left + iconRect.width}px;
          transform: translateX(-100%);
        `;
      case "left":
        return `
          top: ${iconRect.top + iconRect.height}px;
          left: ${iconRect.left}px;
          padding-right: 0.5rem;
          transform: translate(-100%, -50%);
        `;
      case "right":
        return `
          top: ${iconRect.top + iconRect.height}px;
          padding-left: 0.5rem;
          left: ${iconRect.right}px;
          transform: translateY(-50%);
        `;
      default:
        return `
          top: ${iconRect.bottom}px;
          padding-top: 0.5rem;
          left: ${iconRect.left + iconRect.width}px;
          transform: translateX(-100%);
        `;
    }
  }}
`;

const ApproveEditCardHoverIcon: React.FC<EditCardHoverIconProps> = ({
  newValue,
  onCancel,
  onConfirm,
  position = "bottom",
}) => {
  const iconRef = useRef<HTMLDivElement>(null);
  const [isVisible, setIsVisible] = useState(false);
  const [iconRect, setIconRect] = useState<DOMRect | undefined>(undefined);

  const handleMouseEnter = () => {
    if (iconRef.current) {
      const rect = iconRef.current.getBoundingClientRect();
      setIconRect(rect);
      setIsVisible(true);
    }
  };

  const handleMouseLeave = () => {
    setIsVisible(false);
  };

  const portalRoot = document.body;

  return (
    <>
      <IconContainer
        data-testid="edit-card-hover-icon"
        onMouseEnter={handleMouseEnter}
        onMouseLeave={handleMouseLeave}
      >
        <HoverIcon ref={iconRef} data-testid="hover-icon" />
      </IconContainer>
      {createPortal(
        <CardWrapper
          position={position}
          iconRect={iconRect}
          isVisible={isVisible}
          data-testid="card-wrapper"
          onMouseEnter={handleMouseEnter}
          onMouseLeave={handleMouseLeave}
        >
          <ApproveEditCard
            newValue={newValue}
            onCancel={onCancel}
            onConfirm={onConfirm}
          />
        </CardWrapper>,
        portalRoot
      )}
    </>
  );
};

export default ApproveEditCardHoverIcon;
