import { styled } from "styled-components";
import BaseImage from "../assets/images/landingPage/base-image.png";
import StarImage from "../assets/images/landingPage/star.png";
import PaperAirplaneImage from "../assets/images/landingPage/paper-airplane.png";
import LogoImage from "../assets/images/landingPage/microinvest-full-logo.png";
import Translator from "../services/language/Translator";
import { NavLink } from "react-router-dom";

const LandingPageContainer = styled.div`
  position: absolute;
  background-color: var(--landing-page-background-color);
  height: 100%;
  width: 100%;
  display: flex;
  overflow: hidden;
  min-width: 1530px;
  min-height: 760px;
`;

const WavesContainer = styled.svg`
  position: absolute;
  top: 0;
  left: 0;
  height: 330px;
  width: 100%;
  z-index: 1;
  pointer-events: none;
  overflow: hidden;
`;

const BaseStyledImageContainer = styled.div`
  height: 680px;
  position: absolute;
  bottom: 0;
  right: 0;
`;

const LogoStyledImage = styled.img`
  position: fixed;
  right: 0.7rem;
  top: 0.7rem;
  height: 1rem;
  z-index: 1;

  @media (min-width: 1910px) {
    position: absolute;
    left: 1770px;
  }
`;

const BaseStyledImage = styled.img`
  height: 100%;
  -webkit-user-select: none;
  -khtml-user-select: none;
  -moz-user-select: none;
  -o-user-select: none;
  user-select: none;
`;

const StarStyledImage = styled.img`
  height: 15%;
  position: absolute;
  right: 30%;
  bottom: 94%;
  transition: transform 0.3s ease-in-out;

  &:hover {
    transform: scale(1.1) rotate(20deg) translate(-8px, -15px);
  }
`;

const AirplaneOverlay = styled.div`
  position: absolute;
  width: 55%;
  height: 76%;
  top: 20%;
  left: 40%;
  opacity: 0;
  background: var(--landing-page-background-color);
  transition: opacity 0.5s ease-in-out;
`;

const AirplaneContainer = styled.div`
  height: 120px;
  position: absolute;
  right: 85%;
  bottom: 55%;
  transition: transform 0.45s ease-in-out;

  &:hover {
    transform: translate(-90px, -25px);
  }

  &:hover ${AirplaneOverlay} {
    opacity: 1;
  }
`;

const AirplaneStyledImage = styled.img`
  top: 0;
  left: 0;
  height: 120px;
`;

const Header = styled.h1`
  position: absolute;
  top: 350px;
  left: 4rem;
  color: var(--landing-page-header-color);
  font-size: 3.25rem;
  font-family: "Montserrat", sans-serif;
  font-weight: 300;
`;

const TextContainer = styled.div`
  position: absolute;
  width: 30rem;
  top: calc(420px + 4rem);
  left: 4rem;
  font-size: 1.3125rem;
  font-family: "Segoe UI";
  color: var(--landing-page-content-color);
`;

const TextContent = styled.p`
  margin-block: 0;
`;

const LoginButton = styled(NavLink)`
  position: absolute;
  display: flex;
  justify-content: center;
  align-items: center;
  width: 18rem;
  height: 5rem;
  border-radius: 10rem;
  bottom: 10%;
  left: 4rem;
  background: var(--landing-page-content-color);
  border: none;
  color: var(--landing-page-button-text-color);
  font-size: 1.625rem;
  font-family: "Segoe UI";
  text-decoration: none;

  &:hover {
    background: var(--landing-page-content-hover-color);
    cursor: pointer;
  }
`;

const LandingPage = () => {
  return (
    <>
      <link rel="preconnect" href="https://fonts.googleapis.com"></link>
      <link rel="preconnect" href="https://fonts.gstatic.com"></link>
      <link
        href="https://fonts.googleapis.com/css2?family=Montserrat&display=swap"
        rel="stylesheet"
      ></link>

      <LandingPageContainer>
        <WavesContainer>
          <path
            d="M0,0 L0,320 Q300,350 500,230 Q630,170 800,190 Q1100,200 1200,90 Q1300,0 1460,90 T2000 60 T2100 0"
            fill="var(--landing-page-wave-1-color)"
          />
          <path
            d="M0,0 L0,250 Q250,300 500,200 Q630,150 800,145 Q1100,140 1200,70 Q1300,-10 1480,60 T2000 0 L2000,0"
            fill="var(--landing-page-wave-2-color)"
          />
        </WavesContainer>

        <LogoStyledImage src={LogoImage} />

        <Header>
          <Translator getString={"WorkTime"} />
        </Header>

        <TextContainer>
          <TextContent>
            <Translator getString="LandingPageText1" />
          </TextContent>

          <TextContent>
            <Translator getString="LandingPageText2" />
          </TextContent>

          <TextContent>
            <Translator getString="LandingPageText3" />
          </TextContent>
        </TextContainer>

        <LoginButton to="/auth/login">
          <Translator getString={"Login"} />
        </LoginButton>

        <BaseStyledImageContainer>
          <BaseStyledImage src={BaseImage} />

          <StarStyledImage src={StarImage} />

          <AirplaneContainer>
            <AirplaneStyledImage src={PaperAirplaneImage} />
            <AirplaneOverlay />
          </AirplaneContainer>
        </BaseStyledImageContainer>
      </LandingPageContainer>
    </>
  );
};

export default LandingPage;
