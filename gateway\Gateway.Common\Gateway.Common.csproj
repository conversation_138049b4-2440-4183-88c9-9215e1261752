﻿<Project Sdk="Microsoft.NET.Sdk">

  <PropertyGroup>
    <TargetFramework>net8.0</TargetFramework>
    <ImplicitUsings>enable</ImplicitUsings>
    <Nullable>enable</Nullable>
    <Version>0.0.9-preview.20250902144032</Version>
    <Configurations>Debug;Release;Docker;Testing</Configurations>
  </PropertyGroup>

	<ItemGroup>
		<PackageReference Include="MediatR" Version="12.4.0" />
		<PackageReference Include="Serilog" Version="4.0.1" />
		<PackageReference Include="Serilog.Extensions.Logging" Version="8.0.0" />
		<PackageReference Include="Serilog.Sinks.Console" Version="6.0.0" />
		<PackageReference Include="Serilog.Sinks.OpenSearch" Version="1.2.0" />
		<PackageReference Include="StackExchange.Redis" Version="2.8.37" />
		<PackageReference Include="Swashbuckle.AspNetCore" Version="6.7.1" />
	</ItemGroup>

</Project>
