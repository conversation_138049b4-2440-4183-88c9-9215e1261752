﻿using MediatR;
using WorkTimeApi.Models.Mediator;
using WorkTimeApi.Services.Interfaces.Notifications;

namespace WorkTimeApi.Handlers.Mediator
{
    public class DefaltNotificationSettingsHandler(INotificationsService notificationsService) : INotificationHandler<AddDefaultNotificationSettings>
    {
        public async Task Handle(AddDefaultNotificationSettings request, CancellationToken cancellationToken)
        {
             await notificationsService.AddEmployeeNotificationSettings(request.EmployeeId, request.RoleId);
        }
    }
}