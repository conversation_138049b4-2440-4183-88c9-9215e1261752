﻿using Gateway.Common.Extenstions.EndpointExtensions;
using Gateway.Extenstions.MediatorExtensions;
using WorkTimeApi.Common.Requests.Employees;

namespace Gateway.EndpointDefinitions.Users
{
    public class UsersEndpointDefinitions : IEndpointDefinition
    {
        public void DefineEndpoints(WebApplication app)
        {
            app.AuthenticatedGet<LoadUserEmployeeRequest>("/user-employee/{userId}/{companyId}");
        }

        public void DefineServices(IServiceCollection services)
        {
        }
    }
}
