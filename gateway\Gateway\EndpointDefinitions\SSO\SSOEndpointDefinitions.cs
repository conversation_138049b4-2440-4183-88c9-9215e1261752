﻿using Gateway.Common.Extenstions.EndpointExtensions;
using Gateway.Common.Extenstions.MediatorExtensions;
using Gateway.Common.Globals;
using Gateway.Common.Requests.Roles;
using Gateway.Connections;
using Gateway.Connections.Handlers;
using Gateway.Connections.Interfaces;
using Gateway.Extenstions.MediatorExtensions;
using SSO.Common.Requests;
using WorkTimeApi.Common.Requests.Roles;

namespace Gateway.EndpointDefinitions.SSO
{
    public class SSOEndpointDefinitions : IEndpointDefinition
    {
        public void DefineEndpoints(WebApplication app)
        {
            app.MediateGet<AuthenticateRequest>("/sso/auth")
                .MediateGet<HeaderLoginRequest>("/sso/try-login")
                .MediateGet<GoogleLoginRequest>("/sso/try-google-login")
                .MediateGet<FacebookLoginRequest>("/sso/try-facebook-login")
                .MediateGet<MicrosoftLoginRequest>("/sso/try-microsoft-login")
                .MediateGet<GetGeneralRolesRequest>("/sso/roles/general-roles")
                .MediateGet<RecaptchaValidationRequest>("/sso/validate-recaptcha-token")
                .AuthenticatedGet<GetWorkTimeRoleGatewayRequest>("/sso/users/user-worktime-role");

            app.MediatePost<LoginRequest>("/sso/login")
                .MediatePost<LogoutRequest>("/sso/logout")
                .MediatePost<ResetPasswordRequest>("sso/reset-password")
                .MediatePost<ChangeForgottenPasswordRequest>("/sso/change-forgotten-password")
                .AuthenticatedPost<ChangePasswordRequest>("/sso/change-password")
                .AuthenticatedPost<ChangePasswordCodeRequest>("/sso/change-password-code")
                .MediatePost<ValidateRefreshTokenRequest>("/sso/validate-refresh-token")
                .AuthenticatedPost<SetWorkTimeRoleRequest>("/sso/users/worktime-role");
        }

        public void DefineServices(IServiceCollection services)
        {
            services.AddTransient<RequestIdHandler>()
                .AddTransient<UserIdHandler>()
                .AddTransient<AddOriginHeaderHandler>()
                .AddTransient<AddOriginIdHeaderHandler>()
                .AddScoped<GlobalUser>()
                .AddScoped<GlobalEmployee>();

            services.AddHttpContextAccessor();

            services
                .AddHttpClient<ISSOConnection, SSOConnection>()
                .AddHttpMessageHandler<RequestIdHandler>()
                .AddHttpMessageHandler<UserIdHandler>()
                .AddHttpMessageHandler<AddOriginHeaderHandler>()
                .AddHttpMessageHandler<AddOriginIdHeaderHandler>();
        }
    }
}
