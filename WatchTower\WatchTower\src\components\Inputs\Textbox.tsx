import { ChangeEvent, useRef, useState } from "react";
import Translator from "../../services/language/Translator";
import styled from "styled-components";

interface TextboxProps
  extends React.DetailedHTMLProps<
    React.AllHTMLAttributes<HTMLDivElement>,
    HTMLDivElement
  > {
  label: string;
  value: string | number;
  handleChange: (e: ChangeEvent<HTMLInputElement>) => void;
  onKeyDown?: React.KeyboardEventHandler<HTMLInputElement>;
  validation?: {
    isValid: boolean;
    alertMessage: string;
  };
  "data-testid"?: string;
}

const Container = styled.div`
  position: relative;
  margin-top: 0.5rem;
`;

const InputField = styled.input`
  box-sizing: border-box;
  width: 100%;
  border: 0;
  border-radius: 1.9rem;
  padding: 1.25rem 1.25rem 0.6rem;
  background: var(--textbox-color);
  margin: 0.2rem;
  outline: none;
`;

const InputLabel = styled.label<{
  isFocused: boolean;
  text: string | number | undefined;
}>`
  position: absolute;
  top: ${({ isFocused, text }) => (isFocused || text ? "30%" : "50%")};
  left: 1rem;
  font-size: ${({ isFocused, text }) =>
    isFocused || text ? "0.7rem" : "1rem"};
  transform: translateY(-50%);
  transition: top 0.3s ease-out, font-size 0.3s ease;
  color: ${({ isFocused, text }) =>
    isFocused || text
      ? "var(--textbox-label-focused-color)"
      : "var(--textbox-label-blur-color)"};
  user-select: none;
`;

const InputWrapper = styled.div`
  position: relative;
`;

const AlertMessageDiv = styled.div`
  color: var(--text-box-alert-message-color);
`;

const Textbox = (props: TextboxProps) => {
  const [isFocused, setIsFocused] = useState(false);
  const handleFocus = () => setIsFocused(true);
  const handleBlur = () => setIsFocused(false);
  const {
    label,
    value = "",
    type,
    handleChange,
    onKeyDown,
    validation,
    name,
    "data-testid": testId,
  } = props;
  const inputRef = useRef<HTMLInputElement>(null);

  return (
    <Container>
      <InputWrapper>
        <InputField
          ref={inputRef}
          type={type}
          value={value}
          onChange={handleChange}
          onFocus={handleFocus}
          onBlur={handleBlur}
          onKeyDown={onKeyDown}
          name={name}
          data-testid={testId}
        />
        <InputLabel
          isFocused={isFocused}
          text={label}
          onClick={() => inputRef.current?.focus()}
        >
          <Translator getString={label} />
        </InputLabel>
      </InputWrapper>
      {validation?.isValid === false && (
        <AlertMessageDiv>
          <Translator getString={validation.alertMessage} />
        </AlertMessageDiv>
      )}
    </Container>
  );
};

export default Textbox;
