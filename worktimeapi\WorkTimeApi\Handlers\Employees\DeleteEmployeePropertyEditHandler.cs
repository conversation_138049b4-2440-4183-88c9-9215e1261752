using MediatR;
using WorkTimeApi.Common.Requests.Employees;
using WorkTimeApi.Services.Interfaces.Employees;

namespace WorkTimeApi.Handlers.Employees
{
    public class DeleteEmployeePropertyEditHandler : IRequestHandler<DeleteEmployeePropertyEditRequest, IResult>
    {
        private readonly IEmployeesService _employeesService;

        public DeleteEmployeePropertyEditHandler(IEmployeesService employeesService)
        {
            _employeesService = employeesService;
        }

        public async Task<IResult> Handle(DeleteEmployeePropertyEditRequest request, CancellationToken cancellationToken)
        {
            await _employeesService.DeleteEmployeePropertyEditAsync(request.PropertyEditId);

            return Results.Ok();
        }
    }
}
