﻿namespace Gateway.Connections.Handlers
{
    public class RequestIdHandler : DelegatingHandler
    {
        private readonly IHttpContextAccessor _httpContextAccessor;

        public RequestIdHandler(IHttpContextAccessor httpContextAccessor)
        {
            _httpContextAccessor = httpContextAccessor;
        }

        protected override async Task<HttpResponseMessage> SendAsync(HttpRequestMessage request, CancellationToken cancellationToken)
        {
            var requestId = _httpContextAccessor.HttpContext?.Response.Headers["X-Request-ID"].FirstOrDefault();

            if (!string.IsNullOrEmpty(requestId))
            {
                request.Headers.TryAddWithoutValidation("X-Request-ID", requestId);
            }

            return await base.SendAsync(request, cancellationToken);
        }
    }
}   
