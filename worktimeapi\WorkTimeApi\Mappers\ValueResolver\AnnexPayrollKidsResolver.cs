﻿using AutoMapper;
using WorkTimeApi.Common.DTOs.Payrolls;
using WorkTimeApi.Database.Models.TRZ;

namespace WorkTimeApi.Mappers.ValueResolver
{
    public class AnnexPayrollKidsResolver : IValueResolver<AnnexPayrollDTO, TRZAnnexPayroll, ICollection<TRZAnnexPayrollKid>>
    {
        public ICollection<TRZAnnexPayrollKid> Resolve(AnnexPayrollDTO src, TRZAnnexPayroll dest, ICollection<TRZAnnexPayrollKid> destMember, ResolutionContext context)
        {
            var kids = new List<TRZAnnexPayrollKid>();

            if (src.Kids != null)
            {
                kids.AddRange(src.Kids
                    .Where(k => k is not null && k.Id != Guid.Empty)
                    .Select(k => new TRZAnnexPayrollKid { AnnexPayroll = dest, KIDId = k.Id }));
            }

            return kids;
        }
    }
}
