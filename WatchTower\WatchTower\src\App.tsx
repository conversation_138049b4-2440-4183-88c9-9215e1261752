import "./App.css";
import { LanguageProvider } from "./services/language/LanguageProvider";
import AppRouter from "./AppRouter";
import Layout from "./features/Layout";
import { AuthContext, User } from "./features/authentication/AuthContext";
import { useEffect, useState } from "react";
import { initUser } from "./services/authentication/authenticationService";
import { GoogleOAuthProvider } from "@react-oauth/google";
import { ToastContainer } from "react-toastify";
import "react-toastify/dist/ReactToastify.css";
import { PermissionsProvider } from "./features/authorization/PermissionsProvider";
import "bootstrap/dist/css/bootstrap.min.css";
import AppContainer from "./AppContainer";

function App() {
  const [user, setUser] = useState<User>({
    email: undefined,
    hasSignedIn: false,
  });

  useEffect(() => {
    setUser(initUser());
  }, [setUser]);

  return (
    <AuthContext.Provider value={{ user: user, setUser: setUser }}>
      <PermissionsProvider>
        <GoogleOAuthProvider clientId={import.meta.env.VITE_GOOGLE_ID ?? ""}>
          <LanguageProvider>
            <Layout />
            <AppContainer>
              <AppRouter />
              <ToastContainer />
            </AppContainer>
          </LanguageProvider>
        </GoogleOAuthProvider>
      </PermissionsProvider>
    </AuthContext.Provider>
  );
}

export default App;
