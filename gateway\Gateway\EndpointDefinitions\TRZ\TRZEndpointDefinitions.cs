﻿using Gateway.Common.Extenstions.EndpointExtensions;
using Gateway.Extenstions.MediatorExtensions;
using WorkTimeApi.Common.Requests.TRZ;

namespace Gateway.EndpointDefinitions.TRZ
{
    public class TRZEndpointDefinitions : IEndpointDefinition
    {
        public void DefineEndpoints(WebApplication app)
        {
            app.AuthenticatedPost<ImportCompanyRequest>("/trz/companies")
                .AuthenticatedPost<AddTRZEmployeesRequest>("/trz/employees")
                .AuthenticatedGet<GetTRZEmployeesRequest>($"/trz/employees")
                .AuthenticatedPost<AddTRZPendingEventsRequest>("/trz/pending-events")
                .AuthenticatedPost<AddTRZEventsRequest>("/trz/events")
                .AuthenticatedGet<GetTRZEventsRequest>($"/trz/events")
                .AuthenticatedPost<ImportTRZEventRequest>("/trz/event")
                .AuthenticatedPost<UpdateTRZDepartmentsRequest>("/trz/departments")
                .AuthenticatedPost<ImportPendingEmployeesRequest>("/pending-employee-payrolls/import")
                .AuthenticatedGet<LoadPendingEmployeePayrollRequest>("/pending-employee-payrolls");
        }

        public void DefineServices(IServiceCollection services)
        {
        }
    }
}
