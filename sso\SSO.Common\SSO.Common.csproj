﻿<Project Sdk="Microsoft.NET.Sdk">

  <PropertyGroup>
    <TargetFramework>net8.0</TargetFramework>
    <ImplicitUsings>enable</ImplicitUsings>
    <Nullable>enable</Nullable>
	  <Version>0.0.8-preview.20250715114722</Version>
	<Configurations>Debug;Release;Docker;Testing</Configurations>
  </PropertyGroup>
	
    <ItemGroup Condition="'$(Configuration)'=='Debug'">
		<ProjectReference Include="..\..\gateway\Gateway.Common\Gateway.Common.csproj" />
	</ItemGroup>
	<ItemGroup Condition="'$(Configuration)'!='Debug'">
		<PackageReference Include="Gateway.Common" Version="0.0.9-preview.20250902144032" />
	</ItemGroup>
	<ItemGroup>
	  <Folder Include="Requests\Roles\" />
	</ItemGroup>

</Project>
