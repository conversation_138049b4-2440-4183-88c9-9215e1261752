﻿using Microsoft.EntityFrameworkCore;
using SSO.Common;
using SSO.Database.Models;

namespace SSO.Database
{
    public class SSODbContext(DbContextOptions<SSODbContext> options) : DbContext(options)
    {
        public DbSet<User> Users { get; set; }

        public DbSet<RefreshToken> RefreshTokens { get; set; }

        public DbSet<Role> Roles { get; set; }

        public DbSet<RoleUser> RoleUsers { get; set; }

        public DbSet<Permission> Permissions { get; set; }

        protected override void OnModelCreating(ModelBuilder modelBuilder)
        {
            modelBuilder.Entity<RoleUser>()
                .<PERSON><PERSON><PERSON>(ur => new { ur.UsersId, ur.RolesId, ur.UserRegistrationCompanyId });

            modelBuilder.Entity<User>()
                .HasMany(u => u.UserRoles)
                .WithOne(ur => ur.User)
                .HasForeignKey(ur => ur.UsersId);

            modelBuilder.Entity<Role>()
                .HasMany(r => r.UserRoles)
                .WithOne(ur => ur.Role)
                .HasForeignKey(ur => ur.RolesId);

            var permissions = GetPermissions(typeof(Permissions));

            var data = permissions
                .Select(p => new Permission()
                {
                    Id = Common.Permissions.CreateGuidFromName(p),
                    Name = p
                })
                .ToList();

            modelBuilder
                .Entity<Permission>()
                .HasData(data);

            var supportChatRole = new Role
            {
                Id = Common.Permissions.CreateGuidFromName("SupportChatServiceRole"),
                UserRegistrationCompanyId = -10000,
                Name = "SupportChatServiceRole"
            };

            modelBuilder
                .Entity<Role>()
                .HasData(supportChatRole);

            base.OnModelCreating(modelBuilder);
        }

        static IEnumerable<string> GetPermissions(Type type)
        {
            var props = type.GetProperties()
                .Select(prop => prop.GetValue(null)?.ToString())
                .Where(value => !string.IsNullOrEmpty(value))
                .ToList();

            var nested = type.GetNestedTypes().SelectMany(GetPermissions);

            return props.Concat(nested);
        }
    }
}