import { useContext } from "react";
import { useNavigate } from "react-router-dom";
import { AuthContext } from "../AuthContext";
import * as msal from "@azure/msal-browser";
import OAuthButton from "../../../components/Inputs/OAuthButton";
import MicrosoftLogo from "../../../assets/images/logos/microsoft-logo.png";
import { microsoftLogin } from "../../../services/authentication/authenticationService";

interface Props {
  returnAfterLogin?: string;
}

const MicrosoftLogin = ({ returnAfterLogin }: Props) => {
  const navigate = useNavigate();
  const { setUser } = useContext(AuthContext);

  const msalConfig = {
    auth: {
      clientId: import.meta.env.VITE_MICROSOFT_ID ?? "",
    },
  };

  const msalInstance = new msal.PublicClientApplication(msalConfig);

  const login = () => {
    msalInstance
      .acquireTokenPopup({
        scopes: ["User.Read", "email"],
      })
      .then((response) => {
        microsoftLogin(response.accessToken).then((email) => {
          if (email) {
            setUser({ email: email, hasSignedIn: true });
            navigate(returnAfterLogin ?? "/");
          }
        });
      });
  };

  return (
    <OAuthButton
      logo={MicrosoftLogo}
      content="Sign In with Microsoft account"
      onClick={login}
    />
  );
};

export default MicrosoftLogin;
