﻿using Gateway.Common.Extenstions.EndpointExtensions;
using Gateway.Extenstions.MediatorExtensions;
using WorkTimeApi.Common.Requests.OnboardingDocuments;

namespace Gateway.EndpointDefinitions.OnboardingDocuments
{
	public class OnboardingDocumentEnpointDefinitions : IEndpointDefinition
	{
		public void DefineEndpoints(WebApplication app)
		{
			app.AuthenticatedPost<AddOnboardingDocumentRequest>("/onboarding-documents/add-document");
		}

		public void DefineServices(IServiceCollection services)
		{
		}
	}
}
