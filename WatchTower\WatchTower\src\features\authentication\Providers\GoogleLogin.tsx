import { useGoogleLogin } from "@react-oauth/google";
import { googleLogin } from "../../../services/authentication/authenticationService";
import { useNavigate } from "react-router";
import { useContext } from "react";
import { AuthContext } from "../AuthContext";
import OAuthButton from "../../../components/Inputs/OAuthButton";
import GoogleLogo from "../../../assets/images/logos/google-logo.png";

interface Props {
  returnAfterLogin?: string;
}

const GoogleLogin = ({ returnAfterLogin }: Props) => {
  const navigate = useNavigate();
  const { setUser } = useContext(AuthContext);

  const login = useGoogleLogin({
    onSuccess: (codeResponse) => {
      googleLogin(codeResponse.code).then((email) => {
        if (email) {
          setUser({ email: email, hasSignedIn: true });
          navigate(returnAfterLogin ?? "/");
        }
      });
    },
    onError: (error) => console.log(error),
    flow: "auth-code",
  });

  return (
    <OAuthButton
      logo={GoogleLogo}
      content="Sign In with Google account"
      onClick={login}
    />
  );
};

export default GoogleLogin;
