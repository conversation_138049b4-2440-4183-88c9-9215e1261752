﻿namespace Gateway.Common.Results
{
    public readonly struct ValidationResult
    {
        public ValidationResult()
        {

        }

        public ValidationResult(List<int> validationErrors, Guid id)
        {
            ValidationErrors = validationErrors;
            Id = id;
        }

        public ValidationResult(List<int> validationErrors)
        {
            ValidationErrors = validationErrors;
        }

        public Guid? Id { get; }

        public readonly List<int> ValidationErrors { get; } = [];

        public readonly List<int> ValidationWarnings { get; } = [];

        public readonly bool HasValidationErrors => ValidationErrors != null && ValidationErrors.Count != 0;

        public readonly bool HasValidationWarnings => ValidationWarnings != null && ValidationWarnings.Count != 0;

        public void AddError(Enum error) => ValidationErrors.Add(Convert.ToInt32(error));

        public void AddWarning(Enum error) => ValidationWarnings.Add(Convert.ToInt32(error));
    }
}
