﻿using WorkTimeApi.Common.DTOs.Nomenclatures;
using WorkTimeApi.Common.Enums;

namespace WorkTimeApi.Common.DTOs.Payrolls
{
    public class AnnexPayrollSummary
    {
        public string? ContractNumber { get; set; }
        public string? AnnexPayrollNumber { get; set; }

        public Guid StructureLevelId { get; set; }

        public string? StructureLevelName { get; set; }

        public NomenclatureDTO? Position { get; set; }

        public NomenclatureDTO? ContractReason { get; set; }

        public NomenclatureDTO? IncomeType { get; set; }

        public int? DailyWorktime { get; set; }
        
        public string? Workplace { get; set; }
        
        public DateTime? FromDate { get; set; }
        
        public DateTime? ToDate { get; set; }
                
        public DateTime? ContractTermDate { get; set; }
        
        public DateTime? ContractDate { get; set; }
        
        public DateTime? ContractEndDate { get; set; }
                
        public string? Kid { get; set; }
        
        public string? Ekatte { get; set; }
        
        public string? Nkpd { get; set; }        
    }
}
