trigger:
  branches:
    include:
      - testing
variables:
  - group: deployment-variables
steps:
  - checkout: self
  - script: echo $(Build.SourceBranchName)
    displayName: "Display branch name"
  - task: SSH@0
    displayName: "Create directory"
    inputs:
      sshEndpoint: "docker-linux" 
      runOptions: "inline"
      inline: |
        mkdir -p /builds/WorkTime/$(Build.Repository.Name)/$(Build.SourceBranchName)
      failOnStdErr: true
  - task: CopyFilesOverSSH@0
    displayName: "Copy files from build directory"
    inputs:
      sshEndpoint: 'docker-linux'
      sourceFolder: '$(Build.SourcesDirectory)'
      contents: |
        **
        !**/.git/**
        !**/.vscode/**
      targetFolder: '/builds/WorkTime/$(Build.Repository.Name)/$(Build.SourceBranchName)'
      cleanTargetFolder: true
      overwrite: true
      failOnEmptySource: true
  - task: SSH@0
    displayName: "Build and push Docker image"
    inputs:
      sshEndpoint: "docker-linux" 
      runOptions: "inline"
      inline: |
        cd /builds/WorkTime/$(Build.Repository.Name)/$(Build.SourceBranchName)
        export ASPNETCORE_ENVIRONMENT_VARIABLE=Testing
        export RepositoryName=$(Build.Repository.Name)
        export TagName=$(Build.SourceBranchName)
        export InnerPort=8080
        export OuterPort=8080
        docker build --pull --build-arg ASPNETCORE_ENVIRONMENT_VARIABLE=$ASPNETCORE_ENVIRONMENT_VARIABLE --rm -f "Dockerfile" -t $(Build.Repository.Name):$(Build.SourceBranchName) . 2>&1
        docker-compose -p worktime up -d 2>&1
        docker image prune --force 2>&1
      failOnStdErr: false