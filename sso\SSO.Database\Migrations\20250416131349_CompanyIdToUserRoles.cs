﻿using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace SSO.Database.Migrations
{
    /// <inheritdoc />
    public partial class CompanyIdToUserRoles : Migration
    {
        /// <inheritdoc />
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropPrimaryKey(
                name: "PK_RoleUser",
                table: "RoleUser");

            migrationBuilder.DropIndex(
                name: "IX_RoleUser_UsersId",
                table: "RoleUser");

            migrationBuilder.AddColumn<int>(
                name: "UserRegistrationCompanyId",
                table: "RoleUser",
                type: "int",
                nullable: false,
                defaultValue: -10000);

            migrationBuilder.AddPrimaryKey(
                name: "PK_RoleUser",
                table: "RoleUser",
                columns: new[] { "UsersId", "RolesId", "UserRegistrationCompanyId" });

            migrationBuilder.CreateIndex(
                name: "IX_RoleUser_RolesId",
                table: "RoleUser",
                column: "RolesId");
        }

        /// <inheritdoc />
        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropPrimaryKey(
                name: "PK_RoleUser",
                table: "RoleUser");

            migrationBuilder.DropIndex(
                name: "IX_RoleUser_RolesId",
                table: "RoleUser");

            migrationBuilder.DropColumn(
                name: "UserRegistrationCompanyId",
                table: "RoleUser");

            migrationBuilder.AddPrimaryKey(
                name: "PK_RoleUser",
                table: "RoleUser",
                columns: new[] { "RolesId", "UsersId" });

            migrationBuilder.CreateIndex(
                name: "IX_RoleUser_UsersId",
                table: "RoleUser",
                column: "UsersId");
        }
    }
}
