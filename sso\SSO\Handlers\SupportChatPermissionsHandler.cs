﻿using MediatR;
using SSO.Common.Requests;
using SSO.Services.Interfaces;

namespace SSO.Handlers
{
    public class SupportChatPermissionsHandler(IUsersService usersService) : IRequestHandler<SupportChatPermissionsRequest, IResult>
    {
        public async Task<IResult> Handle(SupportChatPermissionsRequest request, CancellationToken cancellationToken)
            => Results.Ok(await usersService.AddSupportChatPermissionsAsync(request.UserDTO));
    }
}
