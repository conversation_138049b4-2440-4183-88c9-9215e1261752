﻿using Gateway.Connections.Interfaces;
using MediatR;
using Microsoft.Extensions.Primitives;
using SSO.Common.Requests;

namespace Gateway.Handlers.SSO
{
	public class AuthenticateHandler : IRequestHandler<AuthenticateRequest, IResult>
	{
		private readonly ISSOConnection _ssoConnection;

		public AuthenticateHandler(ISSOConnection ssoConnection)
		{
			_ssoConnection = ssoConnection;
		}

		public async Task<IResult> Handle(AuthenticateRequest request, CancellationToken cancellationToken)
		{
			if (request.HttpRequest is null)
				return Results.BadRequest();

			var authorizationHeader = request.HttpRequest.Headers.Authorization;

			if (string.IsNullOrWhiteSpace(authorizationHeader)
				|| request.HttpRequest.Headers.TryGetValue("refresh-token", out StringValues refreshTokenHeader))
				return Results.BadRequest();

			var ssoResponse = await _ssoConnection.AuthenticateAsync(authorizationHeader!, refreshTokenHeader!);

			return Results.StatusCode((int)ssoResponse.StatusCode);
		}
	}
}
