import React, { useState } from "react";
import { act, fireEvent, render, screen } from "@testing-library/react";
import { DropdownContext } from "../Context";
import "@testing-library/jest-dom";

describe("DropdownContext", () => {
  const TestProviderValuesComponent = () => {
    return (
      <DropdownContext.Consumer>
        {(context) => (
          <div>
            <div>{context.isOpen ? "Open" : "Closed"}</div>
            <button onClick={() => context.setIsOpen(!context.isOpen)}>
              Toggle
            </button>
          </div>
        )}
      </DropdownContext.Consumer>
    );
  };

  it("provides the expected values to child components", async () => {
    const Wrapper: React.FC = () => {
      const [isOpen, setIsOpen] = useState(false);
      return (
        <DropdownContext.Provider value={{ isOpen, setIsOpen }}>
          <TestProviderValuesComponent />
        </DropdownContext.Provider>
      );
    };

    render(<Wrapper />);

    expect(screen.getByText("Closed")).toBeInTheDocument();

    const button = screen.getByText("Toggle");

    await act(async () => {
      fireEvent.click(button);
    });

    expect(screen.getByText("Open")).toBeInTheDocument();
  });

  const TestDefaultValueComponent = () => {
    return (
      <DropdownContext.Consumer>
        {(context) =>
          JSON.stringify(context) === JSON.stringify({}) ? (
            <div>Default value correct</div>
          ) : (
            <div>Default value incorrect</div>
          )
        }
      </DropdownContext.Consumer>
    );
  };

  it("has the correct default value", () => {
    render(<TestDefaultValueComponent />);
    expect(screen.getByText("Default value correct")).toBeInTheDocument();
  });
});
