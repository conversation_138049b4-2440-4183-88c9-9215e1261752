import React from "react";
import styled, { css } from "styled-components";
import Translator from "../../services/language/Translator";
import { ViewMode } from "../../models/Enums/ViewMode";

const ContainerMonth = styled.div`
  display: flex;
  justify-content: center;
  align-items: center;
  background-color: var(--datepicker-view-buttons-color);
  padding: 0.5em;
  cursor: pointer;
`;

const ContainerMonths = styled.div<{ active: ViewMode }>`
  display: none;
  grid-template-columns: repeat(4, 1fr);
  grid-template-rows: repeat(3, 1fr);
  gap: 0.3em;
  padding: 0.5em;
  border: solid 0.2em;
  width: 15em;
  border-color: var(--datepicker-view-buttons-color);
  background-color: var(--datepicker-view-border);
  border-radius: 1em;

  ${ContainerMonth}:hover {
    background-color: var(--datepicker-view-buttons-hover-color);
  }

  ${({ active }) =>
    active === ViewMode.MonthsView &&
    `
    display: grid;
  `}
`;
interface MonthsElementProps {
  handleMonthsClick: (selectedMonth: number) => void;
  active: ViewMode;
}

const months = [
  "strJan",
  "strFeb",
  "strMar",
  "strApr",
  "strMay",
  "strJun",
  "strJul",
  "strAug",
  "strSep",
  "strOct",
  "strNov",
  "strDec",
];

const MonthsElement: React.FC<MonthsElementProps> = ({
  handleMonthsClick,
  active,
}) => {
  const populateMonths = () => {
    const monthsElement: JSX.Element[] = [];
    for (let i = 0; i < 12; i++) {
      monthsElement.push(
        <ContainerMonth key={`monts-${i}`} onClick={() => handleMonthsClick(i)}>
          <Translator getString={months[i]} />
        </ContainerMonth>
      );
    }

    return (
      <ContainerMonths data-testid="months-container" active={active}>
        {monthsElement}
      </ContainerMonths>
    );
  };

  return <>{populateMonths()}</>;
};

export default MonthsElement;
