﻿using Gateway.Common.Results;
using Gateway.Connections.Interfaces;
using MediatR;
using WorkTimeApi.Common.DTOs.Absences;
using WorkTimeApi.Common.Requests.TRZ;

namespace Gateway.Handlers.TRZ
{
    public class AddTRZEventsHandler : IRequestHandler<AddTRZEventsRequest, IResult>
    {
        private readonly IWorkTimeApiConnection _workTimeApiConnection;

        public AddTRZEventsHandler(IWorkTimeApiConnection workTimeApiConnection)
        {
            _workTimeApiConnection = workTimeApiConnection;
        }

        public async Task<IResult> Handle(AddTRZEventsRequest request, CancellationToken cancellationToken)
        {
            var addEventsResponse = await _workTimeApiConnection.AddTRZEventsAsync(request);

            return new HttpResponseMessageResult(addEventsResponse);
        }
    }
}
