﻿using Gateway.Common.Globals;
using MediatR;
using WorkTimeApi.Common.DTOs.Companies;
using WorkTimeApi.Common.Enums;
using WorkTimeApi.Services.Interfaces.Employees;

namespace WorkTimeApi.Handlers.Companies
{
    public class LeaveCompanyHandler(IEmployeesService employeesService,GlobalEmployee globalEmployee) : IRequestHandler<LeaveCompanyRequest, IResult>
    {
        public async Task<IResult> Handle(LeaveCompanyRequest request, CancellationToken cancellationToken)
        {
            await employeesService.UpdateEmployeeStatusAsync(globalEmployee.Id, EmployeeCompanyStatus.Left);
            return Results.Ok();
        }
    }
}
