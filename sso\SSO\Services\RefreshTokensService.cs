﻿using AutoMapper;
using SSO.Common.DTOs;
using SSO.Database.Models;
using SSO.Database.Repositories.Interfaces;
using SSO.Services.Interfaces;

namespace SSO.Services
{
    public class RefreshTokensService : IRefreshTokensService
    {
        private readonly IRefreshTokenRepository _refreshTokenRepository;
        private readonly IUsersRepository _userRepository;
        private readonly IMapper _mapper;

        public RefreshTokensService(IRefreshTokenRepository refreshTokenRepository,
            IUsersRepository userRepository,
            IMapper mapper)
        {
            _refreshTokenRepository = refreshTokenRepository;
            _userRepository = userRepository;
            _mapper = mapper;
        }

        public async Task<RefreshTokenDTO> AddRefreshTokenAsync(string token, Guid userId, string email)
        {
            var user = await _userRepository.FindUserByIdAsync(userId);
            if (user is null)
            {
                user = await _userRepository.AddUserAsync(new User
                {
                    Email = email,
                    Id = userId
                });
            }

            var refreshToken = new RefreshToken()
            {
                Token = token,
                IsActive = true,
                User = user
            };

            user.RefreshTokens.Add(refreshToken);
            await _userRepository.UpdateUserAsnyc(user);

            return _mapper.Map<RefreshTokenDTO>(refreshToken);
        }

        public async Task<bool> CheckIfRefreshTokenIsActiveAsync(string tokenStr)
        {
            var token = await _refreshTokenRepository.GetByTokenAsync(tokenStr);
            if (token.IsActive is true)
                return true;

            if (token.DeactivatedDate is null)
                return false;

            TimeSpan expiryPeriod = DateTime.Now - token.DeactivatedDate.Value;
            if (expiryPeriod.TotalMinutes <= 2)
                return true;

            if (token.User is null)
            {
                await _refreshTokenRepository.DeactivateTokenAsync(tokenStr);
                return false;
            }

            await _userRepository.DeactivateAllTokensForUserAsync(token.User);
            return false;
        }

        public Task DeactivateTokenAsync(string tokenStr)
        {
            return _refreshTokenRepository.DeactivateTokenAsync(tokenStr);
        }
    }
}
