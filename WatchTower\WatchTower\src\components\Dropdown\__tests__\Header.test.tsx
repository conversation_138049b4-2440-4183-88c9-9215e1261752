import { render, screen, fireEvent } from "@testing-library/react";
import { DropdownContext } from "../Context";
import { Header } from "../Header";
import "@testing-library/jest-dom";

describe("Header Component", () => {
  it("renders children correctly", () => {
    render(
      <Header>
        <div data-testid="child-component">Test Child</div>
      </Header>
    );

    const child = screen.getByTestId("child-component");
    expect(child).toBeInTheDocument();
    expect(child.textContent).toBe("Test Child");
  });

  it("opens the dropdown when clicked", () => {
    let isOpenState = false;
    const setIsOpen: React.Dispatch<React.SetStateAction<boolean>> = (
      value
    ) => {
      if (typeof value === "boolean") {
        isOpenState = value;
      } else {
        isOpenState = value(isOpenState);
      }
    };

    render(
      <DropdownContext.Provider value={{ isOpen: isOpenState, setIsOpen }}>
        <Header data-testid="header-component">Click Me!</Header>
      </DropdownContext.Provider>
    );

    const header = screen.getByTestId("header-component");
    fireEvent.click(header);
    expect(isOpenState).toBe(true);
  });
});
