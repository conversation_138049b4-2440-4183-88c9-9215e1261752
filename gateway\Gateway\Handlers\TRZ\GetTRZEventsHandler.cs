﻿using Gateway.Common.Results;
using Gateway.Connections.Interfaces;
using MediatR;
using WorkTimeApi.Common.Requests.TRZ;

namespace Gateway.Handlers.TRZ
{
    public class GetTRZEventsHandler : IRequestHandler<GetTRZEventsRequest, IResult>
    {
        private readonly IWorkTimeApiConnection _workTimeApiConnection;

        public GetTRZEventsHandler(IWorkTimeApiConnection workTimeApiConnection)
        {
            _workTimeApiConnection = workTimeApiConnection;
        }

        public async Task<IResult> Handle(GetTRZEventsRequest request, CancellationToken cancellationToken)
        {
            var response = await _workTimeApiConnection.GetTRZEventsAsync(request);

            return new HttpResponseMessageResult(response);
        }
    }
}
