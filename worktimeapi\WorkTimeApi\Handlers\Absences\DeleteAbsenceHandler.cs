﻿using Gateway.Common.Globals;
using MediatR;
using WorkTimeApi.Common.Notifications.Absences;
using WorkTimeApi.Common.Requests.Absences;
using WorkTimeApi.Services.Interfaces.Absences;

namespace WorkTimeApi.Handlers.Absences
{
    public class DeleteAbsenceHandler(IAbsencesService absencesService,
        GlobalUser globalUser,
        IMediator mediator) : IRequestHandler<DeleteAbsenceRequest, IResult>
    {
        public async Task<IResult> Handle(DeleteAbsenceRequest request, CancellationToken cancellationToken)
        {
            var employee = await absencesService.GetEmployeeDTOByAbsenceIdAsync(request.AbsenceId,request.IsHospital);
            var absence = await absencesService.DeleteAbsenceAsync(request.AbsenceId, request.PayrollId, request.IsHospital);

            var notificationDelete = new AbsenceEditNotification(absence, request.CompanyId, globalUser.Id, $"{globalUser.FirstName} {globalUser.LastName}",employee.WorkTimeId);
            await mediator.Publish(notificationDelete, cancellationToken);

            return Results.Ok(absence);
        }
    }
}
