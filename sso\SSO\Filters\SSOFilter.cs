﻿using Gateway.Common.Globals;
using SSO.Services.Interfaces;
using System.Security.Claims;

namespace SSO.Filters
{
    public class SSOFilter : IEndpointFilter
    {
        private readonly IHttpContextAccessor _httpContextAccessor;
        private readonly IJwtGeneratorService _jwtGeneratorService;
        private readonly IRefreshTokensService _refreshTokensService;
        private readonly IJWTValidator _jWTValidator;
        private readonly IUsersService _usersService;
        private readonly GlobalUser _globalUser;

        public SSOFilter(IHttpContextAccessor httpContextAccessor,
            IJwtGeneratorService jwtGeneratorService,
            IRefreshTokensService refreshTokensService,
            IJWTValidator jWTValidator,
            IUsersService usersService,
            GlobalUser globalUser)
        {
            _httpContextAccessor = httpContextAccessor;
            _jwtGeneratorService = jwtGeneratorService;
            _refreshTokensService = refreshTokensService;
            _jWTValidator = jWTValidator;
            _usersService = usersService;
            _globalUser = globalUser;
        }

        public async ValueTask<object?> InvokeAsync(EndpointFilterInvocationContext context, EndpointFilterDelegate next)
        {
            if (_httpContextAccessor.HttpContext is null)
            {
                return TypedResults.Unauthorized();
            }

            ClaimsPrincipal? claimsPrincipal;
            var accessToken = _httpContextAccessor.HttpContext.Request.Headers.Authorization.ToString().Split("Bearer").LastOrDefault()?.Trim();
            claimsPrincipal = _jWTValidator.ValidateToken(accessToken);
            if (claimsPrincipal is not null)
            {
                var userId = claimsPrincipal.Claims?.FirstOrDefault(c => c.Type == "UserId")?.Value ?? throw new Exception("UserId not found");
                var user = await _usersService.GetUserByIdAsync(Guid.Parse(userId));

                _globalUser.Id = user.Id;
                _globalUser.Email = user.Email;

                _httpContextAccessor.HttpContext.User = claimsPrincipal; 
                _httpContextAccessor.HttpContext.Response.Headers.Authorization = _httpContextAccessor.HttpContext.Request.Headers.Authorization;
                _httpContextAccessor.HttpContext.Response.Headers["Refresh-Token"] = _httpContextAccessor.HttpContext.Request.Headers["Refresh-Token"];

                return await next.Invoke(context);
            }

            var refreshToken = _httpContextAccessor.HttpContext.Request.Headers["Refresh-Token"];
            claimsPrincipal = _jWTValidator.ValidateToken(refreshToken);
            if (claimsPrincipal is null)
                return TypedResults.Unauthorized();

            var isRefreshTokenActive = await _refreshTokensService.CheckIfRefreshTokenIsActiveAsync(refreshToken.ToString());
            if (!isRefreshTokenActive)
                return TypedResults.Unauthorized();

            _httpContextAccessor.HttpContext.User = claimsPrincipal;

            _httpContextAccessor.HttpContext.Response.Headers.Append("Authorization", $"Bearer {await _jwtGeneratorService.GenerateAccessTokenAsync(claimsPrincipal.Claims)}");

            // await _refreshTokensService.DeactivateTokenAsync(refreshToken.ToString());
            var newRefreshToken = await _jwtGeneratorService.GenerateRefreshTokenAsync(claimsPrincipal.Claims);
            await _refreshTokensService.AddRefreshTokenAsync(newRefreshToken, 
                new Guid(claimsPrincipal.Claims.FirstOrDefault(c => c.Type == "UserId")?.Value ?? ""), 
                claimsPrincipal.Claims.FirstOrDefault(c => c.Type == ClaimTypes.Email)?.Value
                ?? throw new ArgumentException("Invalid email address"));
            _httpContextAccessor.HttpContext.Response.Headers.Append("Refresh-Token", newRefreshToken);

            return await next.Invoke(context);
        }
    }
}
