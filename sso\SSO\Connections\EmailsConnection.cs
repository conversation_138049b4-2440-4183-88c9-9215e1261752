﻿using SSO.Common.DTOs;
using SSO.Connections.Interfaces;

namespace SSO.Connections
{
    public class EmailsConnection : IEmailsConnection
    {
        private readonly HttpClient _httpClient;
        private readonly string _userRegistrationsApiUrl;

        public EmailsConnection(HttpClient httpClient, IConfiguration configuration)
        {
            _httpClient = httpClient;
            _userRegistrationsApiUrl = configuration["UserRegistrationsBaseUrl"] + "api/emails/";
        }

        public async Task<HttpResponseMessage?> ResetPasswordAsync(ResetPasswordDTO resetPasswordDTO) 
            => await _httpClient.PostAsJsonAsync($"{_userRegistrationsApiUrl}reset-password", resetPasswordDTO);
    }
}
