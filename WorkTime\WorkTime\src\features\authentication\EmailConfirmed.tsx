import { useNavigate } from "react-router-dom";
import Alert from "../../components/Inputs/Alert";
import Button from "../../components/Inputs/Button";
import Container from "../../components/Container";
import { useAuth } from "./AuthContext";
import { useEffect, useState } from "react";
import { isAuthenticated } from "../../services/authentication/authenticationService";
import styled from "styled-components";
import MainWindowContainer from "../../components/MainWindowContainer";

const MainContainer = styled(MainWindowContainer)`
  display: flex;
  flex-wrap: wrap;
  flex: 1 1 auto;
  justify-content: center;
  align-items: center;
`;

const DataContainer = styled(Container)`
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  padding: 3rem;
  box-sizing: border-box;
  width: 40rem;
  gap: 2rem;
  text-align: center;

  @media (max-width: 980px) {
    flex: 100%;
    padding: 2rem;
    width: 100%;
  }
`;

const SuccessIcon = styled.div`
  font-size: 4rem;
  color: var(--success-alert-color);
  margin-bottom: 1rem;
`;

const TitleContainer = styled(Container)`
  display: flex;
  flex-direction: column;
  gap: 1rem;
  align-items: center;
`;

const EmailConfirmed = () => {
  const navigate = useNavigate();
  const { user } = useAuth();
  const [isSignedIn, setIsSignedIn] = useState(false);

  useEffect(() => {
    const hasToken = isAuthenticated();
    setIsSignedIn(user.hasSignedIn && hasToken);
  }, [user]);

  const label = isSignedIn ? "Continue" : "Login";
  const handleGoOn = () => {
    if (isSignedIn) {
      navigate("/");
    } else {
      navigate("/auth/login");
    }
  };

  return (
    <MainContainer>
      <DataContainer>
        <SuccessIcon>✅</SuccessIcon>

        <TitleContainer>
          <Alert
            color="var(--success-alert-color)"
            type="success"
            message="EmailConfirmedSuccessfully"
            data-testid="email-confirmed-alert"
          />
        </TitleContainer>

        <Button onClick={handleGoOn} label={label} data-testid="go-on-button" />
      </DataContainer>
    </MainContainer>
  );
};

export default EmailConfirmed;
