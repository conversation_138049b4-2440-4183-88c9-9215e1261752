﻿using Gateway.Common.Results;
using Gateway.Connections.Interfaces;
using MediatR;
using WorkTimeApi.Common.Requests.Nomenclatures;

namespace Gateway.Handlers.Nomenclatures
{
    public class LoadHospitalTypesHandler : IRequestHandler<GetHospitalTypesRequest, IResult>
    {
        private readonly IWorkTimeApiConnection _workTimeApiConnection;

        public LoadHospitalTypesHandler(IWorkTimeApiConnection workTimeApiConnection)
        {
            _workTimeApiConnection = workTimeApiConnection;
        }

        public async Task<IResult> Handle(GetHospitalTypesRequest request, CancellationToken cancellationToken)
        {
            var response = await _workTimeApiConnection.GetHospitalTypesAsync();

            return new HttpResponseMessageResult(response);
        }
    }
}
