﻿using Microsoft.IdentityModel.Tokens;
using SSO.Common.DTOs;
using SSO.Services.Interfaces;
using System.IdentityModel.Tokens.Jwt;
using System.Security.Claims;
using System.Text;

namespace SSO.Services
{
    public class JwtGeneratorService(IConfiguration configuration, IUsersService usersService) : IJwtGeneratorService
    {
        public Task<string> GenerateAccessTokenAsync(UserDTO userDTO)
        {
            return GenerateTokenAsync(userDTO, DateTime.Now.AddHours(1));
        }

        public Task<string> GenerateAccessTokenAsync(IEnumerable<Claim> claims)
        {
            return GenerateTokenAsync(claims, DateTime.Now.AddHours(1));
        }

        public Task<string> GenerateRefreshTokenAsync(UserDTO userDTO)
        {
            return GenerateTokenAsync(userDTO, DateTime.Now.AddMonths(3));
        }

        public Task<string> GenerateRefreshTokenAsync(IEnumerable<Claim> claims)
        {
            return GenerateTokenAsync(claims, DateTime.Now.AddMonths(3));
        }

        private async Task<string> GenerateTokenAsync(UserDTO userDTO, DateTime duration)
        {
            var securityKey = new SymmetricSecurityKey(Encoding.UTF8.GetBytes(configuration["JwtSecret"] ?? throw new Exception("Wrong configuration")));
            var credentials = new SigningCredentials(securityKey, SecurityAlgorithms.HmacSha256);
            var header = new JwtHeader(credentials);

            var authClaims = new List<Claim>
            {
                new("UserId", userDTO.Id.ToString()),
                new(ClaimTypes.Email, userDTO.Email),
                new(JwtRegisteredClaimNames.Jti, Guid.NewGuid().ToString()),
            };
            var expires = duration;
            var payload = new JwtPayload(configuration["JwtIssuer"], configuration["JwtAudience"], authClaims, DateTime.Now, expires);

            var token = new JwtSecurityToken(header, payload);
            var handler = new JwtSecurityTokenHandler();

            return handler.WriteToken(token);
        }

        private async Task<string> GenerateTokenAsync(IEnumerable<Claim> claims, DateTime duration)
        {
            var userId = claims.FirstOrDefault(c => c.Type == "UserId")?.Value;
            if (userId == null)
                return "";

            var securityKey = new SymmetricSecurityKey(Encoding.UTF8.GetBytes(configuration["JwtSecret"] ?? throw new Exception("Wrong configuration")));
            var credentials = new SigningCredentials(securityKey, SecurityAlgorithms.HmacSha256);
            var header = new JwtHeader(credentials);

            var expires = duration;
            var payload = new JwtPayload(configuration["JwtIssuer"], configuration["JwtAudience"], claims, DateTime.Now, expires);

            var token = new JwtSecurityToken(header, payload);
            var handler = new JwtSecurityTokenHandler();

            return handler.WriteToken(token);
        }
    }
}
