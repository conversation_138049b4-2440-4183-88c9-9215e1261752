using Gateway.Common.Extensions.LoggerExtensions;
using Gateway.Common.Middlewares;
using Gateway.Extenstions.EndpointExtensions;
using Gateway.Extenstions.MapperExtensions;
using WatchTower.Common.Extensions.Aspire;
using WatchTower.Common.Extensions.HealthCheck;
using WatchTower.Extenstions.MetricsExtensions;

var builder = WebApplication.CreateBuilder(args);

builder.AddServiceDefaults();
builder.Services.AddEndpointDefinitions(typeof(Program));
builder.Services.InitializeAutoMapper();

builder.Services.AddCors(p => p.AddPolicy("corsapp", builder =>
{
    builder.WithOrigins("*").AllowAnyMethod().AllowAnyHeader().WithExposedHeaders("Authorization", "Refresh-Token");
}));

builder.Services.AddMediatR(x =>
{
	x.Lifetime = ServiceLifetime.Scoped;
	x.RegisterServicesFromAssembly(typeof(Program).Assembly);
});

builder.Services.AddOpenTelemetryMetrics();
builder.Services.AddOpenSearchLogging();

builder.Services.AddHealthChecks();

var app = builder.Build();

app.MapDefaultEndpoints();
app.MapPrometheusScrapingEndpoint();
app.UseCors("corsapp");
app.UseEndpointDefinitions();
app.UseMiddleware<RequestIdMiddleware>();
app.UseMiddlewareExceptionLogging();
app.UseHealthChecks();

app.Run();
