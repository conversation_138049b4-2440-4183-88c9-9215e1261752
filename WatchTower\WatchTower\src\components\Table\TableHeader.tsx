import styled, { css } from "styled-components";
import { ColumnDefinitionType } from "./Table";
import ColumnFilter from "./ColumnFilter";
import { useState } from "react";
import searchImage from "../../assets/images/table/search.png";
import searchXImage from "../../assets/images/table/searchX.png";
import searchHoverXImage from "../../assets/images/table/searchHoverX.png";

interface TableHeaderProps<T, K extends keyof T>
  extends React.DetailedHTMLProps<
    React.ColHTMLAttributes<HTMLTableColElement>,
    HTMLTableColElement
  > {
  columns: Array<ColumnDefinitionType<T, K>>;
  data: T[];
  setTableData: (tableData: T[]) => void;
  filterColumns(columnKey: string, searchText: string): void;
}

const TableRow = styled.tr`
  background-color: var(--table-row-color);
`;

const TableHeaderCell = styled.th<{
  isHidden: boolean;
}>`
  padding: 0.75rem 0 0.75rem 0;
  text-align: center;
  background-color: var(--table-row-color);
  color: var(--table-header-color);
  padding: 0.5rem;
  opacity: 1;
  font-family: segoe-ui;
  font-style: normal;
  &:first-child {
    border-radius: 1.625rem 0 0 0;
  }
  &:last-child {
    border-radius: 0 1.625rem 0 0;
  }
  ${(p) =>
    p.isHidden &&
    css`
      display: none;
    `};
`;

const SortButton = styled.button<{
  showSort: boolean;
}>`
  background-image: url(${searchImage});
  ${(p) =>
    p.showSort &&
    css`
      background-image: url(${searchXImage});
      &:hover {
        background-image: url(${searchHoverXImage});
      }
    `}
  background-size: cover;
  background-color: transparent;
  margin-left: 0.625rem;
  width: 1.25rem;
  height: 1.25rem;
  text-indent: -999px;
  overflow: hidden;
  border: none;
  cursor: pointer;
  &:hover {
    background-image: url(${searchXImage});
    background-color: transparent;
  }
`;

const TableHeader = <T, K extends keyof T>({
  columns,
  data,
  setTableData,
  filterColumns,
}: TableHeaderProps<T, K>): JSX.Element => {
  const [showSort, setShowSort] = useState(false);

  const handleSort = () => {
    setShowSort((current) => !current);
  };

  return (
    <thead>
      <TableRow>
        <TableHeaderCell isHidden={false}>
          <SortButton showSort={showSort} onClick={handleSort} />
        </TableHeaderCell>
        {columns.map((column, index) => (
          <TableHeaderCell isHidden={index === 0} key={`headCell-${index}`}>
            <ColumnFilter
              showSort={showSort}
              column={column}
              data={data}
              setTableData={setTableData}
              filterColumns={filterColumns}
            />
          </TableHeaderCell>
        ))}
      </TableRow>
    </thead>
  );
};

export default TableHeader;
