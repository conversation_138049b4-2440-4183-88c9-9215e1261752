﻿using WorkTimeApi.Common.DTOs.Absences;

namespace WorkTimeApi.Common.Notifications.Absences
{
    public class AbsenceDeletedByAdminNotification : BaseNotification<AbsenceHospitalDTO>
    {
        public Guid UserId { get; }

        public Guid? EmployeeId { get; }

        public AbsenceDeletedByAdminNotification(AbsenceHospitalDTO payload, Guid companyId, Guid userId, string creatorName, Guid? employeeId)
            : base(payload, companyId, NotificationsName.Absences.DeleteByAdmin.Push, creatorName)
        {
            UserId = userId;

            if (payload != null)
            {
                Url = $"{companyId}/attendance?date={payload.FromDate:MM.yyyy}&absenceId={payload.Id}&fromNotification=true";
            }

            EmployeeId = employeeId;
        }
    }
}