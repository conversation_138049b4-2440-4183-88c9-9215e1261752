# Use the official .NET 7 SDK image as the base image
FROM mcr.microsoft.com/dotnet/sdk:8.0 AS build
ARG ASPNETCORE_ENVIRONMENT_VARIABLE
ENV ASPNETCORE_ENVIRONMENT=$ASPNETCORE_ENVIRONMENT_VARIABLE
ENV ASPNETCORE_URLS=http://+:8080
ENV ASPNETCORE_DEBUGGER=\\vsdbg\\vsdbg --interpreter=python
# Set the working directory in the container where your application will be built and run
WORKDIR /gateway
COPY ["NuGet.Config", ""]

# Copy the source code for each of your .NET 7 services into the container
COPY . gateway/
COPY /Gateway/appsettings.json /gateway/
COPY /Gateway/appsettings.Docker.json /gateway/
COPY /Gateway/appsettings.Testing.json /gateway/
COPY /Gateway/appsettings.Development.json /gateway/

# Use the dotnet CLI to restore, build, and publish each of your .NET 7 services into the container
RUN dotnet restore gateway/
RUN dotnet build gateway/ -c $ASPNETCORE_ENVIRONMENT_VARIABLE
RUN dotnet publish gateway/ -c $ASPNETCORE_ENVIRONMENT_VARIABLE -o out-gateway

# Runtime stage
FROM mcr.microsoft.com/dotnet/aspnet:8.0.0-alpine3.18-amd64
ARG ASPNETCORE_ENVIRONMENT_VARIABLE
WORKDIR /app

# Install the necessary culture-specific resources
RUN apk add --no-cache icu-libs
ENV DOTNET_SYSTEM_GLOBALIZATION_INVARIANT=false

# Copy published output from build stage
COPY --from=build /gateway/out-gateway .

# Set ASP.NET Core environment variables
ENV ASPNETCORE_URLS=http://+:8080
ENV ASPNETCORE_ENVIRONMENT=$ASPNETCORE_ENVIRONMENT_VARIABLE
ENV ASPNETCORE_DEBUGGER=\\vsdbg\\vsdbg --interpreter=python

# Set the command to start all of your .NET 7 services when the container starts
ENTRYPOINT ["dotnet", "Gateway.dll"]