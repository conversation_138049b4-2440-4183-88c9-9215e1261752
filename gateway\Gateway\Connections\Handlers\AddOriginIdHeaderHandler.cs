﻿namespace Gateway.Connections.Handlers
{
    public class AddOriginIdHeaderHandler : DelegatingHandler
    {
        private readonly IConfiguration _configuration;

        public AddOriginIdHeaderHandler(IConfiguration configuration)
        {
            _configuration = configuration;
        }

        protected override async Task<HttpResponseMessage> SendAsync(HttpRequestMessage request, CancellationToken cancellationToken)
        {
            var originId = _configuration["Origin-Id"];

            if (originId is not null && !request.Headers.Contains("Origin-Id"))
                request.Headers.Add("Origin-Id", originId);

                return await base.SendAsync(request, cancellationToken);
        }
    }
}
