﻿using Gateway.Common.Extenstions.EndpointExtensions;
using Gateway.Extenstions.MediatorExtensions;
using WorkTimeApi.Common.Requests.Notifications;

namespace Gateway.EndpointDefinitions.Notifications
{

    public class NotificationsEndpointDefinitions : IEndpointDefinition
    {
        public void DefineEndpoints(WebApplication app)
        {
            app.AuthenticatedGet<GetAllUserNotifications>("all-user-notifications")
               .AuthenticatedPost<ReadNotificationRequest>("notifications/mark-as-read");
        }

        public void DefineServices(IServiceCollection services)
        {
        }
    }

}
