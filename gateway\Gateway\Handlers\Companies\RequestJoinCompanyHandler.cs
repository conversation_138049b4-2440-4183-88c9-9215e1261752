﻿using Gateway.Common.Globals;
using Gateway.Common.Models.DTOs.Requests;
using Gateway.Common.Results;
using Gateway.Connections.Interfaces;
using Gateway.Requests.UserRegistration.Companies;
using MediatR;
using WorkTimeApi.Common.DTOs.Companies;
using WorkTimeApi.Common.Enums;
using WorkTimeApi.Common.Requests.Companies;
using WorkTimeApi.Common.Responses;

namespace Gateway.Handlers.Companies
{
    public class RequestJoinCompanyHandler(GlobalUser globalUser,
        IWorkTimeApiConnection workTimeApiConnection,
        IEmailsConnection emailsConnection,
        IConfiguration configuration) : IRequestHandler<RequestJoinCompany, IResult>
    {
        public async Task<IResult> Handle(RequestJoinCompany request, CancellationToken cancellationToken)
        {
            var companyResponse = await workTimeApiConnection.GetCompanyByIdAsync(request.CompanyId);
            if (!companyResponse.IsSuccessStatusCode)
                return Results.StatusCode((int)companyResponse.StatusCode);

            var company = await companyResponse.Content.ReadFromJsonAsync<CompanyDTO>(cancellationToken: cancellationToken);
            if (company is null)
                return Results.NoContent();

            var addedResponse = await workTimeApiConnection.AddEmployeeCompanyAsync(new AddEmployeeCompanyRequest
            {
                CompanyId = company.Id,
                UserId = globalUser.Id,
                Status = EmployeeCompanyStatus.CompanyToApprove
            });

            if (!addedResponse.IsSuccessStatusCode)
                return new HttpResponseMessageResult(addedResponse);

            var companyEmployeeAddedResponse = await addedResponse.Content.ReadFromJsonAsync<CompanyEmployeeAddedResponse>(cancellationToken: cancellationToken);
            if (companyEmployeeAddedResponse is null)
                return new HttpResponseMessageResult(addedResponse);

            await emailsConnection.EmployeeRequestAsync(new SendEmployeeCompanyRequest
            {
                CompanyName = company.Name,
                FirstName = globalUser.FirstName,
                LastName = globalUser.LastName,
                Emails = companyEmployeeAddedResponse.EmployeesToNotify.Select(e => e.Email).ToList() ?? throw new Exception("No users with permission to approve users found!"),
                Url = configuration["WorkTimeUrl"] ?? throw new Exception("WorkTimeUrl не е добавен в appsettings"),
            });

            return Results.Ok(companyEmployeeAddedResponse.EmployeeDTO);
        }
    }
}