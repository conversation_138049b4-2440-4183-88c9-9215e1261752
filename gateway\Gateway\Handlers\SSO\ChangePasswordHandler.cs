﻿using Gateway.Common.Globals;
using Gateway.Common.Results;
using Gateway.Connections.Interfaces;
using MediatR;
using Microinvest.TransferFiles.Tools.Models.Users;
using SSO.Common.Requests;
using WorkTimeApi.Common.Requests.Employees;

namespace Gateway.Handlers.SSO
{
    public class ChangePasswordHandler : IRequestHandler<ChangePasswordRequest, IResult>
    {
        private readonly IUserRegistrationConnection _userRegistrationConnection;
        private readonly IWorkTimeApiConnection _workTimeApiConnection;
        private readonly GlobalUser _globalUser;

        public ChangePasswordHandler(IUserRegistrationConnection userRegistrationConnection,
            IWorkTimeApiConnection workTimeApiConnection,
            GlobalUser globalUser)
        {
            _userRegistrationConnection = userRegistrationConnection;
            _workTimeApiConnection = workTimeApiConnection;
            _globalUser = globalUser;
        }

        public async Task<IResult> Handle(ChangePasswordRequest request, CancellationToken cancellationToken)
        {
            var userValidation = await _userRegistrationConnection.GetIsUserValidAsync(_globalUser.Email, request.OldPassword);
            if (userValidation.StatusCode == System.Net.HttpStatusCode.Unauthorized)
                return Results.NotFound();

            var urChangePasswordResponse = await _userRegistrationConnection.ChangePasswordAsync(new UserDTO
            {
                Email = _globalUser.Email,
                Password = request.Password,
                RefreshToken = _globalUser.RefreshToken
            });

            if (urChangePasswordResponse is null || !urChangePasswordResponse.IsSuccessStatusCode)
                return Results.BadRequest();

            var workTimeResponse = await _workTimeApiConnection.UpdateUserHasSignedInAsync(
                new UpdateUserHasSignedInRequest
                {
                    UserId = _globalUser.Id,
                    Value = true
                });

            return new HttpResponseMessageResult(workTimeResponse);
        }
    }
}
