﻿using Gateway.Common.Results;
using Gateway.Connections.Interfaces;
using MediatR;
using WorkTimeApi.Common.Requests.TRZ;

namespace Gateway.Handlers.TRZ
{
    public class ImportTRZEventHandler : IRequestHandler<ImportTRZEventRequest, IResult>
    {
        private readonly IWorkTimeApiConnection _workTimeApiConnection;

        public ImportTRZEventHandler(IWorkTimeApiConnection workTimeApiConnection)
        {
            _workTimeApiConnection = workTimeApiConnection;
        }

        public async Task<IResult> Handle(ImportTRZEventRequest request, CancellationToken cancellationToken)
        {
            var importTrzEventResponse = await _workTimeApiConnection.ImportTRZEventAsync(request);

            return new HttpResponseMessageResult(importTrzEventResponse);
        }
    }
}
