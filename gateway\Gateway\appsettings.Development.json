{
  "Logging": {
    "LogLevel": {
      "Default": "Information",
      "Microsoft.AspNetCore": "Warning"
    }
  },
  "ConnectionStrings": {
    "Redis": "*************:6379,password=Micr0!nvest"
  },
  //"SSOBaseUrl": "https://sso/",
  "SSOBaseUrl": "https://localhost:7041/",
  "UserRegistrationsBaseUrl": "http://*************/",
  "EmailsApiBaseUrl": "http://*************/emails/",
  "WorkTimeUrl": "http://localhost:3000/",
  //"WorkTimeApiUrl": "https://workTimeApi/",
  "WorkTimeApiUrl": "https://localhost:7124/",
  "Origin-Id": "59D77375-9D80-4514-A8DC-16C4AE253B53"
}
