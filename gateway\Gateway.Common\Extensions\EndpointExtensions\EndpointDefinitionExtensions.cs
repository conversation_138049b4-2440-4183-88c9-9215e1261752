﻿using Gateway.Common.Extenstions.EndpointExtensions;
using Gateway.EndpointDefinitions.Swagger;
using Microsoft.AspNetCore.Builder;
using Microsoft.AspNetCore.Http.Json;
using Microsoft.Extensions.DependencyInjection;
using System.Text.Json.Serialization;

namespace Gateway.Extenstions.EndpointExtensions
{
	public static class EndpointDefinitionExtensions
	{
		public static void AddEndpointDefinitions(
			this IServiceCollection services, params Type[] scanMarkers)
		{
			var endpointDefinitions = new List<IEndpointDefinition>();

			foreach (var marker in scanMarkers)
			{
				endpointDefinitions.AddRange(
					marker.Assembly.ExportedTypes
						.Where(x => typeof(IEndpointDefinition).IsAssignableFrom(x) && !x.IsInterface)
						.Select(Activator.CreateInstance).Cast<IEndpointDefinition>()
					);
			}

			endpointDefinitions.Add(Activator.CreateInstance<SwaggerEndpointDefinition>());

			foreach (var endpointDefinition in endpointDefinitions)
			{
				endpointDefinition.DefineServices(services);
			}

			services.AddSingleton(endpointDefinitions as IReadOnlyCollection<IEndpointDefinition>);

			services.Configure<JsonOptions>(options =>
            {
                options.SerializerOptions.ReferenceHandler = ReferenceHandler.IgnoreCycles;
            });
        }

		public static void UseEndpointDefinitions(this WebApplication app)
		{
			var definitions = app.Services.GetRequiredService<IReadOnlyCollection<IEndpointDefinition>>();

			foreach (var endpointDefinition in definitions)
			{
				endpointDefinition.DefineEndpoints(app);
			}
		}
	}
}
