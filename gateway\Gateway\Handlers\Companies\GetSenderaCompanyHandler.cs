﻿using Gateway.Common.ResponseObjects;
using Gateway.Common.Results;
using Gateway.Connections.Interfaces;
using Gateway.Requests.UserRegistration.Companies;
using MediatR;

namespace Gateway.Handlers.Companies
{
	public class GetSenderaCompanyHandler(IWorkTimeApiConnection workTimeApiConnection) : IRequestHandler<GetSenderaCompanyRequest, IResult>
	{
        public async Task<IResult> Handle(GetSenderaCompanyRequest request, CancellationToken cancellationToken)
		{
			var company = await workTimeApiConnection.GetCompanyByIdAsync(request.CompanyId);
			if (!company.IsSuccessStatusCode)
				return Results.StatusCode((int)company.StatusCode);

			var worktimeCompany = await company.Content.ReadFromJsonAsync<WorktimeCompanyResponse>();

			if (worktimeCompany is null)
				return Results.NoContent();

			return new HttpResponseMessageResult(company);
		}
	}
}