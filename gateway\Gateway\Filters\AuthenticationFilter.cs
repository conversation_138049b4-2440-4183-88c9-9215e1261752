﻿using Gateway.Common.Globals;
using Gateway.Connections.Interfaces;
using Newtonsoft.Json;
using Serilog.Context;
using WorkTimeApi.Common.DTOs.Users;
using WorkTimeApi.Common.Requests.Users;
using URUserDTO = Microinvest.TransferFiles.Tools.Models.Users.UserDTO;

namespace Gateway.Filters
{
    public class AuthenticationFilter(IHttpContextAccessor httpContextAccessor, ISSOConnection ssoConnection,
        IWorkTimeApiConnection workTimeApiConnection, GlobalUser globalUser,
        IUserRegistrationConnection userRegistrationConnection) : IEndpointFilter
    {
        private readonly GlobalUser _globalUser = globalUser;

        public async ValueTask<object?> InvokeAsync(EndpointFilterInvocationContext context, EndpointFilterDelegate next)
        {
            if (httpContextAccessor.HttpContext is null)
                return TypedResults.Unauthorized();

            var authorizationHeader = httpContextAccessor.HttpContext.Request.Headers.Authorization.ToString();
            var refreshToken = httpContextAccessor.HttpContext.Request.Headers["refresh-token"].ToString();

            if (string.IsNullOrEmpty(authorizationHeader))
                return TypedResults.Unauthorized();

            var response = await ssoConnection.AuthenticateAsync(authorizationHeader, refreshToken);

            if (!response.IsSuccessStatusCode)
                return TypedResults.Unauthorized();

            if (!response.Headers.TryGetValues("Authorization", out var authorizations))
                return TypedResults.Unauthorized();

            if (!response.Headers.TryGetValues("refresh-token", out var refreshTokens))
                return TypedResults.Unauthorized();

            httpContextAccessor.HttpContext.Response.Headers.Authorization = authorizations.FirstOrDefault();
            httpContextAccessor.HttpContext.Response.Headers["refresh-token"] = refreshTokens.FirstOrDefault();

            var content = await response.Content.ReadAsStringAsync();
            var globalUser = JsonConvert.DeserializeObject<GlobalUser>(content);

            if (globalUser is null)
                return TypedResults.Unauthorized();

            var userResponse = await workTimeApiConnection.GetUserAsync(new GetUserRequest { Id = globalUser.Id });
            _globalUser.Id = globalUser.Id;
            _globalUser.RefreshToken = refreshTokens.FirstOrDefault() ?? "";
            _globalUser.Email = globalUser.Email;

            if (userResponse.IsSuccessStatusCode)
            {
                var userDTO = await userResponse.Content.ReadFromJsonAsync<UserDTO>();

                _globalUser.FirstName = userDTO?.FirstName ?? "";
                _globalUser.SecondName = userDTO?.SecondName ?? "";
                _globalUser.LastName = userDTO?.LastName ?? "";

                httpContextAccessor.HttpContext.Request.Headers.Append("X-User-ID", globalUser.Id.ToString());
            }
            else
            {
                var senderaUserResponse = await userRegistrationConnection.GetSenderaUserByIdAsync(_globalUser.Id.ToString());
                if (senderaUserResponse.IsSuccessStatusCode)
                {
                    var senderaUserDTO = await senderaUserResponse.Content.ReadFromJsonAsync<URUserDTO>();

                    _globalUser.FirstName = senderaUserDTO?.FirstName ?? "";
                    _globalUser.SecondName = senderaUserDTO?.SecondName ?? "";
                    _globalUser.LastName = senderaUserDTO?.LastName ?? "";
                }
            }

            using (LogContext.PushProperty("UserId", globalUser.Id))
            {
                return await next.Invoke(context);
            }
        }
    }
}
