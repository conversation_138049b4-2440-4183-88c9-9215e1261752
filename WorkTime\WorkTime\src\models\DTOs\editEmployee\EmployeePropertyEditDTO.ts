export interface EmployeePropertyEditDTO {
  id: string;
  editSource: EditSource;
  editStatus: EditStatus;
  editorId: string;
  employeeId: string;
  objectName: string;
  objectId: string;
  propertyName: string;
  oldValue: string | null;
  newValue: string | null;
  createdAt: string;
  updatedAt: string;
}

export interface EmployeePropertyEditGroupDTO {
  employeeId: string;
  editSource: EditSource;
  editorId: string;
  createdAt: string;
  propertyEdits: EmployeePropertyEditDTO[];
}

export enum EditSource {
  UserEdit = 0,
  AdminEdit = 1,
}

export enum EditStatus {
  Pending = 0,
  Approved = 1,
  Declined = 2,
}
