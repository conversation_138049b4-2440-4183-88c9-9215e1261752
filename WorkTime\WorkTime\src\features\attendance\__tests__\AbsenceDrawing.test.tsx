import { render, cleanup, waitFor } from "@testing-library/react";
import DatesTableContainer from "../DatesTableContainer";
import { AbsenceStatus } from "../../../models/DTOs/absence/AbsenceStatus";
import { AlignmentPosition } from "../../../components/CalendarComponent/types/AlignmentPosition.ts";

// Capture props passed to DatesTableView
let capturedProps: any = null;
jest.mock(
  "../../../components/CalendarComponent/DatesTableView",
  () => (props: any) => {
    capturedProps = props;
    return <div data-testid="dates-table-view" />;
  }
);

// Mock modal and menu contexts used inside container
jest.mock("../../../components/PopUp/ActionModalContext", () => ({
  useModal: () => ({ openModal: jest.fn(), isModalOpen: false }),
}));

jest.mock("../../MenuContext", () => ({
  useMenu: () => ({
    toggleMenu: jest.fn(),
    changeView: jest.fn(),
    isOpen: true,
  }),
}));

jest.mock("../../absences/AbsenceContext", () => ({
  useAbsence: () => ({ setSelectedAbsence: jest.fn() }),
}));

jest.mock("../../companies/CompanyContext.tsx", () => ({
  useCompany: () => ({ company: { id: "company-1" } }),
}));

// App hooks
jest.mock("../../../app/hooks", () => ({
  useAppDispatch: () => jest.fn(),
  useAppSelector: (selector: any) => selector(),
}));

// Provide payrolls via selector (stable reference)
const makePayrolls = () => {
  const employee = {
    userId: "u1",
    workTimeId: "emp-1",
    firstName: "Alice",
    lastName: "Smith",
    email: "",
    egn: "",
  };

  return [
    {
      id: "worktime-1",
      workTimeId: "worktime-1",
      employee,
      structureLevelId: "lvl-1",
      leaves: [
        // In-month leave: Aug 10-12 (Approved)
        {
          id: "leave-inmonth",
          payrollId: "worktime-1",
          fromDate: new Date(2025, 7, 10).toISOString(),
          toDate: new Date(2025, 7, 12).toISOString(),
          isHospital: false,
          status: AbsenceStatus.Approved,
          isOverlapping: false,
          typeIdentifier: "vac",
          exportStatus: 0,
          reference: "",
          sickNote: "",
        },
        // In-month hospital: Aug 15-16 (Approved)
        {
          id: "hosp-inmonth",
          payrollId: "worktime-1",
          fromDate: new Date(2025, 7, 15).toISOString(),
          toDate: new Date(2025, 7, 16).toISOString(),
          isHospital: true,
          status: AbsenceStatus.Approved,
          isOverlapping: false,
          typeIdentifier: "hosp",
          exportStatus: 0,
          reference: "",
          sickNote: "SN",
        },
        // Cross-month leave: Aug 30 - Sep 2 (Pending)
        {
          id: "leave-cross",
          payrollId: "worktime-1",
          fromDate: new Date(2025, 7, 30).toISOString(),
          toDate: new Date(2025, 8, 2).toISOString(),
          isHospital: false,
          status: AbsenceStatus.Pending,
          isOverlapping: false,
          typeIdentifier: "vac",
          exportStatus: 0,
          reference: "",
          sickNote: "",
        },
        // Cross-month hospital: Aug 31 - Sep 3 (Pending)
        {
          id: "hosp-cross",
          payrollId: "worktime-1",
          fromDate: new Date(2025, 7, 31).toISOString(),
          toDate: new Date(2025, 8, 3).toISOString(),
          isHospital: true,
          status: AbsenceStatus.Pending,
          isOverlapping: false,
          typeIdentifier: "hosp",
          exportStatus: 0,
          reference: "",
          sickNote: "SN2",
        },
      ],
    },
  ];
};

const STABLE_PAYROLLS = makePayrolls();
const STABLE_SELECT_RETURN = { payrolls: STABLE_PAYROLLS as any };

jest.mock("../../payroll/payrollsActions", () => ({
  onPayrollsLoaded: jest.fn(),
  selectPayrolls: () => STABLE_SELECT_RETURN,
}));

afterEach(() => {
  cleanup();
  capturedProps = null;
});

const baseProps = {
  selectedPayroll: undefined,
  setSelectedPayroll: () => {},
  selectedEmployee: undefined,
  hoveredEmployee: undefined,
  showMyAbsences: false,
  setSelectedMonth: () => {},
  setSelectedYear: () => {},
  holidays: [] as any[],
  highlightedAbsenceId: undefined,
  isFromNotification: false,
};

const findDay = (days: any[], dayNumber: number) =>
  days.find((d) => d.type === "currentMonth" && d.dayNumber === dayNumber);

describe("DatesTableContainer absence drawing for month", () => {
  test("draws leaves and hospitals within month with correct alignment and status", async () => {
    render(
      <DatesTableContainer
        {...baseProps}
        selectedMonth={7}
        selectedYear={2025}
      />
    );

    await waitFor(() => expect(capturedProps?.days?.length).toBeGreaterThan(0));

    const days = capturedProps.days as any[];

    // In-month leave Aug 10-12
    const d10 = findDay(days, 10);
    const d11 = findDay(days, 11);
    const d12 = findDay(days, 12);

    expect(
      d10.missingEmployees.some(
        (m: any) =>
          m.id === "leave-inmonth" &&
          m.positonRounding === AlignmentPosition.Left &&
          m.status === AbsenceStatus.Approved &&
          m.isHospital === false
      )
    ).toBe(true);
    expect(
      d11.missingEmployees.some(
        (m: any) =>
          m.id === "leave-inmonth" &&
          m.positonRounding === AlignmentPosition.Center
      )
    ).toBe(true);
    expect(
      d12.missingEmployees.some(
        (m: any) =>
          m.id === "leave-inmonth" &&
          m.positonRounding === AlignmentPosition.Right
      )
    ).toBe(true);

    // In-month hospital Aug 15-16
    const d15 = findDay(days, 15);
    const d16 = findDay(days, 16);
    expect(
      d15.missingEmployees.some(
        (m: any) =>
          m.id === "hosp-inmonth" &&
          m.isHospital === true &&
          m.positonRounding === AlignmentPosition.Left &&
          m.status === AbsenceStatus.Approved
      )
    ).toBe(true);
    expect(
      d16.missingEmployees.some(
        (m: any) =>
          m.id === "hosp-inmonth" &&
          m.positonRounding === AlignmentPosition.Right
      )
    ).toBe(true);
  });

  test("draws cross-month leaves correctly at month boundaries (August view)", async () => {
    render(
      <DatesTableContainer
        {...baseProps}
        selectedMonth={7}
        selectedYear={2025}
      />
    );

    await waitFor(() => expect(capturedProps?.days?.length).toBeGreaterThan(0));
    const days = capturedProps.days as any[];

    const d30 = findDay(days, 30);
    const d31 = findDay(days, 31);

    // Cross-month leave: Aug 30 Left, Aug 31 Center
    expect(
      d30.missingEmployees.some(
        (m: any) =>
          m.id === "leave-cross" &&
          m.positonRounding === AlignmentPosition.Left &&
          m.status === AbsenceStatus.Pending
      )
    ).toBe(true);
    expect(
      d31.missingEmployees.some(
        (m: any) =>
          m.id === "leave-cross" &&
          m.positonRounding === AlignmentPosition.Center
      )
    ).toBe(true);

    // Cross-month hospital: Aug 31 Left
    expect(
      d31.missingEmployees.some(
        (m: any) =>
          m.id === "hosp-cross" &&
          m.isHospital === true &&
          m.positonRounding === AlignmentPosition.Left &&
          m.status === AbsenceStatus.Pending
      )
    ).toBe(true);
  });

  test("draws cross-month leaves correctly in next month (September view)", async () => {
    const { rerender } = render(
      <DatesTableContainer
        {...baseProps}
        selectedMonth={7}
        selectedYear={2025}
      />
    );

    await waitFor(() => expect(capturedProps?.days?.length).toBeGreaterThan(0));

    // Switch to September
    rerender(
      <DatesTableContainer
        {...baseProps}
        selectedMonth={8}
        selectedYear={2025}
      />
    );

    await waitFor(() => expect(capturedProps?.days?.length).toBeGreaterThan(0));
    const daysSep = capturedProps.days as any[];

    const s1 = findDay(daysSep, 1);
    const s2 = findDay(daysSep, 2);
    const s3 = findDay(daysSep, 3);

    // Continuation of leave-cross: Sep 1 Center, Sep 2 Right
    expect(
      s1.missingEmployees.some(
        (m: any) =>
          m.id === "leave-cross" &&
          m.positonRounding === AlignmentPosition.Center
      )
    ).toBe(true);
    expect(
      s2.missingEmployees.some(
        (m: any) =>
          m.id === "leave-cross" &&
          m.positonRounding === AlignmentPosition.Right
      )
    ).toBe(true);

    // Continuation of hosp-cross: Sep 1 Center, Sep 3 Right
    expect(
      s1.missingEmployees.some(
        (m: any) =>
          m.id === "hosp-cross" &&
          m.isHospital === true &&
          m.positonRounding === AlignmentPosition.Center
      )
    ).toBe(true);
    expect(
      s3.missingEmployees.some(
        (m: any) =>
          m.id === "hosp-cross" && m.positonRounding === AlignmentPosition.Right
      )
    ).toBe(true);
  });
});
