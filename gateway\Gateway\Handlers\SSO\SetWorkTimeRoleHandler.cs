﻿using Gateway.Common.Globals;
using Gateway.Connections.Interfaces;
using MediatR;
using WorkTimeApi.Common.Requests.Roles;

namespace Gateway.Handlers.SSO
{
    public class SetWorkTimeRoleHandler(IWorkTimeApiConnection workTimeApiConnection, GlobalUser globalUser) : IRequestHandler<SetWorkTimeRoleRequest, IResult>
    {
        public async Task<IResult> Handle(SetWorkTimeRoleRequest request, CancellationToken cancellationToken)
        {
            request.UserId = globalUser.Id;

            var response = await workTimeApiConnection.SetWorkTimeRoleAsync(request);

            return Results.Ok((int)response.StatusCode);
        }
    }
}
