﻿using AutoMapper;
using SSO.Common.DTOs;
using SSO.Database.Models;

namespace SSO.Mapper
{
    public class MapperProfile : Profile
    {
        public MapperProfile()
        {
            CreateMap<RefreshToken, RefreshTokenDTO>().ReverseMap();

            CreateMap<Permission, PermissionDTO>().ReverseMap();

            CreateMap<UserDTO, User>().ReverseMap();

            CreateMap<Role, RoleDTO>().ReverseMap();
        }
    }
}
