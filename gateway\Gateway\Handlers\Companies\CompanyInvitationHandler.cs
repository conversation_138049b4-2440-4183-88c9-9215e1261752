using Gateway.Common.Globals;
using Gateway.Connections.Interfaces;
using MediatR;
using WorkTimeApi.Common.DTOs.Companies;

namespace Gateway.Handlers.Companies;

public class CompanyInvitationHandler(IWorkTimeApiConnection workTimeApiConnection, GlobalUser globalUser) : IRequestHandler<CompanyInvitationRequest, IResult>
{
    public async Task<IResult> Handle(CompanyInvitationRequest request, CancellationToken cancellationToken)
    {
        request.UserId = globalUser.Id;

        var response = await workTimeApiConnection.UpdateCompanyInvitationAsync(request);

        return response.IsSuccessStatusCode ? Results.Ok() : Results.Problem(await response.Content.ReadAsStringAsync(cancellationToken));
    }
}
