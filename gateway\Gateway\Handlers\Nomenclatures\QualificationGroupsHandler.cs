﻿using Gateway.Common.Results;
using Gateway.Connections.Interfaces;
using MediatR;
using WorkTimeApi.Common.Requests.Nomenclatures;

namespace Gateway.Handlers.Nomenclatures
{
	public class QualificationHandler : IRequestHandler<GetQualificationGroupsRequest, IResult>
	{
		private readonly IWorkTimeApiConnection _workTimeApiConnection;

		public QualificationHandler(IWorkTimeApiConnection workTimeApiConnection)
		{
			_workTimeApiConnection = workTimeApiConnection;
		}

		public async Task<IResult> Handle(GetQualificationGroupsRequest request, CancellationToken cancellationToken)
		{
			var qualificationGroups = await _workTimeApiConnection.GetQualificationGroupsAsync();

			return new HttpResponseMessageResult(qualificationGroups);
		}
	}
}
