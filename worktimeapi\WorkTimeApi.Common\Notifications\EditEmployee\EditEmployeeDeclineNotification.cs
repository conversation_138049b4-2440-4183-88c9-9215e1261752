﻿using WorkTimeApi.Common.DTOs.Employees;

namespace WorkTimeApi.Common.Notifications.EditEmployee
{
    public class EditEmployeeDeclineNotification : BaseNotification<EmployeeDTO>
    {
        public Guid UserId { get; }

        public string? EditedTab { get; }

        public EditEmployeeDeclineNotification(EmployeeDTO payload, Guid? payrollId, Guid companyId, Guid userId, string creatorName, string? editedTab)
            : base(payload, companyId, NotificationsName.Employees.EditDeclined.Push, creatorName)
        {
            UserId = userId;
            EditedTab = editedTab;

            if (payload != null)
                Url = $"{companyId}/employees/0/{payload.WorkTimeId}/{payrollId}";
        }
    }
}
