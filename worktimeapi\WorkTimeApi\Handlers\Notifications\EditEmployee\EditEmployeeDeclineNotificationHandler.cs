﻿using MediatR;
using WorkTime.Notifications.Services.Interfaces;
using WorkTimeApi.Common.Notifications.EditEmployee;
using WorkTimeApi.Database.Repositories.Interfaces.Companies;
using WorkTimeApi.Services.Interfaces.Notifications;

namespace WorkTimeApi.Handlers.Notifications.EditEmployee
{
    public class EditEmployeeDeclineNotificationHandler(ISignalRNotificationService signalRNotificationService,
        INotificationsService notificationsService,
        IEmailsNotificationService emailsNotificationService, ICompaniesRepository companiesRepository,
        IConfiguration configuration) : INotificationHandler<EditEmployeeDeclineNotification>
    {
        public async Task Handle(EditEmployeeDeclineNotification notification, CancellationToken cancellationToken)
        {
            var employee = notification.Payload ?? throw new Exception("Няма данни за заявени отсъствия");
            var company = await companiesRepository.GetByIdAsync(notification.CompanyId) ?? throw new Exception("Невалидна компания!");

            var fullName = string.Join(" ", new[] { employee.FirstName, employee.LastName }
               .Where(name => !string.IsNullOrWhiteSpace(name)));

            var worktimeUrl = configuration.GetValue<string>("WorkTimeUrl")
                ?? throw new Exception("Невалидна настройка на WorkTimeUrl в appsettings!");

            var emailRequest = new
            {
                Name = fullName,
                ApproverName = notification.CreatorName,
                CompanyName = company.Name,
                EditedTabName = notification.EditedTab == "Addresses" ? "Адреси" : "Лични данни",
                Url = $"{worktimeUrl}{notification.Url}"
            };

            var savedNotification = await notificationsService.AddNotificationByUserIdAndCompanyIdAsync(employee.WorkTimeId, notification);
            await signalRNotificationService.NotifyUser(employee.UserId, savedNotification);

            await emailsNotificationService.SendEmailsAsync(new List<string> { employee.Email }, "employees/edited/declined", emailRequest);
        }
    }
}