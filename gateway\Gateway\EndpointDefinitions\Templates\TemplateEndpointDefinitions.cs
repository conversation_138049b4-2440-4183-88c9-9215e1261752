﻿using Gateway.Common.Extenstions.EndpointExtensions;
using Gateway.Extenstions.MediatorExtensions;
using WorkTimeApi.Common.Requests.Templates;

namespace Gateway.EndpointDefinitions.Templates
{
	public class TemplateEndpointDefinitions : IEndpointDefinition
	{
		public void DefineEndpoints(WebApplication app)
		{
			app.AuthenticatedPost<AddTemplateRequest>("/templates/add-template")
				 .AuthenticatedGet<LoadTemplatesRequest>("/templates/load-template");
		}

		public void DefineServices(IServiceCollection services)
		{
		}
	}
}
