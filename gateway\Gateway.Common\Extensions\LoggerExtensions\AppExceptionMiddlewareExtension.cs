﻿using Microsoft.AspNetCore.Builder;
using Microsoft.AspNetCore.Http;
using Microsoft.Extensions.Logging;

namespace Gateway.Common.Extensions.LoggerExtensions
{
    public static class AppExceptionMiddlewareExtension
    {
        public static IApplicationBuilder UseMiddlewareExceptionLogging(this WebApplication app) 
            => app.Use(async (context, next) =>
               {
                   try
                   {
                       await next(context);
                   }
                   catch (Exception ex)
                   {
                       app.Logger.LogError(ex, "An unhandled exception occurred while processing the request.");

                       context.Response.StatusCode = 500;
                       await context.Response.WriteAsync("An unexpected error occurred. Please try again later.");
                   }
               });
    }
}
