import { useState } from "react";
import { Body } from "./Body";
import { DropdownContext } from "./Context";
import { Header } from "./Header";

interface DropdownProps
  extends React.DetailedHTMLProps<
    React.AllHTMLAttributes<HTMLDivElement>,
    HTMLDivElement
  > {
  children: React.ReactNode | React.ReactNode[];
}

export const Dropdown = (props: DropdownProps) => {
  const { children, style } = props;
  const [isOpen, setIsOpen] = useState(false);

  return (
    <DropdownContext.Provider value={{ isOpen, setIsOpen }}>
      <div
        {...props}
        style={{
          ...style,
          position: "relative",
        }}
      >
        {children}
      </div>
    </DropdownContext.Provider>
  );
};

var _default = Object.assign(Dropdown, {
  Header,
  Body,
});

export default _default;
