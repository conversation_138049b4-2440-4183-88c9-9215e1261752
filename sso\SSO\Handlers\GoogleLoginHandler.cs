﻿using MediatR;
using SSO.Common.DTOs;
using SSO.Common.Requests;
using SSO.Connections.Interfaces;
using SSO.Services.Interfaces;

namespace SSO.Handlers
{
    public class GoogleLoginHandler : IRequestHandler<GoogleLoginRequest, IResult>
    {
        private readonly IUserRegistrationsConnection _userRegistrationsConnection;
        private readonly IJwtGeneratorService _jwtGeneratorService;
        private readonly IRefreshTokensService _refreshTokensService;
        private readonly IGoogleService _googleService;

        public GoogleLoginHandler(IUserRegistrationsConnection userRegistrationsConnection,
            IJwtGeneratorService jwtGeneratorService,
            IRefreshTokensService refreshTokensService,
            IGoogleService googleService)
        {
            _userRegistrationsConnection = userRegistrationsConnection;
            _jwtGeneratorService = jwtGeneratorService;
            _refreshTokensService = refreshTokensService;
            _googleService = googleService;
        }

        public async Task<IResult> Handle(GoogleLoginRequest request, CancellationToken cancellationToken)
        {
            if (request.HttpRequest is null)
                return Results.BadRequest();

            var googleToken = request.HttpRequest.Headers.Authorization.ToString().Split("Bearer ").LastOrDefault();
            if (googleToken is null)
                return Results.BadRequest();

            var googleDTO = await _googleService.GetGoogleDataAsync(googleToken.ToString());
            if (googleDTO is null)
                return Results.BadRequest();

            var userDTO = new UserDTO
            {
                Email = googleDTO.Email,
                FirstName = googleDTO.GivenName,
                LastName = googleDTO.FamilyName
            };

            var response = await _userRegistrationsConnection.TryGetUserOrCreateItAsync(userDTO);
            if (response is null)
                return Results.BadRequest();

            var userId = await response.Content.ReadAsStringAsync(cancellationToken);
            if (string.IsNullOrEmpty(userId))
                return Results.BadRequest();

            userDTO.Id = new Guid(userId);
            request.HttpRequest.HttpContext.Response.Headers.Add("Authorization", $"Bearer {await _jwtGeneratorService.GenerateAccessTokenAsync(userDTO)}");

            var newRefreshToken = await _jwtGeneratorService.GenerateRefreshTokenAsync(userDTO);
            await _refreshTokensService.AddRefreshTokenAsync(newRefreshToken, userDTO.Id, userDTO.Email ?? throw new ArgumentException("Invalid email address"));
            request.HttpRequest.HttpContext.Response.Headers.Add("Refresh-Token", newRefreshToken);

            return Results.Ok(userDTO);
        }
    }
}
