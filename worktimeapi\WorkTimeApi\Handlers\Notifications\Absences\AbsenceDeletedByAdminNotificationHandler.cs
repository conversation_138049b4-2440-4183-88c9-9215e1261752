﻿using MediatR;
using WorkTime.Notifications.Services.Interfaces;
using WorkTimeApi.Common.Enums;
using WorkTimeApi.Common.Notifications.Absences;
using WorkTimeApi.Database.Repositories.Interfaces.Companies;
using WorkTimeApi.Database.Repositories.Interfaces.Employees;
using WorkTimeApi.Services.Interfaces.Notifications;

namespace WorkTimeApi.Handlers.Notifications.Absences
{
    public class AbsenceDeletedByAdminNotificationHandler(ISignalRNotificationService signalRNotificationService,
        INotificationsService notificationsService,
        IEmailsNotificationService emailsNotificationService,
        IEmployeesRepository employeesRepository,
        ICompaniesRepository companiesRepository,
        IConfiguration configuration) : INotificationHandler<AbsenceDeletedByAdminNotification>
    {
        public async Task Handle(AbsenceDeletedByAdminNotification notification, CancellationToken cancellationToken)
        {
            var approver = (await employeesRepository.FindAsync(e => e.UserId == notification.UserId && e.CompanyId == notification.CompanyId)).FirstOrDefault() ?? throw new Exception("Невалидно Employee!");
            var approverName = string.Join(" ", new[] { approver.FirstName, approver.LastName }
                           .Where(name => !string.IsNullOrWhiteSpace(name)));
            var employee = await employeesRepository.GetEmployeeAsync(notification.EmployeeId.Value) ?? throw new Exception("Invalid Employee!");

            var savedNotification = await notificationsService.AddNotificationByUserIdAndCompanyIdAsync(employee.Id, notification);
            await signalRNotificationService.NotifyUser(employee.UserId, savedNotification);

            var absence = notification.Payload ?? throw new Exception("Няма данни за заявени отсъствия");
            var company = await companiesRepository.GetByIdAsync(notification.CompanyId) ?? throw new Exception("Невалидна компания!");
            var fullName = string.Join(" ", new[] { employee.FirstName, employee.LastName }
                           .Where(name => !string.IsNullOrWhiteSpace(name)));

            var worktimeUrl = configuration.GetValue<string>("WorkTimeUrl") ?? throw new Exception("Невалидна настройка на WorkTimeUrl в appsettings!");

            var emailRequest = new
            {
                Name = fullName,
                ApproverName = approverName,
                CompanyName = company.Name,
                AbsenceType = absence.TypeIdentifier.GetDescription(),
                StartDate = absence.FromDate.ToString("dd.MM.yyyy"),
                EndDate = absence.ToDate.ToString("dd.MM.yyyy"),
                Url = $"{worktimeUrl}{notification.Url}"
            };

            await emailsNotificationService.SendEmailsAsync(new List<string> { employee.Email }, "absences/deleted/admin", emailRequest);
        }
    }
}
