﻿using AutoMapper;
using WorkTimeApi.Common.DTOs.Payrolls;
using WorkTimeApi.Database.Models.TRZ;

namespace WorkTimeApi.Mappers.ValueResolver
{
    public class PayrollKpdsResolver : IValueResolver<PayrollDTO, TRZPayroll, ICollection<TRZPayrollKPD>>
    {
        public ICollection<TRZPayrollKPD> Resolve(PayrollDTO src, TRZPayroll dest, ICollection<TRZPayrollKPD> destMember, ResolutionContext context)
        {
            var kpds = new List<TRZPayrollKPD>();

            if (src.Kpds != null)
            {
                kpds.AddRange(src.Kpds
                    .Where(kpd => kpd is not null && kpd.Id != Guid.Empty)
                    .Select(kpd => new TRZPayrollKPD
                    {
                        Payroll = dest,
                        KPDId = kpd.Id
                    }));
            }

            return kpds;
        }
    }
}