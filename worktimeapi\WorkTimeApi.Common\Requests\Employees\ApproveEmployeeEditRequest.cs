﻿using Gateway.Common.Requests;

namespace WorkTimeApi.Common.Requests.Employees
{
    public class ApproveEmployeePropertyEditRequest : BaseRequest
    {
        public Guid? PropertyEditId { get; set; }

        public bool IsApprovingAllForEmployee { get; set; }

        public Guid? EmployeeId { get; set; }

        public Guid? PayrollId { get; set; }

        public string TabName { get; set; }
    }
}
