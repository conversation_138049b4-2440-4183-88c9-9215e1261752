﻿using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace SSO.Database.Models
{
    public class RefreshToken
    {
        [Key]
        [DatabaseGenerated(DatabaseGeneratedOption.Identity)]
        public Guid Id { get; set; }

        public required string Token { get; set; }

        public bool IsActive { get; set; }

        public required User User { get; set; }

        public DateTime? DeactivatedDate { get; set; }
    }
}
