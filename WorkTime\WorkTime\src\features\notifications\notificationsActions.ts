import { Action, Reducer } from "redux";
import { AppThunk, ClearStateAction, RootState } from "../../app/store";
import { NotificationDTO } from "../../models/DTOs/notifications/NotificationDTO";
import {
  authenticatedGet,
  authenticatedPost,
} from "../../services/connectionService";

interface NotificationsState {
  notifications: NotificationDTO[];
}

interface LoadNotificationsAction {
  type: "LOAD_ALL_USER_NOTIFICATIONS";
  notifications: NotificationDTO[];
}

interface AddNotificationAction {
  type: "ADD_NOTIFICATIONS";
  notifications: NotificationDTO[];
}

interface MarkNotificationAsReadAction {
  type: "MARK_NOTIFICATION_AS_READ";
  notificationId: string;
}

type KnownActions =
  | LoadNotificationsAction
  | AddNotificationAction
  | MarkNotificationAsReadAction
  | ClearStateAction;

const loadAllUserNotificationsAction = (
  notifications: NotificationDTO[]
): LoadNotificationsAction => ({
  type: "LOAD_ALL_USER_NOTIFICATIONS",
  notifications,
});

const addNotificationsAction = (
  notifications: NotificationDTO[]
): AddNotificationAction => ({
  type: "ADD_NOTIFICATIONS",
  notifications,
});

const markNotificationAsReadAction = (
  notificationId: string
): MarkNotificationAsReadAction => ({
  type: "MARK_NOTIFICATION_AS_READ",
  notificationId,
});

export const actionCreators = {
  onAllUserNotificationsLoaded: (
    userId: string
  ): AppThunk<void, KnownActions> => {
    return async (dispatch: any) => {
      try {
        const notifications = await authenticatedGet<NotificationDTO[]>(
          `all-user-notifications?userId=${userId}`
        );
        dispatch(loadAllUserNotificationsAction(notifications));
      } catch (error) {
        console.error("Failed to load all user notifications:", error);
      }
    };
  },

  onNotificationsAdded: (
    notifications: NotificationDTO[]
  ): AppThunk<void, KnownActions> => {
    return async (dispatch: any) => {
      dispatch(addNotificationsAction(notifications));
    };
  },

  onMarkNotificationAsRead: (
    notificationId: string
  ): AppThunk<void, KnownActions> => {
    return async (dispatch: any) => {
      try {
        await authenticatedPost(`notifications/mark-as-read`, {
          notificationId,
        });
        dispatch(markNotificationAsReadAction(notificationId));
      } catch (error) {
        console.error("Failed to mark notification as read:", error);
      }
    };
  },
};

export const {
  onNotificationsAdded,
  onMarkNotificationAsRead,
  onAllUserNotificationsLoaded,
} = actionCreators;

const initialState: NotificationsState = {
  notifications: [],
};

export const reducer: Reducer<NotificationsState> = (
  state = initialState,
  action: Action
) => {
  const incomingAction = action as KnownActions;

  switch (incomingAction.type) {
    case "LOAD_ALL_USER_NOTIFICATIONS":
      return {
        ...state,
        notifications: [...incomingAction.notifications],
      };

    case "ADD_NOTIFICATIONS":
      return {
        ...state,
        notifications: [
          ...incomingAction.notifications,
          ...state.notifications,
        ],
      };

    case "MARK_NOTIFICATION_AS_READ":
      return {
        ...state,
        notifications: state.notifications.map((notification) =>
          notification.id === incomingAction.notificationId
            ? { ...notification, isRead: true }
            : notification
        ),
      };

    case "CLEAR_STATE":
      return initialState;

    default:
      return state;
  }
};

export const selectNotifications = (state: RootState) => state.notifications;
