using Microsoft.AspNetCore.Http.HttpResults;
using Moq;
using WorkTimeApi.Common.DTOs.Absences;
using WorkTimeApi.Common.Requests.Absences;
using WorkTimeApi.Handlers.Absences;
using WorkTimeApi.Services.Interfaces.Absences;

namespace WorkTimeApi.Tests.Absences;

public class GetCompanyAbsencesByExportStatusesHandlerTests
{
    private readonly Mock<IAbsencesService> _absencesServiceMock;
    private readonly GetCompanyAbsencesByExportStatusesHandler _handler;

    public GetCompanyAbsencesByExportStatusesHandlerTests()
    {
        _absencesServiceMock = new Mock<IAbsencesService>();
        _handler = new GetCompanyAbsencesByExportStatusesHandler(_absencesServiceMock.Object);
    }

    [Fact]
    public async Task Handle_WithValidMonth_ShouldCallMonthFilteredMethod()
    {
        // Arrange
        var companyId = Guid.NewGuid();
        var request = new GetCompanyAbsencesByExportStatusesRequest
        {
            CompanyId = companyId,
            Month = "08.2025",
            Exported = false
        };

        var expectedAbsences = new List<EventDTO>
        {
            new() { Id = Guid.NewGuid(), StartDate = new DateTime(2025, 8, 15), EndDate = new DateTime(2025, 8, 16) }
        };

        _absencesServiceMock.Setup(x => x.GetCompanyAbsencesForTRZByMonthAsync(companyId, It.IsAny<DateTime>(), false))
            .ReturnsAsync(expectedAbsences);

        // Act
        var result = await _handler.Handle(request, CancellationToken.None);

        // Assert
        var okResult = Assert.IsType<Ok<IEnumerable<EventDTO>>>(result);
        Assert.Equal(expectedAbsences, okResult.Value);
        _absencesServiceMock.Verify(x => x.GetCompanyAbsencesForTRZByMonthAsync(companyId, It.Is<DateTime>(d => d.Month == 8 && d.Year == 2025), false), Times.Once);
    }

    [Fact]
    public async Task Handle_WithInvalidMonthFormat_ShouldReturnBadRequest()
    {
        // Arrange
        var companyId = Guid.NewGuid();
        var request = new GetCompanyAbsencesByExportStatusesRequest
        {
            CompanyId = companyId,
            Month = "invalid-format",
            Exported = false
        };

        // Act
        var result = await _handler.Handle(request, CancellationToken.None);

        // Assert
        var badRequestResult = Assert.IsType<BadRequest<string>>(result);
        Assert.Contains("Invalid month format", badRequestResult.Value);
        _absencesServiceMock.Verify(x => x.GetCompanyAbsencesForTRZByMonthAsync(It.IsAny<Guid>(), It.IsAny<DateTime>(), It.IsAny<bool>()), Times.Never);
    }

    [Theory]
    [InlineData("13.2025")] // Invalid month
    [InlineData("00.2025")] // Invalid month
    [InlineData("08.2024")] // Valid format but different year
    [InlineData("12.2025")] // Valid format
    public async Task Handle_WithVariousMonthFormats_ShouldHandleCorrectly(string month)
    {
        // Arrange
        var companyId = Guid.NewGuid();
        var request = new GetCompanyAbsencesByExportStatusesRequest
        {
            CompanyId = companyId,
            Month = month,
            Exported = true
        };

        var expectedAbsences = new List<EventDTO>
        {
            new() { Id = Guid.NewGuid(), StartDate = DateTime.Now, EndDate = DateTime.Now.AddDays(1) }
        };

        _absencesServiceMock.Setup(x => x.GetCompanyAbsencesForTRZByMonthAsync(companyId, It.IsAny<DateTime>(), true))
            .ReturnsAsync(expectedAbsences);

        // Act
        var result = await _handler.Handle(request, CancellationToken.None);

        // Assert
        if (month == "13.2025" || month == "00.2025")
        {
            var badRequestResult = Assert.IsType<BadRequest<string>>(result);
            Assert.Contains("Invalid month format", badRequestResult.Value);
        }
        else
        {
            var okResult = Assert.IsType<Ok<IEnumerable<EventDTO>>>(result);
            Assert.Equal(expectedAbsences, okResult.Value);
        }
    }
}
