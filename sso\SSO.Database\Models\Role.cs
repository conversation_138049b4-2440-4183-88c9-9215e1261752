﻿using System.ComponentModel.DataAnnotations.Schema;
using System.ComponentModel.DataAnnotations;

namespace SSO.Database.Models
{
    public class Role
    {
        [Key]
        [DatabaseGenerated(DatabaseGeneratedOption.Identity)]
        public Guid Id { get; set; }

        public required string Name { get; set; }

        public required int UserRegistrationCompanyId { get; set; }

        public List<Permission> Permissions { get; set; } = [];

        public List<RoleUser> UserRoles { get; set; } = [];
    }
}
