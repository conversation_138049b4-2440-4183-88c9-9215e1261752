import {
  EditStatus,
  EmployeePropertyEditDTO,
} from "../../models/DTOs/editEmployee/EmployeePropertyEditDTO";
import { EmployeeDTO } from "../../models/DTOs/employees/EmployeeDTO";

/**
 * Service to help manage property-level edits and create virtual employee objects
 * that show pending changes for non-admin users
 */
export class PropertyEditService {
  /**
   * Creates a virtual employee object that shows pending changes for non-admin users
   * For admins, returns the original employee (they see current values)
   */
  static createVirtualEmployeeWithPendingChanges(
    employee: EmployeeDTO,
    propertyEdits: EmployeePropertyEditDTO[],
    isAdmin: boolean
  ): EmployeeDTO {
    if (isAdmin || !propertyEdits?.length) {
      return employee;
    }

    // For non-admin users, show pending changes as if they were applied
    const virtualEmployee = { ...employee };

    // Get all pending edits for the Employee object
    const pendingEmployeeEdits = this.getPendingEditsForObject(
      propertyEdits,
      "Employee",
      employee.workTimeId
    );

    // Apply pending changes to create virtual employee
    pendingEmployeeEdits.forEach((edit) => {
      if (edit.newValue !== null) {
        this.applyPropertyChange(
          virtualEmployee,
          edit.propertyName,
          edit.newValue
        );
      }
    });

    return virtualEmployee;
  }

  /**
   * Gets all pending edits for a specific object
   */
  static getPendingEditsForObject(
    propertyEdits: EmployeePropertyEditDTO[],
    objectName: string,
    objectId: string
  ): EmployeePropertyEditDTO[] {
    return propertyEdits.filter(
      (edit) =>
        edit.objectName === objectName &&
        edit.objectId === objectId &&
        edit.editStatus === EditStatus.Pending
    );
  }

  static getEditsForObject(
    propertyEdits: EmployeePropertyEditDTO[],
    objectName: string,
    objectId: string
  ): EmployeePropertyEditDTO[] {
    return propertyEdits.filter(
      (edit) => edit.objectName === objectName && edit.objectId === objectId
    );
  }

  /**
   * Checks if there are any pending edits for a specific property
   */
  static hasPendingEditForProperty(
    propertyEdits: EmployeePropertyEditDTO[],
    objectName: string,
    objectId: string,
    propertyName: string
  ): boolean {
    const pendingEdits = this.getPendingEditsForObject(
      propertyEdits,
      objectName,
      objectId
    );
    return pendingEdits.some((edit) => edit.propertyName === propertyName);
  }

  static hasEditForProperty(
    propertyEdits: EmployeePropertyEditDTO[],
    objectName: string,
    objectId: string,
    propertyName: string
  ): boolean {
    const pendingEdits = this.getEditsForObject(
      propertyEdits,
      objectName,
      objectId
    );
    return pendingEdits.some((edit) => edit.propertyName === propertyName);
  }

  /**
   * Gets the pending edit for a specific property
   */
  static getPendingEditForProperty(
    propertyEdits: EmployeePropertyEditDTO[],
    objectName: string,
    objectId: string,
    propertyName: string
  ): EmployeePropertyEditDTO | null {
    const pendingEdits = this.getPendingEditsForObject(
      propertyEdits,
      objectName,
      objectId
    );
    return (
      pendingEdits.find((edit) => edit.propertyName === propertyName) || null
    );
  }

  /**
   * Gets any edit (pending, approved, or declined) for a specific property
   */
  static getEditForProperty(
    propertyEdits: EmployeePropertyEditDTO[],
    objectName: string,
    objectId: string,
    propertyName: string
  ): EmployeePropertyEditDTO | null {
    const edits = propertyEdits.filter(
      (edit) =>
        edit.objectName === objectName &&
        edit.objectId === objectId &&
        edit.propertyName === propertyName
    );
    // Return the most recent edit (sorted by createdAt descending)
    return (
      edits.sort(
        (a, b) =>
          new Date(b.createdAt).getTime() - new Date(a.createdAt).getTime()
      )[0] || null
    );
  }

  /**
   * Gets the old value for a property from pending edits (for admin display)
   */
  static getOldValueForProperty(
    propertyEdits: EmployeePropertyEditDTO[],
    objectName: string,
    objectId: string,
    propertyName: string
  ): string | null {
    const edit = this.getPendingEditForProperty(
      propertyEdits,
      objectName,
      objectId,
      propertyName
    );
    return edit?.oldValue || null;
  }

  /**
   * Applies a property change to an object (used for creating virtual objects)
   */
  private static applyPropertyChange(
    obj: any,
    propertyName: string,
    newValue: string
  ): void {
    try {
      // Convert property name to camelCase for JavaScript object access
      const camelCaseProperty = this.toCamelCase(propertyName);

      // Try to parse as JSON first, fallback to string
      let parsedValue: any;
      try {
        parsedValue = JSON.parse(newValue);
      } catch {
        parsedValue = newValue;
      }

      obj[camelCaseProperty] = parsedValue;
    } catch (error) {
      console.warn(
        `Failed to apply property change for ${propertyName}:`,
        error
      );
    }
  }

  /**
   * Converts PascalCase property names from backend to camelCase for frontend
   */
  private static toCamelCase(str: string): string {
    return str.charAt(0).toLowerCase() + str.slice(1);
  }

  /**
   * Checks if there are any pending edits in the list
   */
  static hasPendingEdits(propertyEdits: EmployeePropertyEditDTO[]): boolean {
    return (
      propertyEdits?.some((edit) => edit.editStatus === EditStatus.Pending) ||
      false
    );
  }

  /**
   * Gets the total count of pending edits
   */
  static getPendingEditsCount(
    propertyEdits: EmployeePropertyEditDTO[]
  ): number {
    return (
      propertyEdits?.filter((edit) => edit.editStatus === EditStatus.Pending)
        .length || 0
    );
  }

  /**
   * Gets all pending edits for a specific object type (e.g., all Address edits)
   */
  static getPendingEditsForObjectType(
    propertyEdits: EmployeePropertyEditDTO[],
    objectName: string
  ): EmployeePropertyEditDTO[] {
    return propertyEdits.filter(
      (edit) =>
        edit.objectName === objectName && edit.editStatus === EditStatus.Pending
    );
  }

  /**
   * Checks if there are any pending edits for a specific object type
   */
  static hasPendingEditsForObjectType(
    propertyEdits: EmployeePropertyEditDTO[],
    objectName: string
  ): boolean {
    return (
      this.getPendingEditsForObjectType(propertyEdits, objectName).length > 0
    );
  }

  /**
   * Gets all unique object IDs that have pending edits for a specific object type
   */
  static getObjectIdsWithPendingEdits(
    propertyEdits: EmployeePropertyEditDTO[],
    objectName: string
  ): string[] {
    const pendingEdits = this.getPendingEditsForObjectType(
      propertyEdits,
      objectName
    );
    const uniqueIds = [...new Set(pendingEdits.map((edit) => edit.objectId))];
    return uniqueIds;
  }

  /**
   * Creates a virtual address object that shows pending changes for non-admin users
   */
  static createVirtualAddressWithPendingChanges(
    originalAddress: any,
    propertyEdits: EmployeePropertyEditDTO[],
    addressId: string,
    isAdmin: boolean
  ): any {
    if (isAdmin || !propertyEdits?.length) {
      return originalAddress;
    }

    // For non-admin users, show pending changes as if they were applied
    const virtualAddress = { ...originalAddress };

    // Get all pending edits for this specific address
    const pendingAddressEdits = this.getPendingEditsForObject(
      propertyEdits,
      "Address",
      addressId
    );

    // Apply pending changes to create virtual address
    pendingAddressEdits.forEach((edit) => {
      if (edit.newValue !== null) {
        this.applyPropertyChange(
          virtualAddress,
          edit.propertyName,
          edit.newValue
        );
      }
    });

    return virtualAddress;
  }

  /**
   * Creates a virtual bank account object that shows pending changes for non-admin users
   */
  static createVirtualBankAccountWithPendingChanges(
    originalBankAccount: any,
    propertyEdits: EmployeePropertyEditDTO[],
    bankAccountId: string,
    isAdmin: boolean
  ): any {
    if (isAdmin || !propertyEdits?.length) {
      return originalBankAccount;
    }

    // For non-admin users, show pending changes as if they were applied
    const virtualBankAccount = { ...originalBankAccount };

    // Get all pending edits for this specific bank account
    const pendingBankAccountEdits = this.getPendingEditsForObject(
      propertyEdits,
      "BankAccount",
      bankAccountId
    );

    // Apply pending changes to create virtual bank account
    pendingBankAccountEdits.forEach((edit) => {
      if (edit.newValue !== null) {
        this.applyPropertyChange(
          virtualBankAccount,
          edit.propertyName,
          edit.newValue
        );
      }
    });

    return virtualBankAccount;
  }
}
