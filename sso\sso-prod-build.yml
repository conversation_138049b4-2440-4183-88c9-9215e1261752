trigger:
- release
pool:
  vmImage: 'windows-latest'
  name: 'Jurassic'

variables:
  - group: prod-variables
  - name: solution
    value: '**/*.sln'
  - name: buildPlatform
    value: 'Any CPU'
  - name: buildConfiguration
    value: 'Release'
  - name: appPoolName
    value: 'sso'

steps:
- task: UseDotNet@2
  inputs:
    packageType: 'sdk'
    version: '8.x'
    installationPath: $(Agent.ToolsDirectory)/dotnet

- task: NuGetToolInstaller@1

- script: dotnet build --configuration $(buildConfiguration)
  displayName: 'Dotnet Build'
  
- script: dotnet test --configuration $(buildConfiguration) $(Build.Repository.Name).Tests/$(Build.Repository.Name).Tests.csproj
  displayName: 'Run .NET Tests'

- task: PowerShell@2
  displayName: 'Stop IIS Application Pool'
  inputs:
    targetType: 'inline'
    script: |
      Set-Item WSMan:\localhost\Client\TrustedHosts -Value "$(MachineName)" -Force
      $securePassword = ConvertTo-SecureString "$(Password)" -AsPlainText -Force
      $credentials = New-Object System.Management.Automation.PSCredential ("$(Username)", $securePassword)
      $appPoolName = "$(appPoolName)"
      Invoke-Command -ComputerName $(MachineName) -Credential $credentials -ScriptBlock {
        Import-Module WebAdministration
        $state = (Get-WebAppPoolState -Name $using:appPoolName).Value
        if ($state -ne "Stopped") {
          Stop-WebAppPool -Name $using:appPoolName
        }
      }
      Set-Item WSMan:\localhost\Client\TrustedHosts -Value "" -Force

- task: PowerShell@2
  displayName: 'Remove Unwanted Files'
  inputs:
    targetType: 'inline'
    script: |
      Remove-Item -Path "$(Build.SourcesDirectory)/$(Build.Repository.Name)/$(Build.Repository.Name)/bin/Release/net8.0/Microsoft.CodeAnalysis.CSharp.dll" -Force -ErrorAction SilentlyContinue
      Remove-Item -Path "$(Build.SourcesDirectory)/$(Build.Repository.Name)/$(Build.Repository.Name)/bin/Release/net8.0/Microsoft.CodeAnalysis.CSharp.Workspaces.dll" -Force -ErrorAction SilentlyContinue
      Remove-Item -Path "$(Build.SourcesDirectory)/$(Build.Repository.Name)/$(Build.Repository.Name)/bin/Release/net8.0/Microsoft.CodeAnalysis.dll" -Force -ErrorAction SilentlyContinue
      Remove-Item -Path "$(Build.SourcesDirectory)/$(Build.Repository.Name)/$(Build.Repository.Name)/bin/Release/net8.0/Microsoft.CodeAnalysis.Workspaces.dll" -Force -ErrorAction SilentlyContinue

- task: WindowsMachineFileCopy@2
  displayName: 'Copy Files to Windows Machine'
  inputs:
      SourcePath: '$(Build.SourcesDirectory)/sso/bin/Release/net8.0' 
      MachineNames: '$(MachineName)' 
      AdminUserName: '$(Username)'
      AdminPassword: '$(Password)'
      TargetPath: 'C:\inetpub\wwwroot\sso'
      AdditionalArguments: "/is /it /MIR"

- task: PowerShell@2
  displayName: 'Start IIS Application Pool'
  inputs:
    targetType: 'inline'
    script: |
      Set-Item WSMan:\localhost\Client\TrustedHosts -Value "$(MachineName)" -Force
      $securePassword = ConvertTo-SecureString "$(Password)" -AsPlainText -Force
      $credentials = New-Object System.Management.Automation.PSCredential ("$(Username)", $securePassword)
      $appPoolName = "$(appPoolName)"
      Invoke-Command -ComputerName $(MachineName) -Credential $credentials -ScriptBlock {
        Import-Module WebAdministration
        $state = (Get-WebAppPoolState -Name $using:appPoolName).Value
        if ($state -ne "Started") {
          Start-WebAppPool -Name $using:appPoolName
        }
      }
      Set-Item WSMan:\localhost\Client\TrustedHosts -Value "" -Force
