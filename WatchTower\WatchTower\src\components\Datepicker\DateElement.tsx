import React from "react";
import styled, { css } from "styled-components";
import Translator from "../../services/language/Translator";
import { ViewMode } from "../../models/Enums/ViewMode";

const ContainerNameOfDay = styled.div`
  display: grid;
  justify-content: center;
  align-items: center;
  padding: 0.1em;
  font-size: 0.9em;
  color: var(--datepicker-view-buttons-font-color);
  width: 1.8em;
  height: 1.5em;
  grid-template-columns: repeat(7, 1fr);
  cursor: pointer;
`;

const ContainerDay = styled.div<{ selected: boolean }>`
  display: grid;
  justify-content: center;
  align-items: center;
  background-color: var(--datepicker-view-buttons-color);
  padding: 0.1em;
  font-size: 1em;
  width: 1.8em;
  height: 1.5em;
  grid-template-columns: repeat(7, 1fr);
  color: var(--datepicker-view-buttons-font-color);
  cursor: pointer;

  ${({ selected }) =>
    selected &&
    `
     background-color: var(--button-background-color);
  `}
`;

const ContainerOutsideDay = styled.div`
  display: grid;
  justify-content: center;
  align-items: center;
  background-color: var(--datepicker-view-background-color);
  color: var(--datepicker-view-buttonsNotInRange-font-color);
  padding: 0.1em;
  cursor: pointer;
  font-size: 1em;
  grid-template-columns: repeat(7, 1fr);
  width: 1.8em;
  height: 1.5em;
`;

const ContainerDays = styled.div<{ active: ViewMode }>`
  display: none;
  grid-template-columns: repeat(7, 1fr);
  gap: 0.1em;
  padding: 0.3em;
  border: solid 0.2em;
  border-color: var(--datepicker-view-buttons-color);
  background-color: var(--datepicker-view-border);
  border-radius: 1em;
  width: 15.5em;
  ${ContainerDay}:hover {
    background-color: var(--datepicker-view-buttons-hover-color);
  }
  ${ContainerOutsideDay}:hover {
    background-color: var(--datepicker-view-buttons-hover-color);
  }

  ${({ active }) =>
    active === ViewMode.DatesView &&
    `
    display: grid;
  `}
`;

interface DateElementProps {
  selectedDay: number;
  selectedMonth: number;
  selectedYear: number;
  handleLastMonthDateClick?: (day: number) => void;
  handleDateClick: (day: number, index?: number) => void;
  handleNextMonthDateClick?: (day: number) => void;
  active: ViewMode;
}

const DatesElement: React.FC<DateElementProps> = ({
  selectedDay,
  selectedMonth,
  selectedYear,
  handleDateClick,
  active,
}) => {
  const renderDaysOfWeek = () => {
    const daysOfWeek = [
      "strMon",
      "strTue",
      "strWed",
      "strThu",
      "strFri",
      "strSat",
      "strSun",
    ];

    const daysElement = [];

    for (let i = 0; i < 7; i++) {
      daysElement.push(
        <ContainerNameOfDay key={`days-of-week-${i}`}>
          <Translator getString={daysOfWeek[i]} />
        </ContainerNameOfDay>
      );
    }

    return daysElement;
  };

  const daysInMonth = (selectedMonth: number, year: number) => {
    return new Date(year, selectedMonth + 1, 0).getDate();
  };

  const populateDates = () => {
    const daysElement: JSX.Element[] = [];

    const amountDays = daysInMonth(selectedMonth, selectedYear);
    const amountDaysLastMonth = daysInMonth(selectedYear, selectedMonth - 1);

    const firstDayOfMonth = new Date(selectedYear, selectedMonth, 1);
    const lastDayOfMonth = new Date(selectedYear, selectedMonth, amountDays);
    let lastDayOfWeek = lastDayOfMonth.getDay() || 7;
    let startingDayOfWeek = firstDayOfMonth.getDay() || 7;

    if (startingDayOfWeek == 1) {
      startingDayOfWeek += 7;
    } else if (startingDayOfWeek + 7 - lastDayOfWeek < 9) {
      lastDayOfWeek -= 7;
    }

    for (let i = 1; i < startingDayOfWeek; i++) {
      daysElement.push(
        <ContainerOutsideDay
          onClick={() =>
            handleDateClick(amountDaysLastMonth - startingDayOfWeek + i + 1, -1)
          }
          key={`empty-${amountDaysLastMonth - startingDayOfWeek + i + 1}`}
        >
          {amountDaysLastMonth - startingDayOfWeek + i + 1}
        </ContainerOutsideDay>
      );
    }

    let selected = false;
    for (let i = 0; i < amountDays; i++) {
      selectedDay === i + 1 ? (selected = true) : (selected = false);

      daysElement.push(
        <ContainerDay
          key={`day-${i}`}
          selected={selected}
          onClick={() => handleDateClick(i + 1)}
        >
          {i + 1}
        </ContainerDay>
      );
    }

    for (let i = lastDayOfWeek; i < 7; i++) {
      daysElement.push(
        <ContainerOutsideDay
          key={`day-last-${i}`}
          onClick={() => handleDateClick(i - lastDayOfWeek + 1, +1)}
        >
          {i - lastDayOfWeek + 1}
        </ContainerOutsideDay>
      );
    }

    return (
      <ContainerDays data-testid="days-container" active={active}>
        {renderDaysOfWeek()}
        {daysElement}
      </ContainerDays>
    );
  };

  return <>{populateDates()}</>;
};

export default DatesElement;
