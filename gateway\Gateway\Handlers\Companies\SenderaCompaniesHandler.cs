﻿using Gateway.Common.Globals;
using Gateway.Common.Redis.Models;
using Gateway.Common.Redis.Services.Interfaces;
using Gateway.Common.ResponseObjects;
using Gateway.Connections.Interfaces;
using Gateway.Requests.UserRegistration.Companies;
using MediatR;
using WorkTimeApi.Common.DTOs.Companies;
using WorkTimeApi.Common.Requests.Companies;

namespace Gateway.Handlers.Companies
{
    public class SenderaCompaniesHandler(GlobalUser globalUser,
        IWorkTimeApiConnection workTimeConnection,
        IRedisService<RedisCompanyDTO> companiesRedisService) : IRequestHandler<GetSenderaCompaniesRequest, IResult>
    {
        public async Task<IResult> Handle(GetSenderaCompaniesRequest request, CancellationToken cancellationToken)
        {
            var senderaCompanies = companiesRedisService.GetManyByKey(globalUser.Email);
            if (senderaCompanies is null)
                return Results.NoContent();

            var worktimeCompaniesResponse = await workTimeConnection.GetCompaniesAsync(new GetCompaniesRequest { UserId = globalUser.Id });
            if (!worktimeCompaniesResponse.IsSuccessStatusCode)
                return Results.StatusCode((int)worktimeCompaniesResponse.StatusCode);

            var getCompaniesResponse = await worktimeCompaniesResponse.Content.ReadFromJsonAsync<GetCompaniesResponse>(cancellationToken: cancellationToken);
            getCompaniesResponse ??= new GetCompaniesResponse { ActiveCompanies = [], PendingCompanies = [] };

            List<CompanyDTO> worktimeCompanies = [.. getCompaniesResponse.ActiveCompanies, .. getCompaniesResponse.PendingCompanies];

            var companiesReponseObjects = senderaCompanies
                .Where(uc => !worktimeCompanies
                                .Select(wtc => wtc.UserRegistrationCompanyId)
                                .Contains(uc.Id))
                .Select(c => new SenderaCompanyResponseObject
                {
                    Id = c.Id,
                    Name = c.Name,
                    Bulstat = c.Bulstat,
                    ContactName = c.ContactName,
                    UserStatus = c.Users.FirstOrDefault(u => u.Email.ToLowerInvariant() == globalUser.Email.ToLowerInvariant())?.UserStatus
                });

            return Results.Ok(companiesReponseObjects);
        }
    }
}