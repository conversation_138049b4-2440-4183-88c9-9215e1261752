﻿using Microsoft.EntityFrameworkCore;
using SSO.Common;
using SSO.Common.DTOs;
using SSO.Common.Responses;
using SSO.Database.Models;
using SSO.Database.Repositories.Interfaces;

namespace SSO.Database.Repositories
{
    public class UsersRepository(IDbContextFactory<SSODbContext> contextFactory) : IUsersRepository
    {
        private readonly IDbContextFactory<SSODbContext> _contextFactory = contextFactory;

        public async Task<User> AddUserAsync(User user)
        {
            var context = await _contextFactory.CreateDbContextAsync();

            await context.Users.AddAsync(user);
            await context.SaveChangesAsync();

            return user;
        }

        public async Task<User> UpdateUserAsnyc(User user)
        {
            var context = await _contextFactory.CreateDbContextAsync();

            context.Update(user);
            await context.SaveChangesAsync();

            return user;
        }

        public async Task DeactivateAllTokensForUserAsync(User user)
        {
            var context = await _contextFactory.CreateDbContextAsync();

            var refreshTokens = context.Users.FirstOrDefault(rt => rt.Id == user.Id)?.RefreshTokens;

            if (refreshTokens is null)
                return;

            refreshTokens.ForEach(rt => rt.IsActive = false);

            await context.SaveChangesAsync();
        }

        public async Task<User?> FindUserByIdAsync(Guid id)
        {
            var context = await _contextFactory.CreateDbContextAsync();

            return await context.Users
                .FirstOrDefaultAsync(rt => rt.Id == id);
        }

        public async Task<User?> FindUserByEmailAsync(string email)
        {
            var context = await _contextFactory.CreateDbContextAsync();

            return await context.Users.FirstOrDefaultAsync(rt => rt.Email == email.ToLowerInvariant());
        }

        public async Task<List<User>> GetUsersByPermissionAsync(string permission)
        {
            var context = await _contextFactory.CreateDbContextAsync();

            return await context.Users
                .Include(u => u.UserRoles)
                .ThenInclude(ur => ur.Role)
                .ThenInclude(r => r.Permissions)
                .Where(rt => rt.UserRoles.Select(ur => ur.Role).Any(r => r.Permissions.Any(p => p.Name == permission)))
                .ToListAsync();
        }

        public async Task<List<UserPermissionsDTO>> GetUserPermissionsAsync(Guid userId)
        {
            var context = await _contextFactory.CreateDbContextAsync();

            return context
                    .Users
                    .Include(u => u.UserRoles)
                    .ThenInclude(r => r.Role)
                    .ThenInclude(r => r.Permissions)
                    .FirstOrDefault(u => u.Id == userId)
                    ?.UserRoles
                    .GroupBy(ur => ur.UserRegistrationCompanyId)
                    .Select(ur => new UserPermissionsDTO
                    {
                        CompanyId = ur.Key,
                        Permissions = ur.SelectMany(r => r.Role.Permissions).Select(p => p.Name).ToList()
                    })
                    .ToList()
                ?? [];
        }

        public async Task<AddSupportChatPermissionsResult> AddSupportChatPermissionsAsync(User user)
        {
            var context = await _contextFactory.CreateDbContextAsync();

            var role = await context.Roles.FirstOrDefaultAsync(r => r.Name == "SupportChatRole" && r.UserRegistrationCompanyId == -10000);
            if (role is null)
            {
                var permissions = await context.Permissions.Where(p => p.Name == Permissions.SupportChatService.Chats || p.Name == Permissions.SupportChatService.Users).ToListAsync();

                role = new Role
                {
                    Name = "SupportChatRole",
                    UserRegistrationCompanyId = -10000,
                    Permissions = permissions,
                };

                await context.Roles.AddAsync(role);
            }

            var existingUser = await context.Users
                .Include(user => user.UserRoles)
                .ThenInclude(ur => ur.Role)
                .FirstOrDefaultAsync(u => u.Id == user.Id);
            if (existingUser is null)
            {
                existingUser = user;

                await context.Users.AddAsync(existingUser);
            }
            else if (existingUser.UserRoles.Any(ur => ur.Role.Id == role.Id))
            {
                return new AddSupportChatPermissionsResult
                {
                    Success = false,
                    Message = "Role already exists for this user"
                };
            }

            existingUser.UserRoles.Add(new RoleUser
            {
                UsersId = existingUser.Id,
                RolesId = role.Id,
                UserRegistrationCompanyId = -10000
            });

            await context.SaveChangesAsync();

            return new AddSupportChatPermissionsResult
            {
                Success = true,
                Message = "Role was successfully added"
            };
        }

        public async Task<List<string>> GetUserCompanyPermissionsAsync(Guid userId, int companyId)
        {
            var context = await _contextFactory.CreateDbContextAsync();

            var userRole = await context.RoleUsers.Include(r=>r.Role).ThenInclude(r=>r.Permissions)
                .FirstOrDefaultAsync(rl=>rl.UsersId == userId && rl.UserRegistrationCompanyId == companyId);

            if (userRole == null || userRole.Role == null)
                return new List<string>();

            return userRole.Role.Permissions.Select(p => p.Name).ToList();
        }
    }
}
