import { useNavigate } from "react-router-dom";
import styled from "styled-components";
import ProfileImage from "../../assets/images/menu/profile.png";

const Image = styled.img`
  margin-left: 0.5rem;
  cursor: pointer;
`;

const Wrapper = styled.div`
  display: flex;
  justify-content: center;
  align-items: center;
`;

const LoggedNavMenu = () => {
  const navigate = useNavigate();

  const handleProfileClick = () => {
    navigate("/auth/profile");
  };

  return (
    <Wrapper>
      <Image onClick={handleProfileClick} src={ProfileImage} />
    </Wrapper>
  );
};

export default LoggedNavMenu;
