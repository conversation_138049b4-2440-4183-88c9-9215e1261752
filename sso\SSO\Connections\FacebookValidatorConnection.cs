﻿using SSO.Connections.Interfaces;

namespace SSO.Connections
{
    public class FacebookValidatorConnection : IFacebookValidatorConnection
    {
        private readonly HttpClient _httpClient;
        private const string FACEBOOK_API_URL = "https://graph.facebook.com/me?access_token=";

        public FacebookValidatorConnection(HttpClient httpClient)
        {
            _httpClient = httpClient;
        }

        public Task<HttpResponseMessage> ValidateFacebookTokenAsync(string token)
        {
            return _httpClient.GetAsync($"{FACEBOOK_API_URL}{token}&fields=[\"last_name\",\"email\",\"first_name\"]&method=get&pretty=0&sdk=joey&suppress_http_code=1");
        }
    }
}
