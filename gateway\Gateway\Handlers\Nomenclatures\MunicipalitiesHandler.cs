﻿using Gateway.Common.Results;
using Gateway.Connections.Interfaces;
using MediatR;
using WorkTimeApi.Common.Requests.Nomenclatures;

namespace Gateway.Handlers.Nomenclatures
{
    public class MunicipalitiesHandler : IRequestHandler<GetMunicipalitiesRequest, IResult>
    {
        private readonly IWorkTimeApiConnection _workTimeApiConnection;

        public MunicipalitiesHandler(IWorkTimeApiConnection workTimeApiConnection)
        {
            _workTimeApiConnection = workTimeApiConnection;
        }

        public async Task<IResult> Handle(GetMunicipalitiesRequest request, CancellationToken cancellationToken)
        {
            var municipalities = await _workTimeApiConnection.GetMunicipalitiesAsync();
            return new HttpResponseMessageResult(municipalities);
        }
    }
}