﻿using Gateway.Common.Results;
using Gateway.Connections.Interfaces;
using MediatR;
using WorkTimeApi.Common.Requests.TRZ;

namespace Gateway.Handlers.TRZ
{
    public class UpdateTRZDepartmentsHandler : IRequestHandler<UpdateTRZDepartmentsRequest, IResult>
    {
        private readonly IWorkTimeApiConnection _workTimeApiConnection;

        public UpdateTRZDepartmentsHandler(IWorkTimeApiConnection workTimeApiConnection)
        {
            _workTimeApiConnection = workTimeApiConnection;
        }

        public async Task<IResult> Handle(UpdateTRZDepartmentsRequest request, CancellationToken cancellationToken)
        {
            var addDepartmentsResponse = await _workTimeApiConnection.UpdateTRZDepartmentsAsync(request);

            return new HttpResponseMessageResult(addDepartmentsResponse);
        }
    }
}
