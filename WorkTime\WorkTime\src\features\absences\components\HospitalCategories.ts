export interface HospitalSubOption {
  id: string;
  title: string;
  subtitle?: string;
  eventType: number;
}

export interface HospitalCategory {
  key: string;
  eventType?: number;
  subOptions?: HospitalSubOption[];
}

export const hospitalCategories: HospitalCategory[] = [
  {
    key: "Sick Leave",
    subOptions: [
      {
        id: "sick_leave_basic",
        title: "Sick Leave",
        eventType: 601,
      },
      {
        id: "sick_leave_family_care",
        title: "Caring for Sick Family Member",
        eventType: 602,
      },
      {
        id: "sick_leave_work_accident",
        title: "Work Accident",
        eventType: 603,
      },
      {
        id: "sick_leave_occupational_disease",
        title: "Occupational Disease",
        eventType: 604,
      },
      {
        id: "sick_leave_unpaid_disability",
        title: "Unpaid Leave for Temporary Disability",
        eventType: 605,
      },
      {
        id: "sick_leave_pregnancy",
        title: "Sick Leave During Pregnancy",
        eventType: 606,
      },
      {
        id: "sick_leave_after_birth",
        title: "Sick Leave After Birth",
        eventType: 607,
      },
      {
        id: "sick_leave_pregnancy_birth_unpaid",
        title: "Unpaid Leave for Pregnancy and Birth",
        eventType: 608,
      },
    ],
  },
  {
    key: "Maternity Leave",
    subOptions: [
      {
        id: "maternity_leave_one_thirty_five_to_four_ten",
        title: "Maternity Leave 135-410 Days",
        eventType: 701,
      },
      {
        id: "paternity_leave_over_six_months",
        title: "Paternity Leave for Child Care Over 6 Months",
        eventType: 702,
      },
      {
        id: "leave_fifteen_days_birth",
        title: "Leave Up to 15 Days for Child Birth",
        eventType: 703,
      },
      {
        id: "leave_child_care_two_years",
        title: "Leave for Child Care Up to 2 Years",
        eventType: 704,
      },
      {
        id: "leave_adoption_five_years",
        title: "Leave for Child Adoption Up to 5 Years",
        eventType: 705,
      },
      {
        id: "unpaid_leave_adoption_five_years",
        title: "Unpaid Leave for Child Adoption Up to 5 Years",
        eventType: 706,
      },
      {
        id: "paternity_leave_adoption_five_years",
        title: "Paternity Leave for Child Adoption Up to 5 Years",
        eventType: 707,
      },
      {
        id: "leave_child_care_eight_years_father",
        title: "Leave for Child Care Up to 8 Years by Father (Adopter)",
        eventType: 708,
      },
    ],
  },
];
