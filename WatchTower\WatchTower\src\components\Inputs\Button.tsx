import styled, { css } from "styled-components";
import Translator from "../../services/language/Translator";

interface ButtonProps
  extends React.DetailedHTMLProps<
    React.ButtonHTMLAttributes<HTMLButtonElement>,
    HTMLButtonElement
  > {
  label: string;
  imageSrc?: string;
  ref?: React.Ref<HTMLButtonElement>;
}

const Container = styled.button<ButtonProps>`
  padding: 0.6rem 0.9rem;

  cursor: pointer;
  border: none;
  border-radius: 1.9rem;
  font-weight: 500;
  outline: none;
  color: var(--button-text-color);
  text-align: center;
  font: 1.1rem/1.5rem Segoe UI;
  background: var(--button-background-color);
  margin-top: 0.5rem;
  ${(props) => props.disabled && DISABLED}
`;

const Image = styled.img`
  width: 1.5rem;
  height: 1.5rem;
  cursor: pointer;
  margin-right: 0.5rem;
  margin-left: 0.2rem;
`;

const DISABLED = css`
  background: var(--button-background-color-disabled);
`;

const Button = ({ label, imageSrc, ref, ...rest }: ButtonProps) => {
  return (
    <Container label={label} ref={ref} {...rest}>
      {imageSrc && <Image src={imageSrc} />}
      <Translator getString={label} />
    </Container>
  );
};

export default Button;
