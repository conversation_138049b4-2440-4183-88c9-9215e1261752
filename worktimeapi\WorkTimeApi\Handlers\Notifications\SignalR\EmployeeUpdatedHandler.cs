using MediatR;
using WorkTime.Notifications.Services.Interfaces;
using WorkTimeApi.Common.Notifications.SignalR;

namespace WorkTimeApi.Handlers.Notifications.SignalR
{
    public class EmployeeUpdatedHandler(ISignalRNotificationService signalRNotificationService)
        : INotificationHandler<EmployeeUpdatedNotification>
    {
        public async Task Handle(EmployeeUpdatedNotification notification, CancellationToken cancellationToken)
        {
            var pushNotifications = signalRNotificationService.BroadcastForCompanyAsync(notification);

            await Task.WhenAll(pushNotifications);
        }
    }
}
