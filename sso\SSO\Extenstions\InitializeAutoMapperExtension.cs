﻿using AutoMapper;
using SSO.Mapper;

namespace SSO.Extenstions
{
    public static class InitializeAutoMapperExtension
    {
        public static void InitializeAutoMapper(this IServiceCollection services)
        {
            var mapperConfig = new MapperConfiguration(mc =>
            {
                mc.AddProfile(new MapperProfile());
            });

            IMapper mapper = mapperConfig.CreateMapper();
            services.AddSingleton(mapper);
        }
    }
}
