﻿using MediatR;
using WatchTower.Api.Services.Interfaces;
using WatchTower.Common.Requests;

namespace WatchTower.Api.Handlers.Info
{
    public class GetAllHealthChecksHandler : IRequestHandler<GetAllHealthChecksRequest, IResult>
    {
        private readonly IHealthCheckService _healthCheckService;

        public GetAllHealthChecksHandler(IHealthCheckService healthCheckService)
        {
            _healthCheckService = healthCheckService;
        }

        public async Task<IResult> Handle(GetAllHealthChecksRequest request, CancellationToken cancellationToken)
            => Results.Ok(await _healthCheckService.GetAllAsync(cancellationToken));
    }
}
