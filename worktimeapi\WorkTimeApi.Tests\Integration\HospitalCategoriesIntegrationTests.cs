using WorkTimeApi.Common.Enums;
using Xunit;

namespace WorkTimeApi.Tests.Integration;

public class HospitalCategoriesIntegrationTests
{
    [Fact]
    public void Hospital_Categories_Should_Support_Complete_Workflow_From_Frontend_To_TRZ()
    {
        // Arrange - Simulate frontend hospital category selections
        var frontendHospitalSelections = new[]
        {
            // Sick Leave category selections
            new { Category = "Sick Leave", SubOption = "Sick Leave", EventType = EventType.Болничен },
            new { Category = "Sick Leave", SubOption = "Caring for Sick Family Member", EventType = EventType.ГледанеНаБоленЧленОтСемейството },
            new { Category = "Sick Leave", SubOption = "Work Accident", EventType = EventType.ТрудоваЗлополука },
            new { Category = "Sick Leave", SubOption = "Occupational Disease", EventType = EventType.ПрофесионалнаБолест },
            new { Category = "Sick Leave", SubOption = "Unpaid Leave for Temporary Disability", EventType = EventType.НеплатенЗаВременнаНеработоспособност },
            new { Category = "Sick Leave", SubOption = "Sick Leave During Pregnancy", EventType = EventType.БолниченПоБременност },
            new { Category = "Sick Leave", SubOption = "Sick Leave After Birth", EventType = EventType.БолниченСледРаждане },
            new { Category = "Sick Leave", SubOption = "Unpaid Leave for Pregnancy and Birth", EventType = EventType.НеплатенЗаБременностИРаждане },

            // Maternity Leave category selections
            new { Category = "Maternity Leave", SubOption = "Maternity Leave 135-410 Days", EventType = EventType.ОтпускЗаМайкаСледРаждане135До410Ден },
            new { Category = "Maternity Leave", SubOption = "Paternity Leave for Child Care Over 6 Months", EventType = EventType.ОтпускЗаБащаЗаГледанеНаДетеНад6Месеца },
            new { Category = "Maternity Leave", SubOption = "Leave Up to 15 Days for Child Birth", EventType = EventType.ОтпускДо15ДниПриРажданеНаДете },
            new { Category = "Maternity Leave", SubOption = "Leave for Child Care Up to 2 Years", EventType = EventType.ОтпускЗаОтглежданеНаДетеДо2Години },
            new { Category = "Maternity Leave", SubOption = "Leave for Child Adoption Up to 5 Years", EventType = EventType.ОтпускПриОсиновяванеНаДетеДо5ГодишнаВъзраст },
            new { Category = "Maternity Leave", SubOption = "Unpaid Leave for Child Adoption Up to 5 Years", EventType = EventType.НеплатенОтпускПриОсиновяванеНаДетеДо5ГодишнаВъзраст },
            new { Category = "Maternity Leave", SubOption = "Paternity Leave for Child Adoption Up to 5 Years", EventType = EventType.ОтпускЗаБащаПриОсиновяванеНаДетеДо5ГодишнаВъзраст },
            new { Category = "Maternity Leave", SubOption = "Leave for Child Care Up to 8 Years by Father (Adopter)", EventType = EventType.ОтпускЗаОтглежданеНаДетеДо8ГодишнаВъзрастОтБащата }
        };

        foreach (var selection in frontendHospitalSelections)
        {
            // Act - Simulate backend processing
            var eventType = selection.EventType;
            var isHospital = eventType.IsHospital();
            var trzEventType = eventType.ToTRZEventType();
            var mappedBackEventType = trzEventType.ToEventType();

            // Assert - Verify complete workflow
            Assert.True(isHospital, $"EventType {eventType} should be identified as hospital-related");
            Assert.True(trzEventType.IsHospital(), $"TRZEventType {trzEventType} should be identified as hospital-related");
            Assert.Equal(eventType, mappedBackEventType); // Bidirectional mapping consistency
            Assert.True((int)trzEventType >= 1 && (int)trzEventType <= 26, $"TRZEventType {trzEventType} should have valid ID (1-26)");
        }
    }

    [Fact]
    public void Hospital_Categories_Should_Have_Consistent_Event_Type_Ranges()
    {
        // Arrange - Define expected ranges
        var sickLeaveEventTypes = new[]
        {
            EventType.Болничен, // 601
            EventType.ГледанеНаБоленЧленОтСемейството, // 602
            EventType.ТрудоваЗлополука, // 603
            EventType.ПрофесионалнаБолест, // 604
            EventType.НеплатенЗаВременнаНеработоспособност, // 605
            EventType.БолниченПоБременност, // 606
            EventType.БолниченСледРаждане, // 607
            EventType.НеплатенЗаБременностИРаждане // 608
        };

        var maternityLeaveEventTypes = new[]
        {
            EventType.ОтпускЗаМайкаСледРаждане135До410Ден, // 701
            EventType.ОтпускЗаБащаЗаГледанеНаДетеНад6Месеца, // 702
            EventType.ОтпускДо15ДниПриРажданеНаДете, // 703
            EventType.ОтпускЗаОтглежданеНаДетеДо2Години, // 704
            EventType.ОтпускПриОсиновяванеНаДетеДо5ГодишнаВъзраст, // 705
            EventType.НеплатенОтпускПриОсиновяванеНаДетеДо5ГодишнаВъзраст, // 706
            EventType.ОтпускЗаБащаПриОсиновяванеНаДетеДо5ГодишнаВъзраст, // 707
            EventType.ОтпускЗаОтглежданеНаДетеДо8ГодишнаВъзрастОтБащата // 708
        };

        // Act & Assert - Verify Sick Leave range (601-608)
        foreach (var eventType in sickLeaveEventTypes)
        {
            var eventTypeId = (int)eventType;
            Assert.True(eventTypeId >= 601 && eventTypeId <= 608,
                $"Sick Leave EventType {eventType} (ID: {eventTypeId}) should be in range 601-608");
            Assert.True(eventType.IsHospital(),
                $"Sick Leave EventType {eventType} should be identified as hospital-related");
        }

        // Act & Assert - Verify Maternity Leave range (701-708)
        foreach (var eventType in maternityLeaveEventTypes)
        {
            var eventTypeId = (int)eventType;
            Assert.True(eventTypeId >= 701 && eventTypeId <= 708,
                $"Maternity Leave EventType {eventType} (ID: {eventTypeId}) should be in range 701-708");
            Assert.True(eventType.IsHospital(),
                $"Maternity Leave EventType {eventType} should be identified as hospital-related");
        }
    }

    [Fact]
    public void Hospital_Categories_Should_Map_To_Existing_TRZ_Event_Types()
    {
        // Arrange - All hospital categories should map to existing TRZ event types (1-26)
        var allHospitalEventTypes = new[]
        {
            // Sick Leave (600 range)
            EventType.Болничен,
            EventType.ГледанеНаБоленЧленОтСемейството,
            EventType.ТрудоваЗлополука,
            EventType.ПрофесионалнаБолест,
            EventType.НеплатенЗаВременнаНеработоспособност,
            EventType.БолниченПоБременност,
            EventType.БолниченСледРаждане,
            EventType.НеплатенЗаБременностИРаждане,

            // Maternity Leave (700 range)
            EventType.ОтпускЗаМайкаСледРаждане135До410Ден,
            EventType.ОтпускЗаБащаЗаГледанеНаДетеНад6Месеца,
            EventType.ОтпускДо15ДниПриРажданеНаДете,
            EventType.ОтпускЗаОтглежданеНаДетеДо2Години,
            EventType.ОтпускПриОсиновяванеНаДетеДо5ГодишнаВъзраст,
            EventType.НеплатенОтпускПриОсиновяванеНаДетеДо5ГодишнаВъзраст,
            EventType.ОтпускЗаБащаПриОсиновяванеНаДетеДо5ГодишнаВъзраст,
            EventType.ОтпускЗаОтглежданеНаДетеДо8ГодишнаВъзрастОтБащата
        };

        foreach (var eventType in allHospitalEventTypes)
        {
            // Act
            var trzEventType = eventType.ToTRZEventType();
            var trzEventTypeId = (int)trzEventType;

            // Assert
            Assert.True(trzEventTypeId >= 1 && trzEventTypeId <= 26,
                $"Hospital EventType {eventType} should map to existing TRZEventType (1-26), but mapped to {trzEventType} (ID: {trzEventTypeId})");
            Assert.True(trzEventType.IsHospital(),
                $"Mapped TRZEventType {trzEventType} should be identified as hospital-related");
        }
    }

    [Fact]
    public void Hospital_Categories_Should_Support_All_Required_Business_Scenarios()
    {
        // This test verifies that all required business scenarios are supported

        // Arrange - Define business scenarios
        var businessScenarios = new[]
        {
            // Sick Leave Scenarios
            new { Scenario = "Basic Sick Leave", EventType = EventType.Болничен, ExpectedTRZ = TRZEventType.Болничен },
            new { Scenario = "Caring for Sick Family Member", EventType = EventType.ГледанеНаБоленЧленОтСемейството, ExpectedTRZ = TRZEventType.ГледанеНаБоленЧлен },
            new { Scenario = "Work Accident", EventType = EventType.ТрудоваЗлополука, ExpectedTRZ = TRZEventType.ТрудоваЗлополука },
            new { Scenario = "Occupational Disease", EventType = EventType.ПрофесионалнаБолест, ExpectedTRZ = TRZEventType.ПрофесионалнаБолест },
            new { Scenario = "Unpaid Temporary Disability", EventType = EventType.НеплатенЗаВременнаНеработоспособност, ExpectedTRZ = TRZEventType.НеплатенПоНетрудоспособност },
            new { Scenario = "Sick Leave During Pregnancy", EventType = EventType.БолниченПоБременност, ExpectedTRZ = TRZEventType.БолниченПоБременност },
            new { Scenario = "Sick Leave After Birth", EventType = EventType.БолниченСледРаждане, ExpectedTRZ = TRZEventType.БолниченСледРаждане },
            new { Scenario = "Unpaid Pregnancy and Birth", EventType = EventType.НеплатенЗаБременностИРаждане, ExpectedTRZ = TRZEventType.НеплатенЗаБременност },

            // Maternity Leave Scenarios
            new { Scenario = "Maternity Leave 135-410 Days", EventType = EventType.ОтпускЗаМайкаСледРаждане135До410Ден, ExpectedTRZ = TRZEventType.ОтпускМайка135До410 },
            new { Scenario = "Paternity Leave Over 6 Months", EventType = EventType.ОтпускЗаБащаЗаГледанеНаДетеНад6Месеца, ExpectedTRZ = TRZEventType.ОтпускБащаНад6Месеца },
            new { Scenario = "Leave Up to 15 Days for Child Birth", EventType = EventType.ОтпускДо15ДниПриРажданеНаДете, ExpectedTRZ = TRZEventType.Отпуск15ДниРаждане },
            new { Scenario = "Leave for Child Care Up to 2 Years", EventType = EventType.ОтпускЗаОтглежданеНаДетеДо2Години, ExpectedTRZ = TRZEventType.ОтпускЗаДетеДо2 },
            new { Scenario = "Leave for Child Adoption Up to 5 Years", EventType = EventType.ОтпускПриОсиновяванеНаДетеДо5ГодишнаВъзраст, ExpectedTRZ = TRZEventType.ОтпускОсиновяванеДо5 },
            new { Scenario = "Unpaid Leave for Child Adoption Up to 5 Years", EventType = EventType.НеплатенОтпускПриОсиновяванеНаДетеДо5ГодишнаВъзраст, ExpectedTRZ = TRZEventType.НеплатенОсиновяванеДо5 },
            new { Scenario = "Paternity Leave for Child Adoption Up to 5 Years", EventType = EventType.ОтпускЗаБащаПриОсиновяванеНаДетеДо5ГодишнаВъзраст, ExpectedTRZ = TRZEventType.БащаОсиновителДо5 },
            new { Scenario = "Leave for Child Care Up to 8 Years by Father", EventType = EventType.ОтпускЗаОтглежданеНаДетеДо8ГодишнаВъзрастОтБащата, ExpectedTRZ = TRZEventType.БащаГледаДетеДо8 }
        };

        foreach (var scenario in businessScenarios)
        {
            // Act
            var isHospital = scenario.EventType.IsHospital();
            var trzEventType = scenario.EventType.ToTRZEventType();
            var mappedBackEventType = trzEventType.ToEventType();

            // Assert
            Assert.True(isHospital, $"Scenario '{scenario.Scenario}' should be identified as hospital-related");
            Assert.Equal(scenario.ExpectedTRZ, trzEventType);
            Assert.Equal(scenario.EventType, mappedBackEventType);
        }
    }

    [Fact]
    public void Hospital_Categories_Should_Not_Conflict_With_Existing_Absence_Types()
    {
        // Arrange - Define existing absence types that should NOT be hospital-related
        var nonHospitalEventTypes = new[]
        {
            EventType.ПлатенГодишенОтпуск, // 101
            EventType.ПлатенГодишенОтпускЗаМиналаГодина, // 102
            EventType.НеплатенСОсигурителенСтажОтОсигурител, // 103
            EventType.ПлатенОбучениеЧл169Ал1, // 201
            EventType.ИнцидентенГражданскиБракЧл157Ал1Т1, // 301
            EventType.ПлатенКомпенсация, // 501
        };

        foreach (var eventType in nonHospitalEventTypes)
        {
            // Act
            var isHospital = eventType.IsHospital();

            // Assert
            Assert.False(isHospital, $"EventType {eventType} should NOT be identified as hospital-related");
        }
    }

    [Fact]
    public void Hospital_Categories_Mapping_Should_Perform_Well_Under_Load()
    {
        // Arrange
        var hospitalEventTypes = new[]
        {
            EventType.Болничен,
            EventType.ГледанеНаБоленЧленОтСемейството,
            EventType.ОтпускЗаМайкаСледРаждане135До410Ден,
            EventType.ОтпускЗаБащаЗаГледанеНаДетеНад6Месеца
        };

        var iterations = 1000;

        // Act & Assert - Performance test
        var startTime = DateTime.UtcNow;

        for (int i = 0; i < iterations; i++)
        {
            foreach (var eventType in hospitalEventTypes)
            {
                var isHospital = eventType.IsHospital();
                var trzEventType = eventType.ToTRZEventType();
                var mappedBackEventType = trzEventType.ToEventType();

                Assert.True(isHospital);
                Assert.Equal(eventType, mappedBackEventType);
            }
        }

        var endTime = DateTime.UtcNow;
        var duration = endTime - startTime;

        // Assert - Should complete within reasonable time (less than 1 second for 1000 iterations)
        Assert.True(duration.TotalSeconds < 1.0,
            $"Hospital categories mapping should complete 1000 iterations in less than 1 second, but took {duration.TotalSeconds:F2} seconds");
    }

    [Fact]
    public void Hospital_Categories_Should_Handle_Edge_Cases_Gracefully()
    {
        // Test with invalid enum values
        var invalidEventType = (EventType)999999;
        var invalidTrzEventType = (TRZEventType)999999;

        // Act & Assert
        Assert.False(invalidEventType.IsHospital());
        Assert.False(invalidTrzEventType.IsHospital());
        Assert.Equal(TRZEventType.ПлатенГодишенОтпуск, invalidEventType.ToTRZEventType());
        Assert.Equal(EventType.ПлатенГодишенОтпуск, invalidTrzEventType.ToEventType());
    }

    [Fact]
    public void Hospital_Categories_Should_Maintain_Data_Integrity_Under_Stress()
    {
        // Arrange
        var hospitalEventTypes = new[]
        {
            EventType.Болничен,
            EventType.ГледанеНаБоленЧленОтСемейството,
            EventType.ОтпускЗаМайкаСледРаждане135До410Ден,
            EventType.ОтпускЗаБащаЗаГледанеНаДетеНад6Месеца
        };

        // Act - Perform multiple rounds of mapping
        for (int round = 0; round < 100; round++)
        {
            foreach (var eventType in hospitalEventTypes)
            {
                // Forward mapping
                var trzEventType = eventType.ToTRZEventType();
                var isHospital = eventType.IsHospital();

                // Backward mapping
                var mappedBackEventType = trzEventType.ToEventType();
                var trzIsHospital = trzEventType.IsHospital();

                // Assert - Data integrity should be maintained
                Assert.True(isHospital);
                Assert.True(trzIsHospital);
                Assert.Equal(eventType, mappedBackEventType);
                Assert.True((int)trzEventType >= 1 && (int)trzEventType <= 26);
            }
        }
    }
}
