﻿using MediatR;
using SSO.Common.DTOs;
using SSO.Common.Requests;
using SSO.Connections.Interfaces;
using SSO.Models;
using SSO.Services.Interfaces;
using System.Text.Json;

namespace SSO.Handlers
{
    public class FacebookLoginHandler : IRequestHandler<FacebookLoginRequest, IResult>
    {
        private readonly IUserRegistrationsConnection _userRegistrationsConnection;
        private readonly IJwtGeneratorService _jwtGeneratorService;
        private readonly IRefreshTokensService _refreshTokensService;
        private readonly IFacebookValidatorConnection _facebookValidatorConnection;

        public FacebookLoginHandler(IUserRegistrationsConnection userRegistrationsConnection,
            IJwtGeneratorService jwtGeneratorService,
            IRefreshTokensService refreshTokensService,
            IFacebookValidatorConnection facebookValidatorConnection)
        {
            _userRegistrationsConnection = userRegistrationsConnection;
            _jwtGeneratorService = jwtGeneratorService;
            _refreshTokensService = refreshTokensService;
            _facebookValidatorConnection = facebookValidatorConnection;
        }

        public async Task<IResult> Handle(FacebookLoginRequest request, CancellationToken cancellationToken)
        {
            if (request.HttpRequest is null)
                return Results.BadRequest();

            var facebookToken = request.HttpRequest.Headers.Authorization;

            var facebookResponse = await _facebookValidatorConnection.ValidateFacebookTokenAsync(facebookToken.ToString());
            if (facebookResponse is null || !facebookResponse.IsSuccessStatusCode)
                return Results.BadRequest();

            var facebookDTO = JsonSerializer.Deserialize<FacebookDTO>(await facebookResponse.Content.ReadAsStringAsync(cancellationToken));
            if (facebookDTO is null)
                return Results.BadRequest();

            var userDTO = new UserDTO
            {
                Email = facebookDTO.Email,
                FirstName = facebookDTO.FirstName,
                LastName = facebookDTO.LastName
            };

            var response = await _userRegistrationsConnection.TryGetUserOrCreateItAsync(userDTO);
            if (response is null)
                return Results.BadRequest();

            var userId = await response.Content.ReadAsStringAsync(cancellationToken);
            if (string.IsNullOrEmpty(userId))
                return Results.BadRequest();

            userDTO.Id = new Guid(userId);
            request.HttpRequest.HttpContext.Response.Headers.Add("Authorization", $"Bearer {await _jwtGeneratorService.GenerateAccessTokenAsync(userDTO)}");

            var newRefreshToken = await _jwtGeneratorService.GenerateAccessTokenAsync(userDTO);
            await _refreshTokensService.AddRefreshTokenAsync(newRefreshToken, userDTO.Id, userDTO.Email ?? throw new ArgumentException("Invalid email address"));
            request.HttpRequest.HttpContext.Response.Headers.Add("Refresh-Token", newRefreshToken);

            return Results.Ok(userDTO);
        }
    }
}
