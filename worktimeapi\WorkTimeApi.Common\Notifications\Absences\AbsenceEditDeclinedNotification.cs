﻿using WorkTimeApi.Common.DTOs.Absences;

namespace WorkTimeApi.Common.Notifications.Absences
{
    public class AbsenceEditDeclinedNotification : BaseNotification<AbsenceHospitalDTO>
    {
        public Guid UserId { get; }

        public AbsenceEditDeclinedNotification(AbsenceHospitalDTO payload, Guid companyId, Guid userId, string creatorName)
            : base(payload, companyId, NotificationsName.Absences.EditedConfirmedAbsenceByEmployeeDeclined.Push, creatorName)
        {
            UserId = userId;

            if (payload != null)
            {
                Url = $"{companyId}/attendance?date={payload.FromDate:MM.yyyy}&absenceId={payload.Id}&fromNotification=true";
            }
        }
    }
}