﻿using Gateway.Common.Globals;
using Gateway.Common.Results;
using Gateway.Connections.Interfaces;
using MediatR;
using Microinvest.TransferFiles.Tools.Models.Users;
using SSO.Common.Requests;
using WorkTimeApi.Common.Requests.Employees;

namespace Gateway.Handlers.SSO
{
    public class ChangePasswordCodeHandler (IWorkTimeApiConnection workTimeApiConnection,IUserRegistrationConnection userRegistrationConnection,
        GlobalUser globalUser) : IRequestHandler<ChangePasswordCodeRequest, IResult>
    {
        public async Task<IResult> Handle(ChangePasswordCodeRequest request, CancellationToken cancellationToken)
        {
            var urChangePasswordResponse = await userRegistrationConnection.ChangePasswordAsync(new UserDTO
            {
                Email = globalUser.Email,
                Password = request.Password,
                RefreshToken = globalUser.RefreshToken
            });

            if (urChangePasswordResponse is null || !urChangePasswordResponse.IsSuccessStatusCode)
                return Results.BadRequest();

            var workTimeResponse = await workTimeApiConnection.UpdateUserHasSignedInAsync(
                new UpdateUserHasSignedInRequest
                {
                    UserId = globalUser.Id,
                    Value = true
                });

            return new HttpResponseMessageResult(workTimeResponse);
        }
    }
}
