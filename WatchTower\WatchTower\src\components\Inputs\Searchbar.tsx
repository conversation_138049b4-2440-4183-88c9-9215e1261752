import { ChangeEvent, MouseEvent, useEffect, useRef, useState } from "react";
import Translator from "../../services/language/Translator";
import styled from "styled-components";
import searchIconHover from "../../assets/images/companiesView/searchIconHover.png";
import searchIcon from "../../assets/images/companiesView/searchIcon.png";
import searchFocusedIcon from "../../assets/images/companiesView/searchFocusedIcon.png";
interface SearchbarProps
  extends React.DetailedHTMLProps<
    React.AllHTMLAttributes<HTMLDivElement>,
    HTMLDivElement
  > {
  label: string;
  placeholder: string;
  value?: string | number;
  handleChange: (e: ChangeEvent<HTMLInputElement>) => void;
  onKeyDown?: React.KeyboardEventHandler<HTMLInputElement>;
  validation?: {
    isValid: boolean;
    alertMessage: string;
  };
}

const Container = styled.div`
  position: relative;
  height: 3.5rem;
  background-color: var(--searchbar-backround);
  border-radius: 2rem;
  display: block;
  text-align: start;
  margin: 1rem;
  left: 1rem;
`;

const SearchButton = styled.button<{
  isFocused: boolean;
}>`
  position: relative;
  width: 2rem;
  height: 2rem;
  border: none;
  background-color: transparent;
  display: block;
  margin: 0.8rem;
  top: -3rem;
  background-size: cover;
  transition: background-image 0.3s ease-in-out;

  ${({ isFocused }) =>
    !isFocused
      ? `
    background-image: url(${searchIcon});
  `
      : `
    background-image: url(${searchFocusedIcon});
  `}

  &:hover {
    background-image: url(${searchIconHover});
  }
`;

const InputField = styled.input<{
  isFocused: boolean;
}>`
  position: relative;
  border: 0px solid var(--textbox-border-color);
  border-radius: 1.8rem;
  font-size: 1rem;
  padding: 0.1rem 1.25rem 0.1rem 2.15rem;
  background: var(--textbox-color);
  margin: 0.2rem;
  outline: none;
  height: 2.4rem;
  top: 0.25rem;
  left: 0.25rem;
  opacity: ${({ isFocused }) => (isFocused ? "1" : "0")};
  width: ${({ isFocused }) => (isFocused ? "62%" : "50%")};
  transition: width 0.3s ease-in-out, opacity 0.3s ease-in-out;
`;

const Searchbar = (props: SearchbarProps) => {
  const searchInputRef = useRef<HTMLInputElement>(null);
  const [isFocused, setIsFocused] = useState(false);
  const [isButtonFocused, setIsButtonFocused] = useState(false);
  const handleFocus = () => {
    setIsFocused(true);
    setIsButtonFocused(true);
  };

  const handleBlur = () => {
    if (value?.toString().trim() === "") {
      setIsFocused(false);
      setIsButtonFocused(false);
    }
  };

  const handleButtonClick = (e: MouseEvent<HTMLButtonElement>) => {
    if (value?.toString().trim() === "") {
      setIsFocused(!isFocused);
    }

    if (!isFocused && searchInputRef.current) {
      searchInputRef.current.focus();
    }
  };

  const { label, value, type, handleChange, onKeyDown, validation, name } =
    props;

  return (
    <Container>
      <InputField
        ref={searchInputRef}
        onFocus={handleFocus}
        type={type}
        value={value}
        onChange={handleChange}
        onBlur={handleBlur}
        onKeyDown={onKeyDown}
        name={name}
        isFocused={isFocused}
      ></InputField>
      <SearchButton isFocused={isButtonFocused} onClick={handleButtonClick} />
      {validation?.isValid === false && (
        <div>
          <Translator getString={validation.alertMessage} />
        </div>
      )}
    </Container>
  );
};

export default Searchbar;
