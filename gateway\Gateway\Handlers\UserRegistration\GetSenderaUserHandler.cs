﻿using Gateway.Common.Globals;
using Gateway.Connections.Interfaces;
using Gateway.Requests.UserRegistration;
using MediatR;
using Microinvest.TransferFiles.Tools.Models.Users;

namespace Gateway.Handlers.UserRegistration
{
    public class GetSenderaUserHandler : IRequestHandler<GetSenderaUserRequest, IResult>
    {
        private readonly IUserRegistrationConnection _userRegistrationConnection;
        private readonly GlobalUser _globalUser;

        public GetSenderaUserHandler(IUserRegistrationConnection userRegistrationConnection,
            GlobalUser globalUser)
        {
            _userRegistrationConnection = userRegistrationConnection;
            _globalUser = globalUser;
        }

        public async Task<IResult> Handle(GetSenderaUserRequest request, CancellationToken cancellationToken)
        {
            var user = await _userRegistrationConnection.GetSenderaUserByIdAsync(_globalUser.Id.ToString());
            if (!user.IsSuccessStatusCode || user.StatusCode == System.Net.HttpStatusCode.NoContent)
                return Results.StatusCode((int)user.StatusCode);

            var userData = await user.Content.ReadFromJsonAsync<UserDTO>();

            if (userData is null)
                return Results.NoContent();

            return Results.Ok(userData);
        }
    }
}
