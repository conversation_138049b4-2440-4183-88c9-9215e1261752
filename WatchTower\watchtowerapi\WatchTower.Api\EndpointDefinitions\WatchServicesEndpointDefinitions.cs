﻿using Gateway.Common.Extenstions.EndpointExtensions;
using Gateway.Common.Extenstions.MediatorExtensions;
using WatchTower.Api.Hubs;
using WatchTower.Api.Notifiers;
using WatchTower.Api.Notifiers.HealthChecks;
using WatchTower.Api.Notifiers.Interfaces;
using WatchTower.Api.Services;
using WatchTower.Api.Services.Interfaces;
using WatchTower.Api.Services.Interfaces.Notifiers;
using WatchTower.Api.Services.Notifiers;
using WatchTower.Api.Workers;
using WatchTower.Api.Workers.Custom;
using WatchTower.Common.Requests;
using WatchTower.Database.Repositorise;
using WatchTower.Database.Repositorise.Interfaces;
using WatchTower.Services;
using WatchTower.Services.Interfaces;

namespace WatchTower.EndpointDefinitions
{
    public class WatchServicesEndpointDefinitions : IEndpointDefinition
    {
        public void DefineEndpoints(WebApplication app)
            => app
             .MediateGet<GetAllWatchHealthCheckRequest>("/watch/health")
             .MediatePost<AddWatchHealthCheckRequest>("/watch/health")
             .MediatePut<UpdateWatchHealthCheckRequest>("/watch/health")
             .MediateGet<GetAllHealthChecksRequest>("/health-check")
             .MapHub<HealthCheckHub>("/hubs/health-check");

        public void DefineServices(IServiceCollection services)
            => services
                .AddTransient<IWatchHealthCheckRepository, WatchHealthCheckRepository>()
                .AddTransient<IWatchHealthCheckService, WatchHealthCheckService>()
                .AddTransient<IHealthCheckRepository, HealthCheckRepository>()
                .AddTransient<INotifier, SignalRNotifier>()
                .AddSingleton<INotifier, DiscordNotifier>()
                .AddSingleton<INotify, Notify>()
                .AddTransient<IHealthCheckService, HealthCheckService>()
                .AddTransient<IAppPoolRecycler, AppPoolRecycler>()
                //.AddHostedService<HealthChecksWorker>()
                .AddHostedService<UpdaterServiceChecker>();
    }
}
