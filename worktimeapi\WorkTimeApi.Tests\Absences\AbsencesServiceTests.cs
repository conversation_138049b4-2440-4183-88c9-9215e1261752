﻿using AutoMapper;
using Gateway.Common.Globals;
using Gateway.Common.Results;
using Moq;
using WorkTimeApi.Common.DTOs.Absences;
using WorkTimeApi.Common.Enums;
using WorkTimeApi.Common.EnumUtilities.Validation;
using WorkTimeApi.Common.Requests.Absences;
using WorkTimeApi.Database.Models;
using WorkTimeApi.Database.Repositories.Interfaces.Absences;
using WorkTimeApi.Database.Services.Interfaces;
using WorkTimeApi.Mappers;
using WorkTimeApi.Services.Absences;
using WorkTimeApi.Validators.Interfaces;

namespace WorkTimeApi.Tests.Absences;

public class AbsencesServiceTests
{
    private readonly Mock<IAbsencesRepository> _absencesRepositoryMock;
    private readonly Mock<IMapper> _mapperMock;
    private readonly Mock<IAbsencesValidator> _absencesValidatorMock;
    private readonly Mock<ICalendarService> _calendarServiceMock;
    private readonly Mock<GlobalUser> _globalUserMock = new();
    private readonly Mock<GlobalEmployee> _globalEmployeeMock = new();
    private readonly AbsencesService _absencesService;

    public AbsencesServiceTests()
    {
        _absencesRepositoryMock = new Mock<IAbsencesRepository>();
        _mapperMock = new Mock<IMapper>();
        _absencesValidatorMock = new Mock<IAbsencesValidator>();
        _calendarServiceMock = new Mock<ICalendarService>();

        _absencesService = new AbsencesService(
            _mapperMock.Object,
            _absencesRepositoryMock.Object,
            _calendarServiceMock.Object,
            _globalUserMock.Object,
            _globalEmployeeMock.Object,
            _absencesValidatorMock.Object);
    }

    private static IMapper CreateMapper()
    {
        var config = new MapperConfiguration(cfg => cfg.AddProfile<MapperProfile>());
        return config.CreateMapper();
    }

    [Fact]
    public async Task MarkAbsencesAndHospitalsExportedAsync_ReturnsMappedDtos_ForMatchingCompany()
    {
        var mapper = CreateMapper();

        var repo = new Mock<IAbsencesRepository>();
        var calendar = new Mock<ICalendarService>();
        var validator = new Mock<IAbsencesValidator>();

        var companyId = Guid.NewGuid();

        var absence1 = new Absence { Id = Guid.NewGuid(), PayrollId = Guid.NewGuid(), FromDate = DateTime.Today, ToDate = DateTime.Today.AddDays(1), Status = AbsenceStatus.Approved, ExportStatus = AbsenceExportStatus.Exported };
        var hospital1 = new Hospital { Id = Guid.NewGuid(), PayrollId = Guid.NewGuid(), FromDate = DateTime.Today, ToDate = DateTime.Today.AddDays(1), Status = AbsenceStatus.Approved, ExportStatus = AbsenceExportStatus.Exported, HospitalType = EventType.Болничен };

        repo.Setup(r => r.MarkExportedAsync<Absence>(companyId, It.IsAny<IEnumerable<Guid>>()))
            .ReturnsAsync(new List<Absence> { absence1 });
        repo.Setup(r => r.MarkExportedAsync<Hospital>(companyId, It.IsAny<IEnumerable<Guid>>()))
            .ReturnsAsync(new List<Hospital> { hospital1 });

        var service = new AbsencesService(mapper, repo.Object, calendar.Object, new GlobalUser { Email = "e", RefreshToken = "r" }, new GlobalEmployee(), validator.Object);

        var result = await service.MarkAbsencesAndHospitalsExportedAsync(companyId, new[] { absence1.Id }, new[] { hospital1.Id });
        var list = result.ToList();

        Assert.Equal(2, list.Count);
        Assert.Contains(list, a => a.Id == absence1.Id && a.ExportStatus == AbsenceExportStatus.Exported && a.IsHospital == false);
        Assert.Contains(list, a => a.Id == hospital1.Id && a.ExportStatus == AbsenceExportStatus.Exported && a.IsHospital == true);
    }

    [Fact]
    public async Task MarkAbsencesAndHospitalsExportedAsync_IgnoresNonMatchingCompanyIds()
    {
        var mapper = CreateMapper();

        var repo = new Mock<IAbsencesRepository>();
        var calendar = new Mock<ICalendarService>();
        var validator = new Mock<IAbsencesValidator>();

        var companyId = Guid.NewGuid();

        repo.Setup(r => r.MarkExportedAsync<Absence>(companyId, It.IsAny<IEnumerable<Guid>>()))
            .ReturnsAsync(new List<Absence>());
        repo.Setup(r => r.MarkExportedAsync<Hospital>(companyId, It.IsAny<IEnumerable<Guid>>()))
            .ReturnsAsync(new List<Hospital>());

        var service = new AbsencesService(mapper, repo.Object, calendar.Object, new GlobalUser { Email = "e", RefreshToken = "r" }, new GlobalEmployee(), validator.Object);

        var result = await service.MarkAbsencesAndHospitalsExportedAsync(companyId, new[] { Guid.NewGuid() }, new[] { Guid.NewGuid() });
        Assert.Empty(result);
    }

    [Fact]
    public async Task EditAbsenceAsync_NonHospitalType_WithActiveAbsenceValidationWarning_SetsOldAbsenceAndReturnsEarly()
    {
        // Arrange
        var mapper = CreateMapper();
        var repo = new Mock<IAbsencesRepository>();
        var calendar = new Mock<ICalendarService>();
        var validator = new Mock<IAbsencesValidator>();

        var absenceId = Guid.NewGuid();
        var payrollId = Guid.NewGuid();
        var existingAbsence = new Absence
        {
            Id = absenceId,
            PayrollId = payrollId,
            FromDate = DateTime.Today,
            ToDate = DateTime.Today.AddDays(1),
            Status = AbsenceStatus.Pending,
            AbsenceType = EventType.ПлатенГодишенОтпуск,
            Reference = "Old comment"
        };

        var editRequest = new EditAbsenceRequest
        {
            Id = absenceId,
            TypeIdentifier = (int)EventType.ПлатенГодишенОтпуск,
            FromDate = DateTime.Today,
            ToDate = DateTime.Today.AddDays(1),
            Comment = "New comment",
            PayrollId = payrollId,
            Status = AbsenceStatus.Pending,
            IsAdminEdit = false
        };

        var validationResult = new ValidationResult();
        validationResult.AddWarning(AbsenceValidationErrorsEnum.AbsenceAlreadyStarted);

        repo.Setup(r => r.GetByIdAsync<Absence>(absenceId))
            .ReturnsAsync(existingAbsence);
        repo.Setup(r => r.UpdateAbsenceAsync(existingAbsence))
            .ReturnsAsync(existingAbsence);

        validator.Setup(v => v.ValidateActiveAbsence(payrollId, It.IsAny<IEnumerable<EventDTO>>()))
            .Returns(validationResult);

        var service = new AbsencesService(mapper, repo.Object, calendar.Object, new GlobalUser { Email = "e", RefreshToken = "r" }, new GlobalEmployee(), validator.Object);

        // Act
        var result = await service.EditAbsenceAsync(editRequest);

        // Assert
        Assert.False(result.IsError);
        Assert.True(result.IsWarning);
        Assert.NotNull(result.Value);
        Assert.NotNull(result.Value.OldAbsence);
        Assert.NotNull(result.Value.NewAbsence);
        Assert.Equal(absenceId, result.Value.OldAbsence.Id);
        Assert.Equal(AbsenceStatus.Approved, result.Value.OldAbsence.Status);
        Assert.Equal(AbsenceStatus.Approved, result.Value.NewAbsence.Status);

        repo.Verify(r => r.UpdateAbsenceAsync(existingAbsence), Times.Once);
        validator.Verify(v => v.ValidateActiveAbsence(payrollId, It.IsAny<IEnumerable<EventDTO>>()), Times.Once);
    }

    [Fact]
    public async Task EditAbsenceAsync_NonHospitalType_WithActiveAbsenceValidationError_SetsOldAbsenceAndReturnsEarly()
    {
        // Arrange
        var mapper = CreateMapper();
        var repo = new Mock<IAbsencesRepository>();
        var calendar = new Mock<ICalendarService>();
        var validator = new Mock<IAbsencesValidator>();

        var absenceId = Guid.NewGuid();
        var payrollId = Guid.NewGuid();
        var existingAbsence = new Absence
        {
            Id = absenceId,
            PayrollId = payrollId,
            FromDate = DateTime.Today,
            ToDate = DateTime.Today.AddDays(1),
            Status = AbsenceStatus.Pending,
            AbsenceType = EventType.ПлатенГодишенОтпуск,
            Reference = "Old comment"
        };

        var editRequest = new EditAbsenceRequest
        {
            Id = absenceId,
            TypeIdentifier = (int)EventType.ПлатенГодишенОтпуск,
            FromDate = DateTime.Today,
            ToDate = DateTime.Today.AddDays(1),
            Comment = "New comment",
            PayrollId = payrollId,
            Status = AbsenceStatus.Pending,
            IsAdminEdit = false
        };

        var validationResult = new ValidationResult();
        validationResult.AddError(AbsenceValidationErrorsEnum.AbsencePassed);

        repo.Setup(r => r.GetByIdAsync<Absence>(absenceId))
            .ReturnsAsync(existingAbsence);
        repo.Setup(r => r.UpdateAbsenceAsync(existingAbsence))
            .ReturnsAsync(existingAbsence);

        validator.Setup(v => v.ValidateActiveAbsence(payrollId, It.IsAny<IEnumerable<EventDTO>>()))
            .Returns(validationResult);

        var service = new AbsencesService(mapper, repo.Object, calendar.Object, new GlobalUser { Email = "e", RefreshToken = "r" }, new GlobalEmployee(), validator.Object);

        // Act
        var result = await service.EditAbsenceAsync(editRequest);

        // Assert
        Assert.False(result.IsError);
        Assert.False(result.IsWarning);
        Assert.NotNull(result.Value);
        Assert.NotNull(result.Value.OldAbsence);
        Assert.NotNull(result.Value.NewAbsence);
        Assert.Equal(absenceId, result.Value.OldAbsence.Id);
        Assert.Equal(AbsenceStatus.Approved, result.Value.OldAbsence.Status);
        Assert.Equal(AbsenceStatus.Approved, result.Value.NewAbsence.Status);

        repo.Verify(r => r.UpdateAbsenceAsync(existingAbsence), Times.Once);
        validator.Verify(v => v.ValidateActiveAbsence(payrollId, It.IsAny<IEnumerable<EventDTO>>()), Times.Once);
    }

    [Fact]
    public async Task EditAbsenceAsync_NonHospitalType_WithoutValidationIssues_ContinuesWithNormalFlow()
    {
        // Arrange
        var mapper = CreateMapper();
        var repo = new Mock<IAbsencesRepository>();
        var calendar = new Mock<ICalendarService>();
        var validator = new Mock<IAbsencesValidator>();

        var absenceId = Guid.NewGuid();
        var payrollId = Guid.NewGuid();
        var existingAbsence = new Absence
        {
            Id = absenceId,
            PayrollId = payrollId,
            FromDate = DateTime.Today,
            ToDate = DateTime.Today.AddDays(1),
            Status = AbsenceStatus.Approved,
            AbsenceType = EventType.ПлатенГодишенОтпуск,
            Reference = "Old comment"
        };

        var editRequest = new EditAbsenceRequest
        {
            Id = absenceId,
            TypeIdentifier = (int)EventType.ПлатенГодишенОтпуск,
            FromDate = DateTime.Today.AddDays(1),
            ToDate = DateTime.Today.AddDays(2),
            Comment = "New comment",
            PayrollId = payrollId,
            Status = AbsenceStatus.Pending,
            IsAdminEdit = false
        };

        var validationResult = new ValidationResult(); // No warnings or errors

        repo.Setup(r => r.GetByIdAsync<Absence>(absenceId))
            .ReturnsAsync(existingAbsence);
        repo.Setup(r => r.UpdateAbsenceAsync(existingAbsence))
            .ReturnsAsync(existingAbsence);

        validator.Setup(v => v.ValidateActiveAbsence(payrollId, It.IsAny<IEnumerable<EventDTO>>()))
            .Returns(validationResult);
        validator.Setup(v => v.ValidateAbsencesAsync(payrollId, It.IsAny<IEnumerable<EventDTO>>()))
            .ReturnsAsync(validationResult);
        validator.Setup(v => v.ValidateHospitalAsync(payrollId, It.IsAny<IEnumerable<EventDTO>>()))
            .ReturnsAsync(validationResult);

        var service = new AbsencesService(mapper, repo.Object, calendar.Object, new GlobalUser { Email = "e", RefreshToken = "r" }, new GlobalEmployee(), validator.Object);

        // Act
        var result = await service.EditAbsenceAsync(editRequest);

        // Assert
        Assert.False(result.IsError);
        Assert.False(result.IsWarning);
        Assert.NotNull(result.Value);
        Assert.NotNull(result.Value.OldAbsence);
        Assert.NotNull(result.Value.NewAbsence);
        Assert.Equal(absenceId, result.Value.OldAbsence.Id);
        Assert.Equal(AbsenceStatus.EditedByEmployee, result.Value.NewAbsence.Status);

        repo.Verify(r => r.UpdateAbsenceAsync(existingAbsence), Times.Once);
        validator.Verify(v => v.ValidateActiveAbsence(payrollId, It.IsAny<IEnumerable<EventDTO>>()), Times.Once);
        validator.Verify(v => v.ValidateAbsencesAsync(payrollId, It.IsAny<IEnumerable<EventDTO>>()), Times.Once);
        validator.Verify(v => v.ValidateHospitalAsync(payrollId, It.IsAny<IEnumerable<EventDTO>>()), Times.Once);
    }

    [Fact]
    public async Task EditAbsenceAsync_AdminEdit_SkipsActiveAbsenceValidation()
    {
        // Arrange
        var mapper = CreateMapper();
        var repo = new Mock<IAbsencesRepository>();
        var calendar = new Mock<ICalendarService>();
        var validator = new Mock<IAbsencesValidator>();

        var absenceId = Guid.NewGuid();
        var payrollId = Guid.NewGuid();
        var existingAbsence = new Absence
        {
            Id = absenceId,
            PayrollId = payrollId,
            FromDate = DateTime.Today,
            ToDate = DateTime.Today.AddDays(1),
            Status = AbsenceStatus.Approved,
            AbsenceType = EventType.ПлатенГодишенОтпуск,
            Reference = "Old comment"
        };

        var editRequest = new EditAbsenceRequest
        {
            Id = absenceId,
            TypeIdentifier = (int)EventType.ПлатенГодишенОтпуск,
            FromDate = DateTime.Today.AddDays(1),
            ToDate = DateTime.Today.AddDays(2),
            Comment = "New comment",
            PayrollId = payrollId,
            Status = AbsenceStatus.EditedByAdmin,
            IsAdminEdit = true
        };

        var validationResult = new ValidationResult();

        repo.Setup(r => r.GetByIdAsync<Absence>(absenceId))
            .ReturnsAsync(existingAbsence);
        repo.Setup(r => r.UpdateAbsenceAsync(existingAbsence))
            .ReturnsAsync(existingAbsence);

        validator.Setup(v => v.ValidateAbsencesAsync(payrollId, It.IsAny<IEnumerable<EventDTO>>()))
            .ReturnsAsync(validationResult);
        validator.Setup(v => v.ValidateHospitalAsync(payrollId, It.IsAny<IEnumerable<EventDTO>>()))
            .ReturnsAsync(validationResult);

        var service = new AbsencesService(mapper, repo.Object, calendar.Object, new GlobalUser { Email = "e", RefreshToken = "r" }, new GlobalEmployee(), validator.Object);

        // Act
        var result = await service.EditAbsenceAsync(editRequest);

        // Assert
        Assert.False(result.IsError);
        Assert.False(result.IsWarning);
        Assert.NotNull(result.Value);
        Assert.NotNull(result.Value.OldAbsence);
        Assert.NotNull(result.Value.NewAbsence);
        Assert.Equal(absenceId, result.Value.OldAbsence.Id);
        Assert.Equal(AbsenceStatus.EditedByAdmin, result.Value.NewAbsence.Status);

        repo.Verify(r => r.UpdateAbsenceAsync(existingAbsence), Times.Once);
        validator.Verify(v => v.ValidateActiveAbsence(It.IsAny<Guid>(), It.IsAny<IEnumerable<EventDTO>>()), Times.Never);
        validator.Verify(v => v.ValidateAbsencesAsync(payrollId, It.IsAny<IEnumerable<EventDTO>>()), Times.Once);
        validator.Verify(v => v.ValidateHospitalAsync(payrollId, It.IsAny<IEnumerable<EventDTO>>()), Times.Once);
    }

    [Fact]
    public async Task EditAbsenceAsync_HospitalType_WithExistingHospital_SetsOldAbsence()
    {
        // Arrange
        var mapper = CreateMapper();
        var repo = new Mock<IAbsencesRepository>();
        var calendar = new Mock<ICalendarService>();
        var validator = new Mock<IAbsencesValidator>();

        var hospitalId = Guid.NewGuid();
        var payrollId = Guid.NewGuid();
        var existingHospital = new Hospital
        {
            Id = hospitalId,
            PayrollId = payrollId,
            FromDate = DateTime.Today,
            ToDate = DateTime.Today.AddDays(1),
            Status = AbsenceStatus.Approved,
            HospitalType = EventType.Болничен,
            SickNote = "Old sick note"
        };

        var editRequest = new EditAbsenceRequest
        {
            Id = hospitalId,
            TypeIdentifier = (int)EventType.Болничен,
            FromDate = DateTime.Today.AddDays(1),
            ToDate = DateTime.Today.AddDays(2),
            SickNote = "New sick note",
            PayrollId = payrollId,
            Status = AbsenceStatus.Pending,
            IsAdminEdit = false
        };

        var validationResult = new ValidationResult();

        repo.Setup(r => r.GetByIdAsync<Hospital>(hospitalId))
            .ReturnsAsync(existingHospital);
        repo.Setup(r => r.UpdateAbsenceAsync(existingHospital))
            .ReturnsAsync(existingHospital);

        validator.Setup(v => v.ValidateAbsencesAsync(payrollId, It.IsAny<IEnumerable<EventDTO>>()))
            .ReturnsAsync(validationResult);
        validator.Setup(v => v.ValidateHospitalAsync(payrollId, It.IsAny<IEnumerable<EventDTO>>()))
            .ReturnsAsync(validationResult);

        var service = new AbsencesService(mapper, repo.Object, calendar.Object, new GlobalUser { Email = "e", RefreshToken = "r" }, new GlobalEmployee(), validator.Object);

        // Act
        var result = await service.EditAbsenceAsync(editRequest);

        // Assert
        Assert.False(result.IsError);
        Assert.False(result.IsWarning);
        Assert.NotNull(result.Value);
        Assert.NotNull(result.Value.OldAbsence);
        Assert.NotNull(result.Value.NewAbsence);
        Assert.Equal(hospitalId, result.Value.OldAbsence.Id);
        Assert.Equal(hospitalId, result.Value.NewAbsence.Id);
        Assert.Equal(AbsenceStatus.EditedByEmployee, result.Value.NewAbsence.Status);

        repo.Verify(r => r.UpdateAbsenceAsync(existingHospital), Times.Once);
    }

    [Fact]
    public async Task EditAbsenceAsync_NonHospitalType_WithExistingAbsence_SetsOldAbsence()
    {
        // Arrange
        var mapper = CreateMapper();
        var repo = new Mock<IAbsencesRepository>();
        var calendar = new Mock<ICalendarService>();
        var validator = new Mock<IAbsencesValidator>();

        var absenceId = Guid.NewGuid();
        var payrollId = Guid.NewGuid();
        var existingAbsence = new Absence
        {
            Id = absenceId,
            PayrollId = payrollId,
            FromDate = DateTime.Today,
            ToDate = DateTime.Today.AddDays(1),
            Status = AbsenceStatus.Approved,
            AbsenceType = EventType.ПлатенГодишенОтпуск,
            Reference = "Old comment"
        };

        var editRequest = new EditAbsenceRequest
        {
            Id = absenceId,
            TypeIdentifier = (int)EventType.ПлатенГодишенОтпуск,
            FromDate = DateTime.Today.AddDays(1),
            ToDate = DateTime.Today.AddDays(2),
            Comment = "New comment",
            PayrollId = payrollId,
            Status = AbsenceStatus.Pending,
            IsAdminEdit = false
        };

        var validationResult = new ValidationResult();

        repo.Setup(r => r.GetByIdAsync<Absence>(absenceId))
            .ReturnsAsync(existingAbsence);
        repo.Setup(r => r.UpdateAbsenceAsync(existingAbsence))
            .ReturnsAsync(existingAbsence);

        validator.Setup(v => v.ValidateAbsencesAsync(payrollId, It.IsAny<IEnumerable<EventDTO>>()))
            .ReturnsAsync(validationResult);
        validator.Setup(v => v.ValidateHospitalAsync(payrollId, It.IsAny<IEnumerable<EventDTO>>()))
            .ReturnsAsync(validationResult);

        var service = new AbsencesService(mapper, repo.Object, calendar.Object, new GlobalUser { Email = "e", RefreshToken = "r" }, new GlobalEmployee(), validator.Object);

        // Act
        var result = await service.EditAbsenceAsync(editRequest);

        // Assert
        Assert.False(result.IsError);
        Assert.False(result.IsWarning);
        Assert.NotNull(result.Value);
        Assert.NotNull(result.Value.OldAbsence);
        Assert.NotNull(result.Value.NewAbsence);
        Assert.Equal(absenceId, result.Value.OldAbsence.Id);
        Assert.Equal(absenceId, result.Value.NewAbsence.Id);
        Assert.Equal(AbsenceStatus.EditedByEmployee, result.Value.NewAbsence.Status);

        repo.Verify(r => r.UpdateAbsenceAsync(existingAbsence), Times.Once);
    }

    [Fact]
    public async Task EditAbsenceAsync_InvalidAbsenceId_ReturnsValidationError()
    {
        // Arrange
        var mapper = CreateMapper();
        var repo = new Mock<IAbsencesRepository>();
        var calendar = new Mock<ICalendarService>();
        var validator = new Mock<IAbsencesValidator>();

        var absenceId = Guid.NewGuid();
        var editRequest = new EditAbsenceRequest
        {
            Id = absenceId,
            TypeIdentifier = (int)EventType.ПлатенГодишенОтпуск,
            FromDate = DateTime.Today,
            ToDate = DateTime.Today.AddDays(1),
            Comment = "Comment",
            PayrollId = Guid.NewGuid(),
            Status = AbsenceStatus.Pending,
            IsAdminEdit = false
        };

        repo.Setup(r => r.GetByIdAsync<Absence>(absenceId))
            .ReturnsAsync((Absence)null);
        repo.Setup(r => r.GetByIdAsync<Hospital>(absenceId))
            .ReturnsAsync((Hospital)null);

        var service = new AbsencesService(mapper, repo.Object, calendar.Object, new GlobalUser { Email = "e", RefreshToken = "r" }, new GlobalEmployee(), validator.Object);

        // Act
        var result = await service.EditAbsenceAsync(editRequest);

        // Assert
        Assert.True(result.IsError);
        Assert.False(result.IsWarning);
        Assert.True(result.ValidationError.HasValidationErrors);
        Assert.Contains((int)AbsenceValidationErrorsEnum.InvalidAbsence, result.ValidationError.ValidationErrors);
    }

    [Fact]
    public async Task GetCompanyAbsencesForTRZByMonthAsync_ShouldReturnFilteredAbsences()
    {
        // Arrange
        var companyId = Guid.NewGuid();
        var employeeId = Guid.NewGuid();
        var monthDate = new DateTime(2025, 8, 15);

        var mockAbsences = new List<Absence>
        {
            new()
            {
                Id = Guid.NewGuid(),
                FromDate = new DateTime(2025, 8, 10),
                ToDate = new DateTime(2025, 8, 12),
                AbsenceType = EventType.ПлатенГодишенОтпуск,
                Status = AbsenceStatus.Approved,
                ExportStatus = AbsenceExportStatus.Pending,
                Payroll = new Payroll { CompanyId = companyId, Company = null, Employee = null, EmployeeId = employeeId, StructureLevel = null}
            },
            new()
            {
                Id = Guid.NewGuid(),
                FromDate = new DateTime(2025, 8, 20),
                ToDate = new DateTime(2025, 8, 22),
                AbsenceType = EventType.ПлатенКомпенсация, // This should be filtered out
                Status = AbsenceStatus.Approved,
                ExportStatus = AbsenceExportStatus.Pending,
                Payroll = new Payroll { CompanyId = companyId, Company = null, Employee = null, EmployeeId = employeeId, StructureLevel = null}
            },
            new()
            {
                Id = Guid.NewGuid(),
                FromDate = new DateTime(2025, 8, 25),
                ToDate = new DateTime(2025, 8, 27),
                AbsenceType = EventType.Болничен,
                Status = AbsenceStatus.Approved,
                ExportStatus = AbsenceExportStatus.Pending,
                Payroll = new Payroll { CompanyId = companyId, Company = null, Employee = null, EmployeeId = employeeId, StructureLevel = null}
            }
        };

        var expectedEventDtos = new List<EventDTO>
        {
            new() { Id = mockAbsences[0].Id, StartDate = mockAbsences[0].FromDate, EndDate = mockAbsences[0].ToDate },
            new() { Id = mockAbsences[2].Id, StartDate = mockAbsences[2].FromDate, EndDate = mockAbsences[2].ToDate }
        };

        _absencesRepositoryMock.Setup(x => x.GetCompanyAbsencesForTRZAsync<Absence>(companyId, monthDate, false))
            .ReturnsAsync(mockAbsences);

        _mapperMock.Setup(x => x.Map<EventDTO>(It.IsAny<Absence>()))
            .Returns<Absence>(a => new EventDTO
            {
                Id = a.Id,
                StartDate = a.FromDate,
                EndDate = a.ToDate
            });

        // Act
        var result = await _absencesService.GetCompanyAbsencesForTRZByMonthAsync(companyId, monthDate, false);

        // Assert
        Assert.NotNull(result);
        var resultList = result.ToList();
        Assert.Equal(2, resultList.Count); // Only 2 absences should be returned (excluding ПлатенКомпенсация)

        // Verify the correct absences were returned
        Assert.Contains(resultList, r => r.Id == mockAbsences[0].Id);
        Assert.Contains(resultList, r => r.Id == mockAbsences[2].Id);
        Assert.DoesNotContain(resultList, r => r.Id == mockAbsences[1].Id); // ПлатенКомпенсация should be filtered out

        _absencesRepositoryMock.Verify(x => x.GetCompanyAbsencesForTRZAsync<Absence>(companyId, monthDate, false), Times.Once);
    }

    [Fact]
    public async Task GetCompanyAbsencesForTRZByMonthAsync_WithNoAbsences_ShouldReturnEmptyList()
    {
        // Arrange
        var companyId = Guid.NewGuid();
        var monthDate = new DateTime(2025, 8, 15);

        _absencesRepositoryMock.Setup(x => x.GetCompanyAbsencesForTRZAsync<Absence>(companyId, monthDate, false))
            .ReturnsAsync(new List<Absence>());

        // Act
        var result = await _absencesService.GetCompanyAbsencesForTRZByMonthAsync(companyId, monthDate, false);

        // Assert
        Assert.NotNull(result);
        Assert.Empty(result);

        _absencesRepositoryMock.Verify(x => x.GetCompanyAbsencesForTRZAsync<Absence>(companyId, monthDate, false), Times.Once);
    }

    [Fact]
    public async Task GetCompanyAbsencesForTRZByMonthAsync_WithAllPlatenKompenzatsiya_ShouldReturnEmptyList()
    {
        // Arrange
        var companyId = Guid.NewGuid();
        var employeeId = Guid.NewGuid();
        var monthDate = new DateTime(2025, 8, 15);

        var mockAbsences = new List<Absence>
        {
            new()
            {
                Id = Guid.NewGuid(),
                FromDate = new DateTime(2025, 8, 10),
                ToDate = new DateTime(2025, 8, 12),
                AbsenceType = EventType.ПлатенКомпенсация,
                Status = AbsenceStatus.Approved,
                ExportStatus = AbsenceExportStatus.Pending,
                Payroll = new Payroll { CompanyId = companyId, Company = null, Employee = null, EmployeeId = employeeId, StructureLevel = null}
            },
            new()
            {
                Id = Guid.NewGuid(),
                FromDate = new DateTime(2025, 8, 20),
                ToDate = new DateTime(2025, 8, 22),
                AbsenceType = EventType.ПлатенКомпенсация,
                Status = AbsenceStatus.Approved,
                ExportStatus = AbsenceExportStatus.Pending,
                Payroll = new Payroll { CompanyId = companyId, Company = null, Employee = null, EmployeeId = employeeId, StructureLevel = null}
            }
        };

        _absencesRepositoryMock.Setup(x => x.GetCompanyAbsencesForTRZAsync<Absence>(companyId, monthDate, false))
            .ReturnsAsync(mockAbsences);

        // Act
        var result = await _absencesService.GetCompanyAbsencesForTRZByMonthAsync(companyId, monthDate, false);

        // Assert
        Assert.NotNull(result);
        Assert.Empty(result); // All absences are ПлатенКомпенсация, so should be filtered out

        _absencesRepositoryMock.Verify(x => x.GetCompanyAbsencesForTRZAsync<Absence>(companyId, monthDate, false), Times.Once);
    }

    [Fact]
    public async Task GetCompanyAbsencesForTRZByMonthAsync_WithDifferentMonths_ShouldCallRepositoryWithCorrectDates()
    {
        // Arrange
        var companyId = Guid.NewGuid();
        var testCases = new[]
        {
            new DateTime(2025, 1, 15),  // January
            new DateTime(2025, 6, 15),  // June
            new DateTime(2025, 12, 15), // December
            new DateTime(2024, 8, 15),  // Different year
        };

        foreach (var monthDate in testCases)
        {
            _absencesRepositoryMock.Setup(x => x.GetCompanyAbsencesForTRZAsync<Absence>(companyId, monthDate, false))
                .ReturnsAsync(new List<Absence>());

            // Act
            await _absencesService.GetCompanyAbsencesForTRZByMonthAsync(companyId, monthDate, false);

            // Assert
            _absencesRepositoryMock.Verify(x => x.GetCompanyAbsencesForTRZAsync<Absence>(companyId, monthDate, false), Times.Once);
        }
    }

    [Fact]
    public async Task GetCompanyAbsencesForTRZByMonthAsync_ShouldMapAllAbsencesCorrectly()
    {
        // Arrange
        var companyId = Guid.NewGuid();
        var employeeId = Guid.NewGuid();
        var monthDate = new DateTime(2025, 8, 15);

        var mockAbsences = new List<Absence>
        {
            new()
            {
                Id = Guid.NewGuid(),
                FromDate = new DateTime(2025, 8, 10),
                ToDate = new DateTime(2025, 8, 12),
                AbsenceType = EventType.ПлатенГодишенОтпуск,
                Status = AbsenceStatus.Approved,
                ExportStatus = AbsenceExportStatus.Pending,
                Payroll = new Payroll { CompanyId = companyId, Company = null, Employee = null, EmployeeId = employeeId, StructureLevel = null}
            }
        };

        var expectedEventDto = new EventDTO
        {
            Id = mockAbsences[0].Id,
            StartDate = mockAbsences[0].FromDate,
            EndDate = mockAbsences[0].ToDate
        };

        _absencesRepositoryMock.Setup(x => x.GetCompanyAbsencesForTRZAsync<Absence>(companyId, monthDate, true))
            .ReturnsAsync(mockAbsences);

        _mapperMock.Setup(x => x.Map<EventDTO>(mockAbsences[0]))
            .Returns(expectedEventDto);

        // Act
        var result = await _absencesService.GetCompanyAbsencesForTRZByMonthAsync(companyId, monthDate, true);

        // Assert
        Assert.NotNull(result);
        var resultList = result.ToList();
        Assert.Single(resultList);
        Assert.Equal(expectedEventDto, resultList[0]);

        _mapperMock.Verify(x => x.Map<EventDTO>(mockAbsences[0]), Times.Once);
    }

    [Fact]
    public async Task GetCompanyAbsencesForTRZByMonthAsync_WithIncludeExportedTrue_ShouldIncludeExportedAbsences()
    {
        // Arrange
        var companyId = Guid.NewGuid();
        var employeeId = Guid.NewGuid();
        var monthDate = new DateTime(2025, 8, 15);

        var mockAbsences = new List<Absence>
        {
            new()
            {
                Id = Guid.NewGuid(),
                FromDate = new DateTime(2025, 8, 10),
                ToDate = new DateTime(2025, 8, 12),
                AbsenceType = EventType.ПлатенГодишенОтпуск,
                Status = AbsenceStatus.Approved,
                ExportStatus = AbsenceExportStatus.Pending,
                Payroll = new Payroll { CompanyId = companyId, Company = null, Employee = null, EmployeeId = employeeId, StructureLevel = null}
            },
            new()
            {
                Id = Guid.NewGuid(),
                FromDate = new DateTime(2025, 8, 20),
                ToDate = new DateTime(2025, 8, 22),
                AbsenceType = EventType.ПлатенГодишенОтпуск,
                Status = AbsenceStatus.Approved,
                ExportStatus = AbsenceExportStatus.Exported, // Should be included when includeExported is true
                Payroll = new Payroll { CompanyId = companyId, Company = null, Employee = null, EmployeeId = employeeId, StructureLevel = null}
            },
            new()
            {
                Id = Guid.NewGuid(),
                FromDate = new DateTime(2025, 8, 25),
                ToDate = new DateTime(2025, 8, 27),
                AbsenceType = EventType.Болничен,
                Status = AbsenceStatus.Approved,
                ExportStatus = AbsenceExportStatus.Pending,
                Payroll = new Payroll { CompanyId = companyId, Company = null, Employee = null, EmployeeId = employeeId, StructureLevel = null}
            }
        };

        _absencesRepositoryMock.Setup(x => x.GetCompanyAbsencesForTRZAsync<Absence>(companyId, monthDate, true))
            .ReturnsAsync(mockAbsences);

        _mapperMock.Setup(x => x.Map<EventDTO>(It.IsAny<Absence>()))
            .Returns<Absence>(a => new EventDTO
            {
                Id = a.Id,
                StartDate = a.FromDate,
                EndDate = a.ToDate
            });

        // Act
        var result = await _absencesService.GetCompanyAbsencesForTRZByMonthAsync(companyId, monthDate, true);

        // Assert
        Assert.NotNull(result);
        var resultList = result.ToList();
        Assert.Equal(3, resultList.Count); // All 3 absences should be returned when includeExported is true

        // Verify all absences were returned
        Assert.Contains(resultList, r => r.Id == mockAbsences[0].Id);
        Assert.Contains(resultList, r => r.Id == mockAbsences[1].Id); // Exported absence should be included
        Assert.Contains(resultList, r => r.Id == mockAbsences[2].Id);
    }

    [Fact]
    public async Task GetCompanyAbsencesForTRZByMonthAsync_WithIncludeExportedTrue_ShouldFilterOutPlatenKompensatsiya()
    {
        // Arrange
        var companyId = Guid.NewGuid();
        var employeeId = Guid.NewGuid();
        var monthDate = new DateTime(2025, 8, 15);

        var mockAbsences = new List<Absence>
        {
            new()
            {
                Id = Guid.NewGuid(),
                FromDate = new DateTime(2025, 8, 10),
                ToDate = new DateTime(2025, 8, 12),
                AbsenceType = EventType.ПлатенГодишенОтпуск,
                Status = AbsenceStatus.Approved,
                ExportStatus = AbsenceExportStatus.Exported,
                Payroll = new Payroll { CompanyId = companyId, Company = null, Employee = null, EmployeeId = employeeId, StructureLevel = null}
            },
            new()
            {
                Id = Guid.NewGuid(),
                FromDate = new DateTime(2025, 8, 20),
                ToDate = new DateTime(2025, 8, 22),
                AbsenceType = EventType.ПлатенКомпенсация, // Should be filtered out regardless of includeExported
                Status = AbsenceStatus.Approved,
                ExportStatus = AbsenceExportStatus.Exported,
                Payroll = new Payroll { CompanyId = companyId, Company = null, Employee = null, EmployeeId = employeeId, StructureLevel = null}
            },
            new()
            {
                Id = Guid.NewGuid(),
                FromDate = new DateTime(2025, 8, 25),
                ToDate = new DateTime(2025, 8, 27),
                AbsenceType = EventType.Болничен,
                Status = AbsenceStatus.Approved,
                ExportStatus = AbsenceExportStatus.Pending,
                Payroll = new Payroll { CompanyId = companyId, Company = null, Employee = null, EmployeeId = employeeId, StructureLevel = null}
            }
        };

        _absencesRepositoryMock.Setup(x => x.GetCompanyAbsencesForTRZAsync<Absence>(companyId, monthDate, true))
            .ReturnsAsync(mockAbsences);

        _mapperMock.Setup(x => x.Map<EventDTO>(It.IsAny<Absence>()))
            .Returns<Absence>(a => new EventDTO
            {
                Id = a.Id,
                StartDate = a.FromDate,
                EndDate = a.ToDate
            });

        // Act
        var result = await _absencesService.GetCompanyAbsencesForTRZByMonthAsync(companyId, monthDate, true);

        // Assert
        Assert.NotNull(result);
        var resultList = result.ToList();
        Assert.Equal(2, resultList.Count); // Only 2 absences should be returned (excluding ПлатенКомпенсация)

        // Verify the correct absences were returned
        Assert.Contains(resultList, r => r.Id == mockAbsences[0].Id);
        Assert.Contains(resultList, r => r.Id == mockAbsences[2].Id);
        Assert.DoesNotContain(resultList, r => r.Id == mockAbsences[1].Id); // ПлатенКомпенсация should be filtered out

        _absencesRepositoryMock.Verify(x => x.GetCompanyAbsencesForTRZAsync<Absence>(companyId, monthDate, true), Times.Once);
    }

    [Fact]
    public async Task GetCompanyAbsencesForTRZByMonthAsync_WithIncludeExportedTrue_ShouldIncludeTRZExportedAbsences()
    {
        // Arrange
        var companyId = Guid.NewGuid();
        var employeeId = Guid.NewGuid();
        var monthDate = new DateTime(2025, 8, 15);

        var mockAbsences = new List<Absence>
        {
            new()
            {
                Id = Guid.NewGuid(),
                FromDate = new DateTime(2025, 8, 10),
                ToDate = new DateTime(2025, 8, 12),
                AbsenceType = EventType.ПлатенГодишенОтпуск,
                Status = AbsenceStatus.Approved,
                ExportStatus = AbsenceExportStatus.Pending,
                Payroll = new Payroll { CompanyId = companyId, Company = null, Employee = null, EmployeeId = employeeId, StructureLevel = null}
            },
            new()
            {
                Id = Guid.NewGuid(),
                FromDate = new DateTime(2025, 8, 20),
                ToDate = new DateTime(2025, 8, 22),
                AbsenceType = EventType.ПлатенГодишенОтпуск,
                Status = AbsenceStatus.Approved,
                ExportStatus = AbsenceExportStatus.Exported,
                Payroll = new Payroll { CompanyId = companyId, Company = null, Employee = null, EmployeeId = employeeId, StructureLevel = null}
            }
        };

        // Note: Repository level filtering excludes TRZExported regardless of includeExported parameter
        // This test verifies that the service correctly passes the parameter to the repository

        _absencesRepositoryMock.Setup(x => x.GetCompanyAbsencesForTRZAsync<Absence>(companyId, monthDate, true))
            .ReturnsAsync(mockAbsences);

        _mapperMock.Setup(x => x.Map<EventDTO>(It.IsAny<Absence>()))
            .Returns<Absence>(a => new EventDTO
            {
                Id = a.Id,
                StartDate = a.FromDate,
                EndDate = a.ToDate
            });

        // Act
        var result = await _absencesService.GetCompanyAbsencesForTRZByMonthAsync(companyId, monthDate, true);

        // Assert
        Assert.NotNull(result);
        var resultList = result.ToList();
        Assert.Equal(2, resultList.Count);

        // Verify the correct absences were returned
        Assert.Contains(resultList, r => r.Id == mockAbsences[0].Id);
        Assert.Contains(resultList, r => r.Id == mockAbsences[1].Id);

        _absencesRepositoryMock.Verify(x => x.GetCompanyAbsencesForTRZAsync<Absence>(companyId, monthDate, true), Times.Once);
    }

    [Fact]
    public async Task GetCompanyAbsencesForTRZByMonthAsync_WithIncludeExportedTrue_AndMixedAbsenceTypes_ShouldReturnCorrectFiltering()
    {
        // Arrange
        var companyId = Guid.NewGuid();
        var employeeId = Guid.NewGuid();
        var monthDate = new DateTime(2025, 8, 15);

        var mockAbsences = new List<Absence>
        {
            new()
            {
                Id = Guid.NewGuid(),
                FromDate = new DateTime(2025, 8, 5),
                ToDate = new DateTime(2025, 8, 7),
                AbsenceType = EventType.ПлатенГодишенОтпуск, // Should be included
                Status = AbsenceStatus.Approved,
                ExportStatus = AbsenceExportStatus.Pending,
                Payroll = new Payroll { CompanyId = companyId, Company = null, Employee = null, EmployeeId = employeeId, StructureLevel = null}
            },
            new()
            {
                Id = Guid.NewGuid(),
                FromDate = new DateTime(2025, 8, 10),
                ToDate = new DateTime(2025, 8, 12),
                AbsenceType = EventType.НеплатенСОсигурителенСтажОтОсигурен, // Should be included
                Status = AbsenceStatus.Approved,
                ExportStatus = AbsenceExportStatus.Exported,
                Payroll = new Payroll { CompanyId = companyId, Company = null, Employee = null, EmployeeId = employeeId, StructureLevel = null}
            },
            new()
            {
                Id = Guid.NewGuid(),
                FromDate = new DateTime(2025, 8, 15),
                ToDate = new DateTime(2025, 8, 17),
                AbsenceType = EventType.ПлатенКомпенсация, // Should be filtered out
                Status = AbsenceStatus.Approved,
                ExportStatus = AbsenceExportStatus.Exported,
                Payroll = new Payroll { CompanyId = companyId, Company = null, Employee = null, EmployeeId = employeeId, StructureLevel = null}
            },
            new()
            {
                Id = Guid.NewGuid(),
                FromDate = new DateTime(2025, 8, 20),
                ToDate = new DateTime(2025, 8, 22),
                AbsenceType = EventType.Болничен, // Should be included
                Status = AbsenceStatus.Approved,
                ExportStatus = AbsenceExportStatus.Exported,
                Payroll = new Payroll { CompanyId = companyId, Company = null, Employee = null, EmployeeId = employeeId, StructureLevel = null}
            },
            new()
            {
                Id = Guid.NewGuid(),
                FromDate = new DateTime(2025, 8, 25),
                ToDate = new DateTime(2025, 8, 27),
                AbsenceType = EventType.НеплатенЗаБременностИРаждане,
                Status = AbsenceStatus.Approved,
                ExportStatus = AbsenceExportStatus.Pending,
                Payroll = new Payroll { CompanyId = companyId, Company = null, Employee = null, EmployeeId = employeeId, StructureLevel = null}
            }
        };

        _absencesRepositoryMock.Setup(x => x.GetCompanyAbsencesForTRZAsync<Absence>(companyId, monthDate, true))
            .ReturnsAsync(mockAbsences);

        _mapperMock.Setup(x => x.Map<EventDTO>(It.IsAny<Absence>()))
            .Returns<Absence>(a => new EventDTO
            {
                Id = a.Id,
                StartDate = a.FromDate,
                EndDate = a.ToDate
            });

        // Act
        var result = await _absencesService.GetCompanyAbsencesForTRZByMonthAsync(companyId, monthDate, true);

        // Assert
        Assert.NotNull(result);
        var resultList = result.ToList();
        Assert.Equal(4, resultList.Count); // 4 absences should be returned (excluding ПлатенКомпенсация)

        // Verify the correct absences were returned
        Assert.Contains(resultList, r => r.Id == mockAbsences[0].Id); // ПлатенГодишенОтпуск
        Assert.Contains(resultList, r => r.Id == mockAbsences[1].Id); // НеплатенСОсигурителенСтажОтОсигурен
        Assert.Contains(resultList, r => r.Id == mockAbsences[3].Id); // Болничен
        Assert.Contains(resultList, r => r.Id == mockAbsences[4].Id); // НеплатенЗаБременност
        Assert.DoesNotContain(resultList, r => r.Id == mockAbsences[2].Id); // ПлатенКомпенсация should be filtered out

        _absencesRepositoryMock.Verify(x => x.GetCompanyAbsencesForTRZAsync<Absence>(companyId, monthDate, true), Times.Once);
    }

    [Fact]
    public async Task GetCompanyAbsencesForTRZByMonthAsync_WithIncludeExportedTrue_AndNoAbsences_ShouldReturnEmptyList()
    {
        // Arrange
        var companyId = Guid.NewGuid();
        var monthDate = new DateTime(2025, 8, 15);

        _absencesRepositoryMock.Setup(x => x.GetCompanyAbsencesForTRZAsync<Absence>(companyId, monthDate, true))
            .ReturnsAsync(new List<Absence>());

        // Act
        var result = await _absencesService.GetCompanyAbsencesForTRZByMonthAsync(companyId, monthDate, true);

        // Assert
        Assert.NotNull(result);
        Assert.Empty(result);

        _absencesRepositoryMock.Verify(x => x.GetCompanyAbsencesForTRZAsync<Absence>(companyId, monthDate, true), Times.Once);
    }

    [Fact]
    public async Task GetCompanyAbsencesForTRZByMonthAsync_WithIncludeExportedTrue_ShouldMapAllAbsencesCorrectly()
    {
        // Arrange
        var companyId = Guid.NewGuid();
        var employeeId = Guid.NewGuid();
        var monthDate = new DateTime(2025, 8, 15);

        var mockAbsences = new List<Absence>
        {
            new()
            {
                Id = Guid.NewGuid(),
                FromDate = new DateTime(2025, 8, 10),
                ToDate = new DateTime(2025, 8, 12),
                AbsenceType = EventType.ПлатенГодишенОтпуск,
                Status = AbsenceStatus.Approved,
                ExportStatus = AbsenceExportStatus.Exported,
                Payroll = new Payroll { CompanyId = companyId, Company = null, Employee = null, EmployeeId = employeeId, StructureLevel = null}
            },
            new()
            {
                Id = Guid.NewGuid(),
                FromDate = new DateTime(2025, 8, 20),
                ToDate = new DateTime(2025, 8, 22),
                AbsenceType = EventType.Болничен,
                Status = AbsenceStatus.Approved,
                ExportStatus = AbsenceExportStatus.Pending,
                Payroll = new Payroll { CompanyId = companyId, Company = null, Employee = null, EmployeeId = employeeId, StructureLevel = null}
            }
        };

        var expectedEventDtos = mockAbsences.Select(a => new EventDTO
        {
            Id = a.Id,
            StartDate = a.FromDate,
            EndDate = a.ToDate
        }).ToList();

        _absencesRepositoryMock.Setup(x => x.GetCompanyAbsencesForTRZAsync<Absence>(companyId, monthDate, true))
            .ReturnsAsync(mockAbsences);

        _mapperMock.Setup(x => x.Map<EventDTO>(It.IsAny<Absence>()))
            .Returns<Absence>(a => new EventDTO
            {
                Id = a.Id,
                StartDate = a.FromDate,
                EndDate = a.ToDate
            });

        // Act
        var result = await _absencesService.GetCompanyAbsencesForTRZByMonthAsync(companyId, monthDate, true);

        // Assert
        Assert.NotNull(result);
        var resultList = result.ToList();
        Assert.Equal(2, resultList.Count);

        // Verify mapping was called for each absence
        _mapperMock.Verify(x => x.Map<EventDTO>(mockAbsences[0]), Times.Once);
        _mapperMock.Verify(x => x.Map<EventDTO>(mockAbsences[1]), Times.Once);

        // Verify correct mapping results
        for (int i = 0; i < mockAbsences.Count; i++)
        {
            Assert.Contains(resultList, r => r.Id == mockAbsences[i].Id &&
                                            r.StartDate == mockAbsences[i].FromDate &&
                                            r.EndDate == mockAbsences[i].ToDate);
        }

        _absencesRepositoryMock.Verify(x => x.GetCompanyAbsencesForTRZAsync<Absence>(companyId, monthDate, true), Times.Once);
    }

    [Fact]
    public async Task GetCompanyAbsencesForTRZByMonthAsync_WithIncludeExportedTrue_ShouldCallRepositoryWithCorrectParameters()
    {
        // Arrange
        var companyId = Guid.NewGuid();
        var monthDate = new DateTime(2025, 8, 15);

        _absencesRepositoryMock.Setup(x => x.GetCompanyAbsencesForTRZAsync<Absence>(companyId, monthDate, true))
            .ReturnsAsync(new List<Absence>());

        // Act
        await _absencesService.GetCompanyAbsencesForTRZByMonthAsync(companyId, monthDate, true);

        // Assert
        _absencesRepositoryMock.Verify(x => x.GetCompanyAbsencesForTRZAsync<Absence>(
            It.Is<Guid>(id => id == companyId),
            It.Is<DateTime>(date => date == monthDate),
            It.Is<bool>(includeExported => includeExported == true)
        ), Times.Once);
    }

    [Fact]
    public async Task GetCompanyAbsencesForTRZByMonthAsync_CompareIncludeExportedTrueVsFalse_ShouldShowDifference()
    {
        // Arrange
        var companyId = Guid.NewGuid();
        var employeeId = Guid.NewGuid();
        var monthDate = new DateTime(2025, 8, 15);

        var pendingAbsence = new Absence
        {
            Id = Guid.NewGuid(),
            FromDate = new DateTime(2025, 8, 10),
            ToDate = new DateTime(2025, 8, 12),
            AbsenceType = EventType.ПлатенГодишенОтпуск,
            Status = AbsenceStatus.Approved,
            ExportStatus = AbsenceExportStatus.Pending,
            Payroll = new Payroll { CompanyId = companyId, Company = null, Employee = null, EmployeeId = employeeId, StructureLevel = null }
        };

        var exportedAbsence = new Absence
        {
            Id = Guid.NewGuid(),
            FromDate = new DateTime(2025, 8, 20),
            ToDate = new DateTime(2025, 8, 22),
            AbsenceType = EventType.ПлатенГодишенОтпуск,
            Status = AbsenceStatus.Approved,
            ExportStatus = AbsenceExportStatus.Exported,
            Payroll = new Payroll { CompanyId = companyId, Company = null, Employee = null, EmployeeId = employeeId, StructureLevel = null }
        };

        // Setup for includeExported = false (only pending)
        _absencesRepositoryMock.Setup(x => x.GetCompanyAbsencesForTRZAsync<Absence>(companyId, monthDate, false))
            .ReturnsAsync(new List<Absence> { pendingAbsence });

        // Setup for includeExported = true (both pending and exported)
        _absencesRepositoryMock.Setup(x => x.GetCompanyAbsencesForTRZAsync<Absence>(companyId, monthDate, true))
            .ReturnsAsync(new List<Absence> { pendingAbsence, exportedAbsence });

        _mapperMock.Setup(x => x.Map<EventDTO>(It.IsAny<Absence>()))
            .Returns<Absence>(a => new EventDTO
            {
                Id = a.Id,
                StartDate = a.FromDate,
                EndDate = a.ToDate
            });

        // Act
        var resultWithoutExported = await _absencesService.GetCompanyAbsencesForTRZByMonthAsync(companyId, monthDate, false);
        var resultWithExported = await _absencesService.GetCompanyAbsencesForTRZByMonthAsync(companyId, monthDate, true);

        // Assert
        var listWithoutExported = resultWithoutExported.ToList();
        var listWithExported = resultWithExported.ToList();

        Assert.Single(listWithoutExported); // Only pending absence
        Assert.Equal(2, listWithExported.Count); // Both pending and exported absences

        Assert.Contains(listWithoutExported, r => r.Id == pendingAbsence.Id);
        Assert.DoesNotContain(listWithoutExported, r => r.Id == exportedAbsence.Id);

        Assert.Contains(listWithExported, r => r.Id == pendingAbsence.Id);
        Assert.Contains(listWithExported, r => r.Id == exportedAbsence.Id);

        _absencesRepositoryMock.Verify(x => x.GetCompanyAbsencesForTRZAsync<Absence>(companyId, monthDate, false), Times.Once);
        _absencesRepositoryMock.Verify(x => x.GetCompanyAbsencesForTRZAsync<Absence>(companyId, monthDate, true), Times.Once);
    }
}
