﻿using Gateway.Common.Extenstions.EndpointExtensions;
using WorkTimeApi.Common.Requests.Calendar;
using WorkTimeApi.Extensions.MediatorExtensions;
using WorkTimeApi.Services.Calendar;
using WorkTimeApi.Database.Services.Interfaces;

namespace WorkTimeApi.EndpointDefinitions.Calendar
{
    public class HolidaysEndpointDefinitions : IEndpointDefinition
    {
        public void DefineEndpoints(WebApplication app)
        {
            app.AuthenticatedGet<GetHolidaysRequest>("/holidays");
        }

        public void DefineServices(IServiceCollection services)
        {
            services.AddTransient<IHolidayService, HolidayService>();
        }
    }
}
