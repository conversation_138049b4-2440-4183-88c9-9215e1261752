﻿using Gateway.Connections.Interfaces;
using Gateway.Extenstions.RequestExtensions;
using WorkTimeApi.Common.Requests.Employees;
using MediatR;
using Newtonsoft.Json;
using SSO.Common.DTOs;
using SSO.Common.Requests;

namespace Gateway.Handlers.SSO
{
	public class GoogleLoginHandler : IRequestHandler<GoogleLoginRequest, IResult>
	{
		private readonly ISSOConnection _ssoConnection;
		private readonly IWorkTimeApiConnection _workTimeApiConnection;

		public GoogleLoginHandler(ISSOConnection ssoConnection, IWorkTimeApiConnection workTimeApiConnection)
		{
			_ssoConnection = ssoConnection;
			_workTimeApiConnection = workTimeApiConnection;
		}

		public async Task<IResult> Handle(GoogleLoginRequest request, CancellationToken cancellationToken)
		{
			if (request.HttpRequest is null)
				return Results.BadRequest();

			var authorizationHeader = request.HttpRequest.Headers.Authorization;

			if (string.IsNullOrWhiteSpace(authorizationHeader))
				return Results.BadRequest();

			var ssoResponse = await _ssoConnection.GoogleLoginAsync(authorizationHeader!);

			if (!ssoResponse.IsSuccessStatusCode)
				return Results.StatusCode((int)ssoResponse.StatusCode);

			request.AddResponseAuthorizationHeaders(ssoResponse);
			var userDTO = await ssoResponse.Content.ReadFromJsonAsync<UserDTO>(cancellationToken: cancellationToken);

			if (userDTO is null)
				return Results.BadRequest();

			var addEmployeeRequest = JsonConvert.DeserializeObject<AddEmployeeRequest>(
				await ssoResponse.Content.ReadAsStringAsync(cancellationToken));
			await _workTimeApiConnection.AddEmployeeAsync(addEmployeeRequest);

			return Results.Text(userDTO.Email);
		}
	}
}
