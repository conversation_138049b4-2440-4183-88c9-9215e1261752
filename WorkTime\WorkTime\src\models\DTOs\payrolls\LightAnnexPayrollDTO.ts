import { AnnexPayrollDTO } from "./AnnexPayrollDTO";

export interface LightAnnexPayrollDTO {
    mainPayrollId?: number;
    annexPayrollFromDate?: string;
    annexPayrollToDate?: string;
}

export const mapAnnexPayrollToLightAnnexPayroll = (
    annexPayroll: AnnexPayrollDTO
  ): LightAnnexPayrollDTO => {
    
    return {
      mainPayrollId: annexPayroll.mainPayrollId,
      annexPayrollFromDate: annexPayroll.annexPayrollFromDate,
      annexPayrollToDate: annexPayroll.annexPayrollToDate,
    };
  };