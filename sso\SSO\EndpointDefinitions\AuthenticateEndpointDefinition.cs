﻿using Gateway.Common.Extenstions.EndpointExtensions;
using Gateway.Common.Extenstions.MediatorExtensions;
using Gateway.Common.Globals;
using Microsoft.Extensions.DependencyInjection.Extensions;
using SSO.Common.Requests;
using SSO.Connections;
using SSO.Connections.Interfaces;
using SSO.Database.Repositories;
using SSO.Database.Repositories.Interfaces;
using SSO.Extenstions.MediatorExtensions;
using SSO.Services;
using SSO.Services.Interfaces;

namespace SSO.EndpointDefinitions
{
    public class AuthenticateEndpointDefinition : IEndpointDefinition
    {
        public void DefineEndpoints(WebApplication app)
        {
            app.AuthenticatedGet<AuthenticateRequest>("/sso/auth")
                .AuthenticatedPost<LogoutRequest>("/sso/logout")
                .MediateGet<HeaderLoginRequest>("/sso/try-login")
                .MediateGet<GoogleLoginRequest>("/sso/try-google-login")
                .MediateGet<FacebookLoginRequest>("/sso/try-facebook-login")
                .MediateGet<MicrosoftLoginRequest>("/sso/try-microsoft-login")
                .MediatePost<LoginRequest>("/sso/login")
                .MediatePost<ResetPasswordRequest>("/sso/reset-password")
                .MediatePost<ChangeForgottenPasswordRequest>("/sso/change-forgotten-password")
                .AuthenticatedPost<SupportChatPermissionsRequest>("/sso/permissions")
                .AuthenticatedGet<GetPermissionsRequest>("/sso/companies/{companyId}/permissions")
                .MediatePost<ValidateRefreshTokenRequest>("/sso/validate-refresh-token")
                .MediateGet<RecaptchaValidationRequest>("sso/validate-recaptcha-token");
        }

        public void DefineServices(IServiceCollection services)
        {
            services
                .AddTransient<IJWTValidator, JWTValidator>()
                .AddTransient<IRefreshTokensService, RefreshTokensService>()
                .AddTransient<IRefreshTokenRepository, RefreshTokenRepository>()
                .AddTransient<IJwtGeneratorService, JwtGeneratorService>()
                .AddTransient<IPrincipalExtensionsWrapper, PrincipalExtensionsWrapper>()
                .AddTransient<IGoogleService, GoogleService>()
                .AddTransient<IEmailsConnection, EmailsConnection>()
                .AddTransient<IUsersRepository, UsersRepository>()
                .AddTransient<IRolesRepository, RolesRepository>()
                .AddTransient<IUsersService, UsersService>()
                .AddSingleton<GlobalUser>()
                .TryAddSingleton<IHttpContextAccessor, HttpContextAccessor>();

            services.AddHttpClient<IUserRegistrationsConnection, UserRegistrationsConnection>();
            services.AddHttpClient<IGoogleValidatorConnection, GoogleValidatorConnection>();
            services.AddHttpClient<IFacebookValidatorConnection, FacebookValidatorConnection>();
            services.AddHttpClient<IMicrosoftValidatorConnection, MicrosoftValidatorConnection>();
        }
    }
}
