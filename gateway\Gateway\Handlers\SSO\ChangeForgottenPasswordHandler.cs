﻿using Gateway.Connections.Interfaces;
using MediatR;
using SSO.Common.Requests;

namespace Gateway.Handlers.SSO
{
    public class ChangeForgottenPasswordHandler : IRequestHandler<ChangeForgottenPasswordRequest, IResult>
    {
        private readonly ISSOConnection _sSOConnection;

        public ChangeForgottenPasswordHandler(ISSOConnection sSOConnection)
        {
            _sSOConnection = sSOConnection;
        }

        public async Task<IResult> Handle(ChangeForgottenPasswordRequest request, CancellationToken cancellationToken)
        {
            var response = await _sSOConnection.ChangeForgottenPasswordAsync(request);
            return response is not null && response.IsSuccessStatusCode ? Results.Ok() : Results.BadRequest();
        }
    }
}
