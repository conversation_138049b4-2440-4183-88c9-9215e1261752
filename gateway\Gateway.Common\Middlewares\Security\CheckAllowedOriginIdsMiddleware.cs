﻿using Gateway.Common.Models;
using Microsoft.AspNetCore.Http;

namespace Gateway.Common.Middlewares.Security
{
    public class CheckAllowedOriginIdsMiddleware
    {
        private readonly RequestDelegate _next;
        private readonly OriginSettings _originSettings;

        public CheckAllowedOriginIdsMiddleware(RequestDelegate next, OriginSettings originSettings)
        {
            _next = next;
            _originSettings = originSettings;
        }

        public async Task InvokeAsync(HttpContext context)
        {
            if (_originSettings.AllowedEndpoints.Any(path => context.Request.Path.StartsWithSegments(path, StringComparison.OrdinalIgnoreCase)))
            {
                await _next(context);
                return;
            }

            var allowedOriginIds = _originSettings.AllowedOriginIds;

            if (allowedOriginIds.Count == 0)
            {
                await _next(context);
            }
            else if (context.Request.Headers.TryGetValue("Origin-Id", out var originId)
                && allowedOriginIds.Contains(originId.ToString()))
            {
                await _next(context);
            }   
            else
            {
                context.Response.StatusCode = StatusCodes.Status403Forbidden;
                return;
            }
        }
    }
}
