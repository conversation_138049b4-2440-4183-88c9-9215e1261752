pool:
  vmImage: 'windows-latest'
  name: 'Jurassic'


variables:
  solution: '**/*.sln'
  buildPlatform: 'Any CPU'
  buildConfiguration: 'Release'

steps:
- task: UseDotNet@2
  inputs:
    packageType: 'sdk'
    version: '8.x'
    installationPath: $(Agent.ToolsDirectory)/dotnet

- task: NuGetToolInstaller@1

# - task: NuGetCommand@2
#   inputs:
#     restoreSolution: '$(solution)'
#     command: 'restore'
#     feedsToUse: 'config'
#     nugetConfigPath: 'NuGet.config'

- task: MSBuild@1
  inputs:
    solution: '**/*.sln'
    platform: 'Any CPU'
    configuration: 'Release'
    
# За сега нямаме тестове на Gateway-a
# - script: dotnet test --configuration $(buildConfiguration) $(Build.Repository.Name).Tests/$(Build.Repository.Name).Tests.csproj
#   displayName: 'Run .NET Tests'