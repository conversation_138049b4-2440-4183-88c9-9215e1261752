﻿using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace SSO.Database.Models
{
    public class User
    {
        [Key]
        [DatabaseGenerated(DatabaseGeneratedOption.None)]
        public Guid Id { get; set; }

        public required string Email { get; set; }

        public List<RoleUser> UserRoles { get; set; } = new();

        public List<RefreshToken> RefreshTokens { get; set; } = new();
    }
}
