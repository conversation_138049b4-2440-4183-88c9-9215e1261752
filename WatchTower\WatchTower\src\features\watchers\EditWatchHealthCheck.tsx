import React, { useState } from "react";
import { Button, Form } from "react-bootstrap";
import { useAppDispatch } from "../../app/hooks";
import { onUpdateWatchHealthCheck } from "./watchersActions";
import { WatchHealthCheckDTO } from "../../models/DTOs/watchers/WatchHealthCheckDTO";
import { useLocation, useNavigate } from "react-router-dom";
import Translator from "../../services/language/Translator";

interface NavigationState {
  watchHealthCheckDTO: WatchHealthCheckDTO;
}

export const EditWatchHealthCheck = () => {
  const dispatch = useAppDispatch();
  const navigate = useNavigate();

  const { watchHealthCheckDTO } = useLocation().state as NavigationState;

  const [oldEnvironment] = useState(watchHealthCheckDTO.environment);
  const [formData, setFormData] = useState(watchHealthCheckDTO);

  const handleChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const { name, value } = e.target;
    setFormData((prev) => ({ ...prev, [name]: value }));
  };

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();

    dispatch(onUpdateWatchHealthCheck(oldEnvironment, formData));

    navigate("/");
  };

  return (
    <Form onSubmit={handleSubmit}>
      <Form.Label as="h1" className="mb-4 text-center">
        <Translator getString="Edit Health Check Watcher" />
      </Form.Label>

      <Form.Group controlId="name" className="mb-2">
        <Form.Label>
          <Translator getString="Name" />
        </Form.Label>
        <Form.Control
          type="text"
          name="name"
          value={formData.name}
          onChange={handleChange}
          placeholder="Enter name"
          required
        />
      </Form.Group>

      <Form.Group controlId="environment" className="mb-2">
        <Form.Label>
          <Translator getString="Environment" />
        </Form.Label>
        <Form.Control
          type="text"
          name="environment"
          value={formData.environment}
          onChange={handleChange}
          placeholder="Enter environment"
          required
        />
      </Form.Group>

      <Form.Group controlId="url" className="mb-2">
        <Form.Label>
          <Translator getString="URL" />
        </Form.Label>
        <Form.Control
          type="url"
          name="url"
          value={formData.url}
          onChange={handleChange}
          placeholder="Enter URL"
          required
        />
      </Form.Group>

      <Form.Group controlId="pollingIntervalInSeconds" className="mb-4">
        <Form.Label>
          <Translator getString="Polling Interval (in seconds)" />
        </Form.Label>
        <Form.Control
          type="text"
          name="pollingIntervalInSeconds"
          value={formData.pollingIntervalInSeconds}
          onChange={handleChange}
          placeholder="Enter polling interval"
          required
        />
      </Form.Group>

      <Form.Group controlId="failedChecksCountToNotify" className="mb-4">
        <Form.Label>
          <Translator getString="Failed checks before notify" />
        </Form.Label>
        <Form.Control
          type="text"
          name="failedChecksCountToNotify"
          value={formData.failedChecksCountToNotify}
          onChange={handleChange}
          placeholder="Failed checks before notify"
          required
        />
      </Form.Group>

      <Button variant="primary" type="submit" className="w-100">
        <Translator getString="Edit" />
      </Button>
    </Form>
  );
};
