﻿using Gateway.Common.Results;
using Gateway.Connections.Interfaces;
using MediatR;
using WorkTimeApi.Common.Requests.Notifications;

namespace Gateway.Handlers.Notifications
{
    public class AllUserNotificationsHandler : IRequestHandler<GetAllUserNotifications, IResult>
    {
        private readonly IWorkTimeApiConnection _workTimeApiConnection;

        public AllUserNotificationsHandler(IWorkTimeApiConnection workTimeApiConnection)
        {
            _workTimeApiConnection = workTimeApiConnection;
        }

        public async Task<IResult> Handle(GetAllUserNotifications request, CancellationToken cancellationToken)
        {
            var response = await _workTimeApiConnection.LoadAllUserNotificationsAsync(request);
            return new HttpResponseMessageResult(response);
        }
    }
}
