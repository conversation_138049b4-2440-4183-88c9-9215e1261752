﻿using Gateway.Common.Requests.Interfaces;
using MediatR;
using SSO.Filters;

namespace SSO.Extenstions.MediatorExtensions
{
    public static class MediatorExtensions
    {
        public static WebApplication AuthenticatedGet<TRequest>(
            this WebApplication app,
            string template) where TRequest : IHttpRequest
        {
            app.MapGet(template, async (IMediator mediator,
                HttpRequest httpRequest,
                [AsParameters] TRequest request) =>
            {
                request.HttpRequest = httpRequest;
                return await mediator.Send(request);
            }).AddEndpointFilter<SSOFilter>();

            return app;
        }

        public static WebApplication AuthenticatedPost<TRequest>(
            this WebApplication app,
            string template) where TRequest : IHttpRequest
        {
            app.MapPost(template, async (IMediator mediator,
                HttpRequest httpRequest,
                TRequest request) =>
            {
                request.HttpRequest = httpRequest;
                return await mediator.Send(request);
            }).AddEndpointFilter<SSOFilter>();

            return app;
        }
    }
}
