﻿using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.DependencyInjection;
using Moq;
using SSO.Database;
using SSO.Database.Models;
using SSO.Database.Repositories;
using SSO.Database.Repositories.Interfaces;

namespace SSO.Tests.SSO.Database.Repositories
{
    public class RefreshTokenRepositoryTests
    {
        private const string USER_GUID_STR = "4c34a6a8-4b6a-496c-bcc1-5a249d487b91";
        private const string USER_EMAIL = "<EMAIL>";

        private readonly Mock<IDbContextFactory<SSODbContext>> _contextFactoryMock;

        public RefreshTokenRepositoryTests()
        {
            _contextFactoryMock = new Mock<IDbContextFactory<SSODbContext>>();
        }

        [Fact]
        public async Task RefreshTokenIsAddedSuccessfullyToDatabase()
        {
            // Arrange
            var cancellationToken = new CancellationToken();
            var context = new SSODbContext(CreateNewContextOptions());
            var user = new User
            {
                Id = new Guid(USER_GUID_STR),
                Email = USER_EMAIL
            };

            _contextFactoryMock
                .Setup(cf => cf.CreateDbContextAsync(cancellationToken))
                .ReturnsAsync(context);
            IRefreshTokenRepository refreshTokenRepository = new RefreshTokenRepository(_contextFactoryMock.Object);
            var refreshToken = new RefreshToken()
            {
                Id = Guid.NewGuid(),
                IsActive = true,
                Token = "refreshtokencontent",
                User = user
            };

            // Act
            var refreshTokenResponse = await refreshTokenRepository.AddRefreshTokenAsync(refreshToken);

            // Assert
            Assert.NotNull(refreshTokenResponse);
            Assert.NotNull(refreshTokenResponse.Id);
            Assert.Equal(refreshToken.Token, refreshTokenResponse.Token);
            Assert.Equal(refreshToken.IsActive, refreshTokenResponse.IsActive);
        }

        [Fact]
        public async Task GetByTokenReturnsRefreshToken()
        {
            // Arrange
            var user = new User
            {
                Id = new Guid(USER_GUID_STR),
                Email = USER_EMAIL
            };

            var refreshToken = new RefreshToken()
            {
                Id = Guid.NewGuid(),
                IsActive = true,
                Token = "refreshtokencontent",
                User = user
            };
            var context = new SSODbContext(CreateNewContextOptions());
            context.RefreshTokens.Add(refreshToken);
            await context.SaveChangesAsync();

            _contextFactoryMock
                .Setup(cf => cf.CreateDbContextAsync(It.IsAny<CancellationToken>()))
                .ReturnsAsync(context);
            IRefreshTokenRepository refreshTokenRepository = new RefreshTokenRepository(_contextFactoryMock.Object);

            // Act
            var refreshTokenResponse = await refreshTokenRepository.GetByTokenAsync(refreshToken.Token);

            // Assert
            Assert.NotNull(refreshTokenResponse);
            Assert.NotNull(refreshTokenResponse.Id);
            Assert.Equal(refreshToken.Token, refreshTokenResponse.Token);
            Assert.Equal(refreshToken.IsActive, refreshTokenResponse.IsActive);
            Assert.True(context.RefreshTokens.Any());
        }

        [Fact]
        public async Task DeactivateAllTokensByEmailShouldSetIsActiveToFalseForAllTokensWithThisEmail()
        {
            // Arrange
            var user = new User
            {
                Id = new Guid(USER_GUID_STR),
                Email = USER_EMAIL
            };

            var anotherUser = new User
            {
                Id = new Guid("18eda4fa-a06b-46ca-b38f-155bac937944"),
                Email = "<EMAIL>"
            };

            var refreshToken = new RefreshToken()
            {
                IsActive = true,
                Token = "refreshtokencontent",
                User = user
            };
            var refreshToken2 = new RefreshToken()
            {
                IsActive = true,
                Token = "refreshtokencontent2",
                User = user
            };
            var refreshToken3 = new RefreshToken()
            {
                IsActive = true,
                Token = "refreshtokencontent3",
                User = user
            };
            var refreshToken4 = new RefreshToken()
            {
                IsActive = true,
                Token = "refreshtokencontent4",
                User = anotherUser
            };
            var context = new SSODbContext(CreateNewContextOptions());

            context.RefreshTokens.Add(refreshToken);
            context.RefreshTokens.Add(refreshToken2);
            context.RefreshTokens.Add(refreshToken3);
            context.RefreshTokens.Add(refreshToken4);
            await context.SaveChangesAsync();

            _contextFactoryMock
                .Setup(cf => cf.CreateDbContextAsync(It.IsAny<CancellationToken>()))
                .ReturnsAsync(context);
            IRefreshTokenRepository refreshTokenRepository = new RefreshTokenRepository(_contextFactoryMock.Object);
            IUsersRepository userRepository = new UsersRepository(_contextFactoryMock.Object);

            // Act
            await userRepository.DeactivateAllTokensForUserAsync(user);

            // Assert
            Assert.True(context.Users.FirstOrDefault(u => u.Id == user.Id)?.RefreshTokens.All(rt => !rt.IsActive));
            Assert.True(context.Users.FirstOrDefault(u => u.Id == anotherUser.Id)?.RefreshTokens.All(rt => rt.IsActive));
        }

        [Fact]
        public async Task DeactivateTokenShouldDeactivateOnlyThisToken()
        {
            // Arrange
            var user = new User
            {
                Id = new Guid(USER_GUID_STR),
                Email = USER_EMAIL
            };

            var refreshToken = new RefreshToken()
            {
                IsActive = true,
                Token = "refreshtokencontent",
                User = user
            };
            var refreshToken2 = new RefreshToken()
            {
                IsActive = true,
                Token = "refreshtokencontent2",
                User = user
            };
            var refreshToken3 = new RefreshToken()
            {
                IsActive = true,
                Token = "refreshtokencontent3",
                User = user
            };
            var refreshToken4 = new RefreshToken()
            {
                IsActive = true,
                Token = "refreshtokencontent4",
                User = user
            };

            var context = new SSODbContext(CreateNewContextOptions());
            context.RefreshTokens.Add(refreshToken);
            context.RefreshTokens.Add(refreshToken2);
            context.RefreshTokens.Add(refreshToken3);
            context.RefreshTokens.Add(refreshToken4);
            await context.SaveChangesAsync();

            _contextFactoryMock
                .Setup(cf => cf.CreateDbContextAsync(It.IsAny<CancellationToken>()))
                .ReturnsAsync(context);
            IRefreshTokenRepository refreshTokenRepository = new RefreshTokenRepository(_contextFactoryMock.Object);

            // Act
            await refreshTokenRepository.DeactivateTokenAsync(refreshToken.Token);

            // Assert
            Assert.True(context.RefreshTokens.Where(rt => rt.Token == refreshToken.Token).All(rt => !rt.IsActive));
            Assert.True(context.RefreshTokens.Where(rt => rt.Token != refreshToken.Token).All(rt => rt.IsActive));
        }

        [Fact]
        public async Task GetByTokenThrowsExceptionIfTokenDoesNotExist()
        {
            // Arrange
            var user = new User
            {
                Id = new Guid(USER_GUID_STR),
                Email = USER_EMAIL
            };

            var refreshToken = new RefreshToken()
            {
                IsActive = true,
                Token = "refreshtokencontent",
                User = user
            };
            var context = new SSODbContext(CreateNewContextOptions());
            context.RefreshTokens.Add(refreshToken);
            await context.SaveChangesAsync();

            _contextFactoryMock
                .Setup(cf => cf.CreateDbContextAsync(It.IsAny<CancellationToken>()))
                .ReturnsAsync(context);
            IRefreshTokenRepository refreshTokenRepository = new RefreshTokenRepository(_contextFactoryMock.Object);

            // Act
            Func<Task> act = async () => await refreshTokenRepository.GetByTokenAsync("anothertoken");

            // Assert
            await Assert.ThrowsAsync<ArgumentException>(async() => await act());
        }

        private static DbContextOptions<SSODbContext> CreateNewContextOptions()
        {
            var serviceProvider = new ServiceCollection()
                .AddEntityFrameworkInMemoryDatabase()
                .BuildServiceProvider();

            var builder = new DbContextOptionsBuilder<SSODbContext>();
            builder.UseInMemoryDatabase("microinvest-sso")
                   .UseInternalServiceProvider(serviceProvider);

            return builder.Options;
        }
    }
}
