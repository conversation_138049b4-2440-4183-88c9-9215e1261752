﻿using MediatR;
using WorkTime.Notifications.Services.Interfaces;
using WorkTimeApi.Common.Notifications.EditEmployee;
using WorkTimeApi.Database.Repositories.Interfaces.Companies;
using WorkTimeApi.Database.Repositories.Interfaces.Employees;
using WorkTimeApi.Services.Interfaces.Employees;
using WorkTimeApi.Services.Interfaces.Notifications;

namespace WorkTimeApi.Handlers.Notifications.EditEmployee
{
    public class EditEmployeeByAdminNotificationHandler(ISignalRNotificationService signalRNotificationService,
        INotificationsService notificationsService,
        IEmailsNotificationService emailsNotificationService,
        IEmployeesRepository employeesRepository,
        ICompaniesRepository companiesRepository,
        IEmployeesService employeesService,
        IConfiguration configuration) : INotificationHandler<EditEmployeeByAdminNotification>
    {
        public async Task Handle(EditEmployeeByAdminNotification notification, CancellationToken cancellationToken)
        {
            var approver = (await employeesRepository.FindAsync(e => e.UserId == notification.UserId && e.CompanyId == notification.CompanyId))
                .FirstOrDefault() ?? throw new Exception("Невалидно Employee!");

            var employee = await employeesService.GetEmployeeAsync(notification.Payload.WorkTimeId) ?? throw new Exception("Не съществуващ Employee!");

            var addNotifications = await notificationsService.AddNotificationByUserIdAndCompanyIdAsync(employee.WorkTimeId, notification);

            var emails = new List<string>() { employee.Email };

            var company = await companiesRepository.GetByIdAsync(notification.CompanyId) ?? throw new Exception("Невалидна компания!");
            var fullName = string.Join(" ", new[] { employee.FirstName, employee.LastName }
                           .Where(name => !string.IsNullOrWhiteSpace(name)));
            var approverName = string.Join(" ", new[] { approver.FirstName, approver.LastName }
                           .Where(name => !string.IsNullOrWhiteSpace(name)));

            var worktimeUrl = configuration.GetValue<string>("WorkTimeUrl") ?? throw new Exception("Невалидна настройка на WorkTimeUrl в appsettings!");

            var emailRequest = new
            {
                Name = fullName,
                ApproverName = approverName,
                CompanyName = company.Name,
                EditedTabName = "Лични данни",
                Url = $"{worktimeUrl}{notification.Url}"
            };

            var emailNotifications = emailsNotificationService.SendEmailsAsync(
                emails.Where(e => approver.Email == null || e != approver.Email),
                "employees/edited/admin",
                emailRequest);

            await signalRNotificationService.NotifyUser(employee.UserId, addNotifications);

            await Task.WhenAll(emailNotifications);
        }
    }
}