import React from "react";
import styled from "styled-components";
import Translator from "../../services/language/Translator";

const CardContainer = styled.div`
  background-color: var(--auth-button-background-color);
  border-radius: 1.5rem;
  padding: 1.5rem;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  min-width: 15rem;
  box-sizing: border-box;
`;

const StatusText = styled.p`
  margin: 0;
  font-size: 1rem;
  color: var(--profile-department-name-font-color);
  font-weight: 500;
  line-height: 1.4;
`;

const PendingUserEditCard: React.FC = () => {
  return (
    <CardContainer>
      <StatusText>
        <Translator getString="Your edit is pending approval." />
      </StatusText>
    </CardContainer>
  );
};

export default PendingUserEditCard;
