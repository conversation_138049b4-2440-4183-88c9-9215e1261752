﻿using Gateway.Common.Extenstions.EndpointExtensions;
using Gateway.Extenstions.MediatorExtensions;
using WorkTimeApi.Common.Requests.Payrolls;

namespace Gateway.EndpointDefinitions.Payrolls
{
	public class PayrollEnpointDefinitions : IEndpointDefinition
	{
		public void DefineEndpoints(WebApplication app)
		{
			app.AuthenticatedPost<AddPayrollRequest>("/payrolls/add-payroll")
				.AuthenticatedDelete<DeletePayrollRequest>("/payrolls/delete-payroll");
		}

		public void DefineServices(IServiceCollection services)
		{
		}
	}
}
