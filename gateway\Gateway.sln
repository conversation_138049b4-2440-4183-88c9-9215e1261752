﻿
Microsoft Visual Studio Solution File, Format Version 12.00
# Visual Studio Version 17
VisualStudioVersion = 17.3.32929.385
MinimumVisualStudioVersion = 10.0.40219.1
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "Gateway", "Gateway\Gateway.csproj", "{66A784BD-0B8C-4B41-AA3A-FAC0FAA0AC3C}"
EndProject
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "Gateway.Common", "Gateway.Common\Gateway.Common.csproj", "{4E3425C8-2945-437F-BD3C-9F815E854CBA}"
EndProject
Project("{2150E333-8FDC-42A3-9474-1A3956D46DE8}") = "Solution Items", "Solution Items", "{0C7B42D4-4153-4414-856E-6F2B86017216}"
	ProjectSection(SolutionItems) = preProject
		.editorconfig = .editorconfig
		deploy-image-script.ps1 = deploy-image-script.ps1
		docker-compose.yml = docker-compose.yml
		docker-testing-build.yml = docker-testing-build.yml
		Dockerfile = Dockerfile
		dot-net-build-and-tests.yml = dot-net-build-and-tests.yml
		NuGet.Config = NuGet.Config
		gateway-prod-build.yml = gateway-prod-build.yml
		README.md = README.md
	EndProjectSection
EndProject
Global
	GlobalSection(SolutionConfigurationPlatforms) = preSolution
		Debug|Any CPU = Debug|Any CPU
		Docker|Any CPU = Docker|Any CPU
		Release|Any CPU = Release|Any CPU
		Testing|Any CPU = Testing|Any CPU
	EndGlobalSection
	GlobalSection(ProjectConfigurationPlatforms) = postSolution
		{66A784BD-0B8C-4B41-AA3A-FAC0FAA0AC3C}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{66A784BD-0B8C-4B41-AA3A-FAC0FAA0AC3C}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{66A784BD-0B8C-4B41-AA3A-FAC0FAA0AC3C}.Docker|Any CPU.ActiveCfg = Docker|Any CPU
		{66A784BD-0B8C-4B41-AA3A-FAC0FAA0AC3C}.Docker|Any CPU.Build.0 = Docker|Any CPU
		{66A784BD-0B8C-4B41-AA3A-FAC0FAA0AC3C}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{66A784BD-0B8C-4B41-AA3A-FAC0FAA0AC3C}.Release|Any CPU.Build.0 = Release|Any CPU
		{66A784BD-0B8C-4B41-AA3A-FAC0FAA0AC3C}.Testing|Any CPU.ActiveCfg = Testing|Any CPU
		{66A784BD-0B8C-4B41-AA3A-FAC0FAA0AC3C}.Testing|Any CPU.Build.0 = Testing|Any CPU
		{4E3425C8-2945-437F-BD3C-9F815E854CBA}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{4E3425C8-2945-437F-BD3C-9F815E854CBA}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{4E3425C8-2945-437F-BD3C-9F815E854CBA}.Docker|Any CPU.ActiveCfg = Docker|Any CPU
		{4E3425C8-2945-437F-BD3C-9F815E854CBA}.Docker|Any CPU.Build.0 = Docker|Any CPU
		{4E3425C8-2945-437F-BD3C-9F815E854CBA}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{4E3425C8-2945-437F-BD3C-9F815E854CBA}.Release|Any CPU.Build.0 = Release|Any CPU
		{4E3425C8-2945-437F-BD3C-9F815E854CBA}.Testing|Any CPU.ActiveCfg = Testing|Any CPU
		{4E3425C8-2945-437F-BD3C-9F815E854CBA}.Testing|Any CPU.Build.0 = Testing|Any CPU
	EndGlobalSection
	GlobalSection(SolutionProperties) = preSolution
		HideSolutionNode = FALSE
	EndGlobalSection
	GlobalSection(ExtensibilityGlobals) = postSolution
		SolutionGuid = {FEEEAA78-9E83-4577-A5C6-241242E183E6}
	EndGlobalSection
EndGlobal
