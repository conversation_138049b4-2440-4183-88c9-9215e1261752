﻿using Gateway.Common.Results;
using Gateway.Connections.Interfaces;
using MediatR;
using WorkTimeApi.Common.Requests.Employees;

namespace Gateway.Handlers.Employees
{
    public class LoadEmployeesHandler : IRequestHandler<LoadEmployeesRequest, IResult>
    {
        private readonly IWorkTimeApiConnection _workTimeApiConnection;

        public LoadEmployeesHandler(IWorkTimeApiConnection workTimeApiConnection)
        {
            _workTimeApiConnection = workTimeApiConnection;
        }

        public async Task<IResult> Handle(LoadEmployeesRequest request, CancellationToken cancellationToken)
        {
            var response = await _workTimeApiConnection.LoadEmployeesAsync(request);

            return new HttpResponseMessageResult(response);
        }
    }
}
