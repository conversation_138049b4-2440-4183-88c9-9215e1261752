﻿using Gateway.Common.Results;
using Gateway.Connections.Interfaces;
using MediatR;
using System.Net;
using WorkTimeApi.Common;
using WorkTimeApi.Common.Requests.TRZ;
using UserDTO = Microinvest.TransferFiles.Tools.Models.Users.UserDTO;
using WorktimeUserDTO = WorkTimeApi.Common.DTOs.Users.UserDTO;

namespace Gateway.Handlers.TRZ
{
    public class AddTRZEmployeesHandler : IRequestHandler<AddTRZEmployeesRequest, IResult>
    {
        private readonly IWorkTimeApiConnection _workTimeApiConnection;
        private readonly IUserRegistrationConnection _userRegistrationConnection;

        public AddTRZEmployeesHandler(IWorkTimeApiConnection workTimeApiConnection,
            IUserRegistrationConnection userRegistrationConnection)
        {
            _workTimeApiConnection = workTimeApiConnection;
            _userRegistrationConnection = userRegistrationConnection;
        }

        public async Task<IResult> Handle(AddTRZEmployeesRequest request, CancellationToken cancellationToken)
        {
            foreach (var employee in request.TRZPendingEmployees)
            {
                UserDTO? userDTO;
                var worktimeUsers = new List<WorktimeUserDTO>();

                if (employee.WorkTimeId != null && employee.WorkTimeId != Guid.Empty)
                    continue;

                var worktimeUser = new WorktimeUserDTO() { Email = employee?.EMail };

                if (string.IsNullOrEmpty(employee?.EMail))
                {
                    var user = new UserDTO() { FirstName = employee?.Name, Password = employee.EGN };
                    var registrationResponse = await _userRegistrationConnection.RegisterUserByCodeAsync(user);
                    if (registrationResponse is null || !registrationResponse.IsSuccessStatusCode)
                        continue;

                    userDTO = await registrationResponse.Content.ReadFromJsonAsync<UserDTO>();
                    if (userDTO is null)
                        continue;

                    worktimeUser.Code = userDTO.Code;
                    worktimeUser.CodePassword = userDTO.Password;
                }
                else
                {
                    var userResponse = await _userRegistrationConnection.GetSenderaUserAsync(employee.EMail);
                    if (userResponse.IsSuccessStatusCode && userResponse.StatusCode != HttpStatusCode.NoContent)
                    {
                        userDTO = await userResponse.Content.ReadFromJsonAsync<UserDTO>(cancellationToken);
                    }
                    else
                    {
                        var user = new UserDTO() { Email = employee.EMail, FirstName = employee.Name, Password = employee.EGN };
                        var registrationResponse = await _userRegistrationConnection.ConfirmedRegisterEmployeeByEmailAsync(user);
                        if (registrationResponse is null || !registrationResponse.IsSuccessStatusCode)
                            continue;

                        userDTO = await registrationResponse.Content.ReadFromJsonAsync<UserDTO>();
                        if (userDTO is null)
                            continue;

                        worktimeUser.CodePassword = userDTO.Password;
                    }
                }

                worktimeUser.Id = Guid.TryParse(userDTO?.Id, out var id) ? id : Guid.Empty;
                worktimeUser.FirstName = userDTO?.FirstName;
                worktimeUser.SecondName = userDTO?.SecondName;
                worktimeUser.LastName = userDTO?.LastName;
                worktimeUser.WorkTimeRoleId = new Guid(DefaultWorktimeRole.Employee);

                var userWTResponse = await (await _workTimeApiConnection.AddUserAsync(new WorkTimeApi.Common.Requests.Users.AddUserRequest { User = worktimeUser }))
                    .Content.ReadFromJsonAsync<WorktimeUserDTO>();

                employee.UserId = userWTResponse?.Id;
            }

            var addEmployeesResponse = await _workTimeApiConnection.AddTRZEmployeesAsync(request);

            return new HttpResponseMessageResult(addEmployeesResponse);
        }
    }
}
