import { LoginUserDTO } from "../../models/DTOs/LoginUserDTO";
import { RegisterUserDTO } from "../../models/DTOs/RegisterUserDTO";
import { post, getResponse, gatewayPost } from "../connectionService";
import {
  LOCAL_STORAGE_ACCESS_TOKEN,
  LOCAL_STORAGE_HAS_SIGNED_IN,
  LOCAL_STORAGE_REFRESH_TOKEN,
  LOCAL_STORAGE_USER_EMAIL,
} from "../../constants/local-storage-constants";
import { User } from "../../features/authentication/AuthContext";
import { ChangeForgottenPasswordRequest } from "../../models/Requests/ChangeForgottenPasswordRequest";

function getBasicAuthenticationToken(email: string, password: string) {
  return "Basic " + window.btoa(email + ":" + password);
}

export const registration = async (registerDTO: RegisterUserDTO) => {
  return await post(`user-registration/registration`, registerDTO);
};

export const confirmEmail = async (userId: string, code: string) => {
  return await post(`user-registration/confirm-email`, { userId, code });
};

export const logout = () => {
  gatewayPost(`sso/logout`);

  localStorage.removeItem(LOCAL_STORAGE_USER_EMAIL);
  localStorage.removeItem(LOCAL_STORAGE_ACCESS_TOKEN);
  localStorage.removeItem(LOCAL_STORAGE_REFRESH_TOKEN);
  localStorage.removeItem(LOCAL_STORAGE_HAS_SIGNED_IN);
};

export const login = async (
  loginDTO: LoginUserDTO,
  rememberMeChecked: boolean
) => {
  const authToken = getBasicAuthenticationToken(
    loginDTO.email,
    loginDTO.password
  );

  const response = await getResponse(`sso/try-login`, {
    Authorization: authToken,
  });

  if (response.status === 200) {
    localStorage.setItem(LOCAL_STORAGE_USER_EMAIL, loginDTO.email);
    localStorage.setItem(
      LOCAL_STORAGE_ACCESS_TOKEN,
      response.headers.get("Authorization")
    );
    if (rememberMeChecked) {
      localStorage.setItem(
        LOCAL_STORAGE_REFRESH_TOKEN,
        response.headers.get("Refresh-token")
      );
    }
  }

  return response;
};

export const facebookLogin = async (
  token: string
): Promise<string | undefined> => {
  const response = await getResponse(`sso/try-facebook-login`, {
    Authorization: token,
  });

  if (response.status !== 200) return undefined;

  const email = await response.text();

  localStorage.setItem(LOCAL_STORAGE_USER_EMAIL, email);
  localStorage.setItem(
    LOCAL_STORAGE_ACCESS_TOKEN,
    response.headers.get("Authorization")
  );
  localStorage.setItem(
    LOCAL_STORAGE_REFRESH_TOKEN,
    response.headers.get("Refresh-token")
  );

  return email;
};

export const microsoftLogin = async (
  token: string
): Promise<string | undefined> => {
  const response = await getResponse(`sso/try-microsoft-login`, {
    Authorization: token,
  });

  if (response.status !== 200) return undefined;

  const email = await response.text();

  localStorage.setItem(LOCAL_STORAGE_USER_EMAIL, email);
  localStorage.setItem(
    LOCAL_STORAGE_ACCESS_TOKEN,
    response.headers.get("Authorization")
  );
  localStorage.setItem(
    LOCAL_STORAGE_REFRESH_TOKEN,
    response.headers.get("Refresh-token")
  );

  return email;
};

export const googleLogin = async (
  token?: string
): Promise<string | undefined> => {
  const response = await getResponse(`sso/try-google-login`, {
    Authorization: token,
  });

  if (response.status !== 200) return undefined;

  const email = await response.text();
  localStorage.setItem(LOCAL_STORAGE_USER_EMAIL, email);
  localStorage.setItem(
    LOCAL_STORAGE_ACCESS_TOKEN,
    response.headers.get("Authorization")
  );
  localStorage.setItem(
    LOCAL_STORAGE_REFRESH_TOKEN,
    response.headers.get("Refresh-token")
  );

  return email;
};

export const isAuthenticated = () => {
  const accessToken = localStorage.getItem(LOCAL_STORAGE_ACCESS_TOKEN);
  return accessToken !== undefined && accessToken !== null;
};

export const initUser = (): User => {
  const email = localStorage.getItem(LOCAL_STORAGE_USER_EMAIL);
  if (isAuthenticated() && email) {
    const hasSignedIn = localStorage.getItem(LOCAL_STORAGE_HAS_SIGNED_IN);
    if (hasSignedIn) {
      return {
        email: email,
        hasSignedIn: hasSignedIn === "true",
      };
    }

    return {
      email: email,
      hasSignedIn: false,
    };
  }

  return {
    email: undefined,
    hasSignedIn: false,
  };
};

export const changeForgottenPassword = async (
  changeForgottenPassword: ChangeForgottenPasswordRequest
) => {
  return await post<boolean>(
    "sso/change-forgotten-password",
    changeForgottenPassword
  );
};
