import { IEntity } from "../IEntity";
import { NomenclatureDTO } from "../nomenclatures/NomenclatureDTO";

export interface BankAccountDTO extends IEntity {
  id: string;
  employeeId: string;
  iBAN: string;
  purpose: NomenclatureDTO;
}

export enum BankAccountPurpose {
  Salary = 0,
  AdvancePayment = 1,
  Hospital = 3,
  RevenuesAdministeredByNAP = 4,
  DOO = 5,
  NZOK = 6,
  InsuranceContributionsAndPenaltyInterestDZPO = 7,
  ForcedCollectionOfPublicReceivables = 8,
  InsuranceAndTaxes = 9,
  Loan = 10,
  WorkerContribution = 11,
  Constipation = 12,
  Other = 13,
}
