﻿<Project Sdk="Microsoft.NET.Sdk.Web">

  <PropertyGroup>
    <TargetFramework>net8.0</TargetFramework>
    <Nullable>enable</Nullable>
    <ImplicitUsings>enable</ImplicitUsings>
	  <GenerateRuntimeConfigurationFiles>True</GenerateRuntimeConfigurationFiles>
	  <IsTransformWebConfigDisabled>true</IsTransformWebConfigDisabled>
	  <Configurations>Debug;Release;Docker;Testing</Configurations>
  </PropertyGroup>

  <ItemGroup>
    <PackageReference Include="AutoMapper" Version="13.0.1" />
    <PackageReference Include="Microsoft.EntityFrameworkCore.Design" Version="8.0.8">
      <PrivateAssets>all</PrivateAssets>
      <IncludeAssets>runtime; build; native; contentfiles; analyzers; buildtransitive</IncludeAssets>
    </PackageReference>
  </ItemGroup>

    <ItemGroup Condition="'$(Configuration)'=='Debug'">
		<ProjectReference Include="..\..\WatchTower\watchtowerapi\WatchTower.Common\WatchTower.Common.csproj" />
		<ProjectReference Include="..\..\gateway\Gateway\Gateway.csproj" />
	</ItemGroup>
	<ItemGroup Condition="'$(Configuration)'!='Debug'">
		<PackageReference Include="WatchTower.Common" Version="0.0.1-preview.20231123144352" />
		<PackageReference Include="Gateway.Common" Version="0.0.9-preview.20250902144032" />
	</ItemGroup>

	<ItemGroup>
    <ProjectReference Include="..\SSO.Database\SSO.Database.csproj" />
  </ItemGroup>

</Project>
