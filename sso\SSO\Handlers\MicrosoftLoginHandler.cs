﻿using MediatR;
using SSO.Common.DTOs;
using SSO.Common.Requests;
using SSO.Connections.Interfaces;
using SSO.Models;
using SSO.Services.Interfaces;
using System.Text.Json;

namespace SSO.Handlers
{
    public class MicrosoftLoginHandler : IRequestHandler<MicrosoftLoginRequest, IResult>
    {
        private readonly IUserRegistrationsConnection _userRegistrationsConnection;
        private readonly IJwtGeneratorService _jwtGeneratorService;
        private readonly IRefreshTokensService _refreshTokensService;
        private readonly IMicrosoftValidatorConnection _microsoftValidatorConnection;

        public MicrosoftLoginHandler(IUserRegistrationsConnection userRegistrationsConnection,
            IJwtGeneratorService jwtGeneratorService,
            IRefreshTokensService refreshTokensService,
            IMicrosoftValidatorConnection microsoftValidatorConnection)
        {
            _userRegistrationsConnection = userRegistrationsConnection;
            _jwtGeneratorService = jwtGeneratorService;
            _refreshTokensService = refreshTokensService;
            _microsoftValidatorConnection = microsoftValidatorConnection;
        }

        public async Task<IResult> Handle(MicrosoftLoginRequest request, CancellationToken cancellationToken)
        {
            if (request.HttpRequest is null)
                return Results.BadRequest();

            var microsoftToken = request.HttpRequest.Headers.Authorization.ToString().Split(" ").LastOrDefault();
            if (microsoftToken is null)
                return Results.BadRequest();

            var microsoftResponse = await _microsoftValidatorConnection.ValidateMicrosoftTokenAsync(microsoftToken.ToString());
            if (microsoftResponse is null || !microsoftResponse.IsSuccessStatusCode)
                return Results.BadRequest();

            var content = await microsoftResponse.Content.ReadAsStringAsync(cancellationToken);
            var microsoftDTO = JsonSerializer.Deserialize<MicrosoftDTO>(content);
            if (microsoftDTO is null)
                return Results.BadRequest();

            var userDTO = new UserDTO
            {
                Email = microsoftDTO.Mail ?? "",
                FirstName = microsoftDTO.GivenName,
                LastName = microsoftDTO.Surname
            };

            var response = await _userRegistrationsConnection.TryGetUserOrCreateItAsync(userDTO);
            if (response is null)
                return Results.BadRequest();

            var userId = await response.Content.ReadAsStringAsync(cancellationToken);
            if (string.IsNullOrEmpty(userId))
                return Results.BadRequest();

            userDTO.Id = new Guid(userId);
            request.HttpRequest.HttpContext.Response.Headers.Add("Authorization", $"Bearer {await _jwtGeneratorService.GenerateAccessTokenAsync(userDTO)}");

            var newRefreshToken = await _jwtGeneratorService.GenerateAccessTokenAsync(userDTO);
            await _refreshTokensService.AddRefreshTokenAsync(newRefreshToken, userDTO.Id, userDTO.Email ?? throw new ArgumentException("Invalid email address"));
            request.HttpRequest.HttpContext.Response.Headers.Add("Refresh-Token", newRefreshToken);

            return Results.Ok(userDTO);
        }
    }
}
