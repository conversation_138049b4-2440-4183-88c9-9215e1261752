import React, { useState } from "react";
import { Button, Form } from "react-bootstrap";
import { useAppDispatch } from "../../app/hooks";
import { onAddWatchHealthCheck } from "./watchersActions";
import { WatchHealthCheckDTO } from "../../models/DTOs/watchers/WatchHealthCheckDTO";
import { useNavigate } from "react-router-dom";
import Translator from "../../services/language/Translator";

export const CreateWatchHealthCheck = () => {
  const dispatch = useAppDispatch();
  const navigate = useNavigate();

  const [formData, setFormData] = useState({
    name: "",
    environment: "",
    url: "",
    pollingIntervalInSeconds: 5,
    failedChecksCountToNotify: 5,
  } as WatchHealthCheckDTO);

  const handleChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const { name, value } = e.target;
    setFormData((prev) => ({ ...prev, [name]: value }));
  };

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();

    dispatch(onAddWatchHealthCheck(formData));

    navigate("/");
  };

  return (
    <Form onSubmit={handleSubmit}>
      <Form.Label as="h1" className="mb-4 text-center">
        <Translator getString="Create Health Check Watcher" />
      </Form.Label>

      <Form.Group controlId="name" className="mb-2">
        <Form.Label>
          <Translator getString="Name" />
        </Form.Label>
        <Form.Control
          type="text"
          name="name"
          value={formData.name}
          onChange={handleChange}
          placeholder="Enter name"
          required
        />
      </Form.Group>

      <Form.Group controlId="environment" className="mb-2">
        <Form.Label>
          <Translator getString="Environment" />
        </Form.Label>
        <Form.Control
          type="text"
          name="environment"
          value={formData.environment}
          onChange={handleChange}
          placeholder="Enter environment"
          required
        />
      </Form.Group>

      <Form.Group controlId="url" className="mb-2">
        <Form.Label>
          <Translator getString="URL" />
        </Form.Label>
        <Form.Control
          type="url"
          name="url"
          value={formData.url}
          onChange={handleChange}
          placeholder="Enter URL"
          required
        />
      </Form.Group>

      <Form.Group controlId="pollingIntervalInSeconds" className="mb-4">
        <Form.Label>
          <Translator getString="Polling Interval (in seconds)" />
        </Form.Label>
        <Form.Control
          type="text"
          name="pollingIntervalInSeconds"
          value={formData.pollingIntervalInSeconds}
          onChange={handleChange}
          placeholder="Enter polling interval"
          required
        />
      </Form.Group>

      <Form.Group controlId="failedChecksCountToNotify" className="mb-4">
        <Form.Label>
          <Translator getString="Failed checks before notify" />
        </Form.Label>
        <Form.Control
          type="text"
          name="failedChecksCountToNotify"
          value={formData.failedChecksCountToNotify}
          onChange={handleChange}
          placeholder="Failed checks before notify"
          required
        />
      </Form.Group>

      <Button variant="primary" type="submit" className="w-100">
        <Translator getString="Create" />
      </Button>
    </Form>
  );
};
