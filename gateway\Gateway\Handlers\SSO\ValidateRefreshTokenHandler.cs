﻿using Gateway.Common.Results;
using Gateway.Connections.Interfaces;
using MediatR;
using SSO.Common.Requests;

namespace Gateway.Handlers.SSO
{
    public class ValidateRefreshTokenHandler : IRequestHandler<ValidateRefreshTokenRequest, IResult>
    {
        private readonly ISSOConnection _ssoConnection;

        public ValidateRefreshTokenHandler(ISSOConnection ssoConnection)
        {
            _ssoConnection = ssoConnection;
        }

        public async Task<IResult> Handle(ValidateRefreshTokenRequest request, CancellationToken cancellationToken)
        {
            return new HttpResponseMessageResult(await _ssoConnection.ValidateRefreshToken(request));
        }
    }
}
