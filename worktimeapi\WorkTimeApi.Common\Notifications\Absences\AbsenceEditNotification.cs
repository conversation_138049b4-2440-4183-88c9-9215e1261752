﻿using WorkTimeApi.Common.DTOs.Absences;

namespace WorkTimeApi.Common.Notifications.Absences
{
    public class AbsenceEditNotification : BaseNotification<AbsenceHospitalDTO>
    {
        public Guid UserId { get; }

        public bool IsActiveAbsence { get; }

        public Guid? EmployeeId { get; }

        public AbsenceHospitalDTO OldAbsence { get; }

        public AbsenceEditNotification(AbsenceHospitalDTO payload, Guid companyId, Guid userId, string creatorName, Guid? employeeId, AbsenceHospitalDTO oldAbsence = null, bool isActiveAbsence = false)
            : base(payload, companyId, NotificationsName.Absences.Edit.Push, creatorName)
        {
            UserId = userId;
            IsActiveAbsence = isActiveAbsence;
            OldAbsence = oldAbsence;
            EmployeeId = employeeId;

            if (payload != null)
            {
                Url = $"{companyId}/attendance?date={payload.FromDate:MM.yyyy}&absenceId={payload.Id}&fromNotification=true";
            }
        }
    }
}
