﻿using Gateway.Connections.Interfaces;
using MediatR;
using Microsoft.Extensions.Primitives;
using SSO.Common.Requests;

namespace Gateway.Handlers.SSO
{
	public class LogoutHandler : IRequestHandler<LogoutRequest, IResult>
	{
		private readonly ISSOConnection _ssoConnection;

		public LogoutHandler(ISSOConnection ssoConnection)
		{
			_ssoConnection = ssoConnection;
		}

		public async Task<IResult> Handle(LogoutRequest request, CancellationToken cancellationToken)
		{
			if (request.HttpRequest is null)
				return Results.BadRequest();

			var authorizationHeader = request.HttpRequest.Headers.Authorization;

			if (string.IsNullOrWhiteSpace(authorizationHeader)
				|| !request.HttpRequest.Headers.TryGetValue("refresh-token", out StringValues refreshTokenHeader))
				return Results.BadRequest();

			var ssoResponse = await _ssoConnection.LogoutAsync(authorizationHeader!, refreshTokenHeader!);

            request.HttpRequest.HttpContext.Response.Headers.Remove("Authorization");
            request.HttpRequest.HttpContext.Response.Headers.Remove("Refresh-token");
            request.HttpRequest.HttpContext.Request.Headers.Remove("Authorization");
            request.HttpRequest.HttpContext.Request.Headers.Remove("Refresh-token");

            return Results.StatusCode((int)ssoResponse.StatusCode);
		}
	}
}
