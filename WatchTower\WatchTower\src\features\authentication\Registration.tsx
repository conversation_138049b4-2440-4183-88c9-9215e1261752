import { ChangeEvent, MouseEvent, useState } from "react";
import { useNavigate } from "react-router-dom";
import Form from "../../components/Form/Form";
import Button from "../../components/Inputs/Button";
import Textbox from "../../components/Inputs/Textbox";
import { RegisterUserDTO } from "../../models/DTOs/RegisterUserDTO";
import { registration } from "../../services/authentication/authenticationService";
import Container from "../../components/Container";
import styled from "styled-components";
import Checkbox from "../../components/Inputs/Checkbox";
import { isValidEmail } from "../../services/emailService";
import EmailBox from "../../components/Inputs/EmailBox";
import PopUp from "../../components/PopUp/PopUp";
import PasswordBox from "../../components/Inputs/PasswordBox";
import { PasswortStrengthType } from "../../models/Enums/PasswortStrengthType";
import { passwordStrengthCheck } from "../../services/authentication/passwordService";

const NamesContainer = styled(Container)`
  display: flex;
  justify-content: space-between;
`;

const NameContainer = styled(Container)`
  flex-basis: 49%;
  margin: 0;
  box-sizing: border-box;
`;

const RegistrationButton = styled(Button)`
  width: 100%;
`;

const PasswordStrengthContainer = styled(Container)`
  width: 100%;
  display: flex;
  padding: 0 1rem 0 1rem;
  box-sizing: border-box;
`;

const PasswordStrengthRectangle = styled(Container)<{
  $passwordStrength: PasswortStrengthType;
}>`
  background-color: var(--password-strength-rectangle-empty-background-color);
  width: 100%;
  height: 0.4rem;
  margin: 0.5rem 0.5rem 0.25rem 0.5rem;
`;

const PasswordStrengthRectangleOne = styled(PasswordStrengthRectangle)`
  background-color: ${(props) =>
    props.$passwordStrength === PasswortStrengthType.Weak &&
    "var(--password-strength-rectangle-weak-background-color)"};
  background-color: ${(props) =>
    props.$passwordStrength >= PasswortStrengthType.Middle &&
    "var(--password-strength-rectangle-strong-background-color)"};
`;
const PasswordStrengthRectangleTwo = styled(PasswordStrengthRectangle)`
  background-color: ${(props) =>
    props.$passwordStrength >= PasswortStrengthType.Middle &&
    "var(--password-strength-rectangle-strong-background-color)"};
`;
const PasswordStrengthRectangleThree = styled(PasswordStrengthRectangle)`
  background-color: ${(props) =>
    props.$passwordStrength >= PasswortStrengthType.Good &&
    "var(--password-strength-rectangle-strong-background-color)"};
`;
const PasswordStrengthRectangleFour = styled(PasswordStrengthRectangle)`
  background-color: ${(props) =>
    props.$passwordStrength >= PasswortStrengthType.Strong &&
    "var(--password-strength-rectangle-strong-background-color)"};
`;

const AcceptTermsContainer = styled.div`
  display: flex;
  padding-left: 1rem;
`;

const Registration = () => {
  const [user, setUser] = useState({
    callbackUrl: `${window.location.origin}/auth/confirm-email`,
  } as RegisterUserDTO);
  const [acceptTerms, setAcceptTerms] = useState(false);
  const [isRegistrationButtonActive, setIsRegistrationButtonActive] =
    useState(false);
  const [passwordStrength, setPasswordStrength] = useState(
    PasswortStrengthType.Empty
  );
  const navigate = useNavigate();

  const handleUserChange = (e: ChangeEvent<HTMLInputElement>) => {
    const newUser = { ...user, [e.currentTarget.name]: e.currentTarget.value };
    setUser(newUser);

    const currentPasswordStrength = passwordStrengthCheck(newUser.password);
    setPasswordStrength(currentPasswordStrength);

    setIsRegistrationButtonActive(
      isValidEmail(newUser.email) &&
        newUser.password !== undefined &&
        newUser.password !== "" &&
        newUser.password === newUser.confirmPassword &&
        acceptTerms &&
        currentPasswordStrength >= 2
    );
  };

  const handleRegistrationSubmit = (e: MouseEvent<HTMLButtonElement>) => {
    e.preventDefault();

    registration(user).then(() => {
      navigate(`/auth/confirmation-email-sent/${user.email}`);
    });
  };

  const handleTermsCheckboxClick = () => {
    setAcceptTerms(!acceptTerms);

    setIsRegistrationButtonActive(
      isValidEmail(user.email) &&
        user.password !== undefined &&
        user.password !== "" &&
        user.password === user.confirmPassword &&
        !acceptTerms &&
        passwordStrength >= 2
    );
  };

  return (
    <Form>
      <NamesContainer>
        <NameContainer>
          <Textbox
            name="firstName"
            handleChange={handleUserChange}
            label="Name"
            value={user.firstName}
          />
        </NameContainer>
        <NameContainer>
          <Textbox
            name="lastName"
            handleChange={handleUserChange}
            label="Last name"
            value={user.lastName}
          />
        </NameContainer>
      </NamesContainer>
      <EmailBox
        name="email"
        handleChange={handleUserChange}
        label="E-mail"
        placeholder="Fill your email"
        value={user.email}
      />
      <PasswordBox
        name="password"
        handleChange={handleUserChange}
        label="Password"
        placeholder="Fill your password"
        value={user.password}
        type="password"
      />
      <Textbox
        name="confirmPassword"
        handleChange={handleUserChange}
        label="Confirm Password"
        placeholder="Confirm your password"
        value={user.confirmPassword}
        type="password"
      />
      <PasswordStrengthContainer>
        <PasswordStrengthRectangleOne $passwordStrength={passwordStrength} />
        <PasswordStrengthRectangleTwo $passwordStrength={passwordStrength} />
        <PasswordStrengthRectangleThree $passwordStrength={passwordStrength} />
        <PasswordStrengthRectangleFour $passwordStrength={passwordStrength} />
      </PasswordStrengthContainer>
      <AcceptTermsContainer>
        <Checkbox
          isChecked={acceptTerms}
          handleChange={handleTermsCheckboxClick}
          label=""
          name="acceptTerms"
          style={{ margin: "0 auto" }}
        />
        <PopUp title="">
          <h1></h1>
        </PopUp>
      </AcceptTermsContainer>
      <RegistrationButton
        onClick={handleRegistrationSubmit}
        label="Registration"
        disabled={!isRegistrationButtonActive}
      />
    </Form>
  );
};

export default Registration;
