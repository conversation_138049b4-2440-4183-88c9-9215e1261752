﻿using Microsoft.Extensions.Configuration;
using Moq;
using Moq.Protected;
using SSO.Common.DTOs;
using SSO.Connections;
using SSO.Connections.Interfaces;
using System.Net;
using System.Text.Json;

namespace SSO.Tests.SSO.Connections
{
    public class UserRegistrationsConnectionTests
    {
        private const string USER_GUID_STR = "4c34a6a8-4b6a-496c-bcc1-5a249d487b91";

        private readonly Mock<HttpMessageHandler> _httpMessageHandlerMock;
        private readonly HttpClient _httpClient;
        private readonly IUserRegistrationsConnection _userRegistrationsConnection; 
        private readonly string _userRegistrationsApiUrl = "https://api.example.com/";
        private readonly IConfiguration _configuration;

        public UserRegistrationsConnectionTests()
        {
            _httpMessageHandlerMock = new Mock<HttpMessageHandler>();
            _httpClient = new HttpClient(_httpMessageHandlerMock.Object)
            {
                BaseAddress = new Uri(_userRegistrationsApiUrl)
            };
            var inMemorySettings = new Dictionary<string, string?> {
                {"UserRegistrationsBaseUrl", "http://localhost/"},
            };

            _configuration = new ConfigurationBuilder()
                .AddInMemoryCollection(inMemorySettings)
                .Build();
            _userRegistrationsConnection = new UserRegistrationsConnection(_httpClient, _configuration);
        }

        [Fact]
        public async Task GetUserDetailsOnSuccessfulLogin()
        {
            // Arrange
            var user = new UserDTO
            {
                Id = new Guid(USER_GUID_STR),
                Email = "<EMAIL>",
                FirstName = "Test",
                LastName = "Testov",
                SecondName = "Testov",
            };

            _httpMessageHandlerMock.Protected()
                .Setup<Task<HttpResponseMessage>>("SendAsync", ItExpr.IsAny<HttpRequestMessage>(), ItExpr.IsAny<CancellationToken>())
                .ReturnsAsync(new HttpResponseMessage
                {
                    StatusCode = HttpStatusCode.OK,
                    Content = new StringContent(JsonSerializer.Serialize(user))
                });

            // Act
            var result = JsonSerializer.Deserialize<UserDTO>(await _userRegistrationsConnection.TryLoginAsync("Basic testauthorizationheadercontent"));

            // Assert
            Assert.NotNull(result);
            Assert.Equal(user.Email, result.Email);
            Assert.Equal(user.FirstName, result.FirstName);
            Assert.Equal(user.SecondName, result.SecondName);
            Assert.Equal(user.LastName, result.LastName);
        }

        [Fact]
        public async Task GetUserDetailsThrowsExeptionWhenUrlIsNotValid()
        {
            // Arrange
            var inMemorySettings = new Dictionary<string, string?> {
                {"UserRegistrationsBaseUrl", "unvalid url"},
            };

            var user = new UserDTO
            {
                Id = new Guid(USER_GUID_STR),
                Email = "<EMAIL>",
                FirstName = "Test",
                LastName = "Testov",
                SecondName = "Testov",
            };
            
            var configuration = new ConfigurationBuilder()
                .AddInMemoryCollection(inMemorySettings)
                .Build(); 
            
            var mockMessageHandler = new Mock<HttpMessageHandler>();
            mockMessageHandler.Protected()
                .Setup<Task<HttpResponseMessage>>("SendAsync", ItExpr.IsAny<HttpRequestMessage>(), ItExpr.IsAny<CancellationToken>())
                .ReturnsAsync(new HttpResponseMessage
                {
                    StatusCode = HttpStatusCode.OK,
                    Content = new StringContent(JsonSerializer.Serialize(user))
                });
            var underTest = new UserRegistrationsConnection(new HttpClient(mockMessageHandler.Object), configuration);

            // Act
            async Task Act() => await underTest.TryLoginAsync("Basic testauthorizationheadercontent");

            // Assert
            await Assert.ThrowsAsync<InvalidOperationException>(Act);
        }

        [Fact]
        public async Task TryGetUserOrCreateIt_ValidUserDTO_ReturnsHttpResponseMessage()
        {
            // Arrange
            var userDTO = new UserDTO
            {
                Id = new Guid(USER_GUID_STR),
                Email = "<EMAIL>",
                FirstName = "John",
                LastName = "Doe"
            };

            var response = new HttpResponseMessage(HttpStatusCode.OK)
            {
                Content = new StringContent(Guid.NewGuid().ToString())
            };

            _httpMessageHandlerMock.Protected().Setup<Task<HttpResponseMessage>>(
                "SendAsync",
                ItExpr.IsAny<HttpRequestMessage>(),
                ItExpr.IsAny<CancellationToken>()
            ).ReturnsAsync(response);

            // Act
            var result = await _userRegistrationsConnection.TryGetUserOrCreateItAsync(userDTO);

            // Assert
            Assert.NotNull(result);
            Assert.Equal(HttpStatusCode.OK, result.StatusCode);
        }

        [Fact]
        public async Task TryGetUserOrCreateIt_ApiCallFails_ThrowsInvalidOperationException()
        {
            // Arrange
            var userDTO = new UserDTO
            {
                Id = new Guid(USER_GUID_STR),
                Email = "<EMAIL>",
                FirstName = "John",
                LastName = "Doe"
            };

            var response = new HttpResponseMessage(HttpStatusCode.InternalServerError);

            var mockMessageHandler = new Mock<HttpMessageHandler>();
            mockMessageHandler.Protected()
                .Setup<Task<HttpResponseMessage>>("SendAsync", ItExpr.IsAny<HttpRequestMessage>(), ItExpr.IsAny<CancellationToken>())
                .ReturnsAsync(new HttpResponseMessage
                {
                    StatusCode = HttpStatusCode.OK,
                    Content = new StringContent(JsonSerializer.Serialize(userDTO))
                });
            var underTest = new UserRegistrationsConnection(new HttpClient(mockMessageHandler.Object), _configuration);

            // Act & Assert
            await Assert.ThrowsAsync<InvalidOperationException>(async () => await _userRegistrationsConnection.TryGetUserOrCreateItAsync(userDTO));
        }
    }
}
