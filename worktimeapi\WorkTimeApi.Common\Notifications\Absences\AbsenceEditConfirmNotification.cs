﻿using WorkTimeApi.Common.DTOs.Absences;

namespace WorkTimeApi.Common.Notifications.Absences
{
    public class AbsenceEditConfirmNotification : BaseNotification<AbsenceHospitalDTO>
    {
        public Guid UserId { get; }

        public AbsenceEditConfirmNotification(AbsenceHospitalDTO payload, Guid companyId, Guid userId, string creatorName)
            : base(payload, companyId, NotificationsName.Absences.Edit.Push, creatorName)
        {
            UserId = userId;

            if (payload != null)
            {
                Url = $"{companyId}/attendance?date={payload.FromDate:MM.yyyy}&absenceId={payload.Id}&fromNotification=true";
            }
        }
    }
}
