﻿using Gateway.Common.Globals;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Http.HttpResults;
using Moq;
using SSO.Common.DTOs;
using SSO.Filters;
using SSO.Services.Interfaces;
using System.Security.Claims;

namespace SSO.Tests.SSO.Filters
{
    public class SSOFilterTests
    {
        private const string USER_GUID_STR = "4c34a6a8-4b6a-496c-bcc1-5a249d487b91";

        private readonly Mock<IHttpContextAccessor> _contextAccessorMock;
        private readonly Mock<IJwtGeneratorService> _jwtGeneratorServiceMock;
        private readonly Mock<IRefreshTokensService> _refreshTokensServiceMock;
        private readonly Mock<IJWTValidator> _jwtValidatorMock;
        private readonly Mock<IUsersService> _usersServiceMock;
        private readonly Mock<GlobalUser> _globalUserMock;

        public SSOFilterTests()
        {
            _contextAccessorMock = new Mock<IHttpContextAccessor>();
            _jwtGeneratorServiceMock = new Mock<IJwtGeneratorService>();
            _refreshTokensServiceMock = new Mock<IRefreshTokensService>();
            _jwtValidatorMock = new Mock<IJWTValidator>();
            _usersServiceMock = new Mock<IUsersService>();
            _globalUserMock = new Mock<GlobalUser>();
        }

        [Fact]
        public async Task ReturnUnauthorizedWhenHttpContextIsNull()
        {
            // Arrange
            _contextAccessorMock.Object.HttpContext = null;
            var endpointFilterInvocationContextMock = new Mock<EndpointFilterInvocationContext>();
            DefaultHttpContext defaultContext = new DefaultHttpContext();

            // Act
            var middlewareInstance = new SSOFilter(
                _contextAccessorMock.Object, 
                _jwtGeneratorServiceMock.Object,
                _refreshTokensServiceMock.Object,
                _jwtValidatorMock.Object,
                _usersServiceMock.Object,
                _globalUserMock.Object);

            var result = await middlewareInstance.InvokeAsync(endpointFilterInvocationContextMock.Object, (innerHttpContext) =>
            {
                return ValueTask.FromResult((object?)innerHttpContext);
            });
            var unauthorizedResult = (UnauthorizedHttpResult?)result;

            // Assert
            Assert.NotNull(unauthorizedResult);
            Assert.Equal(401, unauthorizedResult.StatusCode);
        }

        [Fact]
        public async Task ReturnsUserDataIfAccessTokenIsValid()
        {
            // Arrange
            DefaultHttpContext defaultContext = new DefaultHttpContext();
            var accessToken = "accesstokencontent";
            var email = "<EMAIL>";
            defaultContext.Request.Headers.Authorization = $"Bearer {accessToken}";
            _contextAccessorMock.Setup(_ => _.HttpContext).Returns(defaultContext);
            var endpointFilterInvocationContextMock = new Mock<EndpointFilterInvocationContext>();

            var claimsPrincipalMock = new Mock<ClaimsPrincipal>();
            claimsPrincipalMock.Setup(_ => _.Claims).Returns(new List<Claim>()
            {
                new Claim("UserId", USER_GUID_STR),
                new Claim(ClaimTypes.Email, email)
            });

            var userDTO = new UserDTO()
            {
                Email = email,
                Id = Guid.Parse(USER_GUID_STR)
            };

            _jwtValidatorMock.Setup(v => v.ValidateToken(accessToken)).Returns(claimsPrincipalMock.Object);
            _usersServiceMock.Setup(v => v.GetUserByIdAsync(Guid.Parse(USER_GUID_STR))).ReturnsAsync(userDTO);

            // Act
            var middlewareInstance = new SSOFilter(
                _contextAccessorMock.Object,
                _jwtGeneratorServiceMock.Object,
                _refreshTokensServiceMock.Object,
                _jwtValidatorMock.Object,
                _usersServiceMock.Object,
                _globalUserMock.Object);

            await middlewareInstance.InvokeAsync(endpointFilterInvocationContextMock.Object, (innerHttpContext) =>
            {
                return ValueTask.FromResult((object?)innerHttpContext);
            });

            // Assert
            Assert.NotNull(_contextAccessorMock.Object.HttpContext);
            Assert.NotNull(_contextAccessorMock.Object.HttpContext.User);
            Assert.Equal(claimsPrincipalMock.Object, _contextAccessorMock.Object.HttpContext.User);
        }

        [Fact]
        public async Task ReturnsUserDataIfAccessTokenIsInvalidButRefreshTokenIsValidAndActive()
        {
            // Arrange
            DefaultHttpContext defaultContext = new DefaultHttpContext();
            var accessToken = "accesstokencontent";
            defaultContext.Request.Headers.Authorization = $"Bearer {accessToken}";
            var refreshToken = "refreshtokencontent";
            defaultContext.Request.Headers.Add("Refresh-Token", refreshToken);
            _contextAccessorMock.Setup(_ => _.HttpContext).Returns(defaultContext);
            var endpointFilterInvocationContextMock = new Mock<EndpointFilterInvocationContext>();

            var claimsPrincipalMock = new Mock<ClaimsPrincipal>();
            claimsPrincipalMock.Setup(_ => _.Claims).Returns(new List<Claim>()
            {
                new Claim(ClaimTypes.Email, "<EMAIL>"),
                new Claim("UserId", USER_GUID_STR)
            });

            _jwtValidatorMock.Setup(v => v.ValidateToken(accessToken)).Returns((ClaimsPrincipal?)null);
            _jwtValidatorMock.Setup(v => v.ValidateToken(refreshToken)).Returns(claimsPrincipalMock.Object);
            _refreshTokensServiceMock.Setup(r => r.CheckIfRefreshTokenIsActiveAsync(refreshToken)).ReturnsAsync(true);
            var newAccessToken = "accesstokennewcontent";
            var newRefreshToken = "refreshtokennewcontent";
            var refreshTokenDTO = new RefreshTokenDTO
            {
                Token = newRefreshToken
            };

            _refreshTokensServiceMock.Setup(r => r.DeactivateTokenAsync(It.IsAny<string>()));
            _refreshTokensServiceMock.Setup(r => r.AddRefreshTokenAsync(It.IsAny<string>(), It.IsAny<Guid>(), It.IsAny<string>())).ReturnsAsync(refreshTokenDTO);
            _jwtGeneratorServiceMock.Setup(j => j.GenerateAccessTokenAsync(claimsPrincipalMock.Object.Claims)).ReturnsAsync(newAccessToken);
            _jwtGeneratorServiceMock.Setup(j => j.GenerateRefreshTokenAsync(claimsPrincipalMock.Object.Claims)).ReturnsAsync(newRefreshToken);

            // Act
            var middlewareInstance = new SSOFilter(
                _contextAccessorMock.Object,
                _jwtGeneratorServiceMock.Object,
                _refreshTokensServiceMock.Object,
                _jwtValidatorMock.Object,
                _usersServiceMock.Object,
                _globalUserMock.Object);

            await middlewareInstance.InvokeAsync(endpointFilterInvocationContextMock.Object, (innerHttpContext) =>
            {
                return ValueTask.FromResult((object?)innerHttpContext);
            });

            // Assert
            Assert.NotNull(_contextAccessorMock.Object.HttpContext);
            Assert.NotNull(_contextAccessorMock.Object.HttpContext.User);
            Assert.Equal(claimsPrincipalMock.Object, _contextAccessorMock.Object.HttpContext.User);
            Assert.Equal($"Bearer {newAccessToken}", _contextAccessorMock.Object.HttpContext.Response.Headers.Authorization);
            Assert.Equal(newRefreshToken, _contextAccessorMock.Object.HttpContext.Response.Headers["Refresh-Token"]);
        }

        [Fact]
        public async Task ReturnUnauthorizedWhenRefreshTokenIsNotValid()
        {
            // Arrange
            _contextAccessorMock.Object.HttpContext = null;
            var endpointFilterInvocationContextMock = new Mock<EndpointFilterInvocationContext>();
            DefaultHttpContext defaultContext = new DefaultHttpContext();
            var accessToken = "accesstokencontent";
            defaultContext.Request.Headers.Authorization = $"Bearer {accessToken}";
            var refreshToken = "refreshtokencontent";
            defaultContext.Request.Headers.Add("Refresh-Token", refreshToken);
            _contextAccessorMock.Setup(_ => _.HttpContext).Returns(defaultContext);

            _jwtValidatorMock.Setup(v => v.ValidateToken(accessToken)).Returns((ClaimsPrincipal?)null);
            _jwtValidatorMock.Setup(v => v.ValidateToken(refreshToken)).Returns((ClaimsPrincipal?)null); 

            // Act
            var middlewareInstance = new SSOFilter(
                _contextAccessorMock.Object,
                _jwtGeneratorServiceMock.Object,
                _refreshTokensServiceMock.Object,
                _jwtValidatorMock.Object,
                _usersServiceMock.Object,
                _globalUserMock.Object);

            var result = await middlewareInstance.InvokeAsync(endpointFilterInvocationContextMock.Object, (innerHttpContext) =>
            {
                return ValueTask.FromResult((object?)innerHttpContext);
            });
            var unauthorizedResult = (UnauthorizedHttpResult?)result;

            // Assert
            Assert.NotNull(unauthorizedResult);
            Assert.Equal(401, unauthorizedResult.StatusCode);
        }

        [Fact]
        public async Task ReturnUnauthorizedWhenRefreshTokenIsValidButNotActive()
        {
            // Arrange
            _contextAccessorMock.Object.HttpContext = null;
            var endpointFilterInvocationContextMock = new Mock<EndpointFilterInvocationContext>();
            DefaultHttpContext defaultContext = new DefaultHttpContext();
            var accessToken = "accesstokencontent";
            defaultContext.Request.Headers.Authorization = $"Bearer {accessToken}";
            var refreshToken = "refreshtokencontent";
            defaultContext.Request.Headers.Add("Refresh-Token", refreshToken);
            _contextAccessorMock.Setup(_ => _.HttpContext).Returns(defaultContext);

            var claimsPrincipalMock = new Mock<ClaimsPrincipal>();
            claimsPrincipalMock.Setup(_ => _.Claims).Returns(new List<Claim>()
            {
                new Claim(ClaimTypes.Email, "<EMAIL>")
            });
            _jwtValidatorMock.Setup(v => v.ValidateToken(accessToken)).Returns((ClaimsPrincipal?)null);
            _jwtValidatorMock.Setup(v => v.ValidateToken(refreshToken)).Returns(claimsPrincipalMock.Object);
            _refreshTokensServiceMock.Setup(r => r.CheckIfRefreshTokenIsActiveAsync(refreshToken)).ReturnsAsync(false);

            // Act
            var middlewareInstance = new SSOFilter(
                _contextAccessorMock.Object,
                _jwtGeneratorServiceMock.Object,
                _refreshTokensServiceMock.Object,
                _jwtValidatorMock.Object,
                _usersServiceMock.Object,
                _globalUserMock.Object);

            var result = await middlewareInstance.InvokeAsync(endpointFilterInvocationContextMock.Object, (innerHttpContext) =>
            {
                return ValueTask.FromResult((object?)innerHttpContext);
            });
            var unauthorizedResult = (UnauthorizedHttpResult?)result;

            // Assert
            Assert.NotNull(unauthorizedResult);
            Assert.Equal(401, unauthorizedResult.StatusCode);
        }
    }
}
