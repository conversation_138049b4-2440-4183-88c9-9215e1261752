﻿using Gateway.Common.Extenstions.EndpointExtensions;
using Gateway.Common.Extenstions.MediatorExtensions;
using Gateway.Connections;
using Gateway.Connections.Handlers;
using Gateway.Connections.Interfaces;
using Gateway.Extenstions.MediatorExtensions;
using Gateway.Requests.UserRegistration;
using WorkTimeApi.Common.Requests.Users;

namespace Gateway.EndpointDefinitions.UserRegistration
{
	public class UserRegistrationEndpointDefinition : IEndpointDefinition
	{
		public void DefineEndpoints(WebApplication app)
		{
			app.MediatePost<RegistrationRequest>("/user-registration/registration")
				.MediatePost<ConfirmEmailRequest>("/user-registration/confirm-email")
				.AuthenticatedPost<EditUserDataRequest>("/user-registration/edit-user-data")
				.AuthenticatedGet<GetSenderaUserRequest>("/user-registration/sendera-user")
				.MediateGet<ConfirmEmailCodeRequest>("/confirm-email");
		}

		public void DefineServices(IServiceCollection services)
		{
			services
				.AddHttpClient<IUserRegistrationConnection, UserRegistrationConnection>()
				.AddHttpMessageHandler<RequestIdHandler>()
				.AddHttpMessageHandler<UserIdHandler>()
				.AddHttpMessageHandler<AddOriginHeaderHandler>()
				.AddHttpMessageHandler<AddOriginIdHeaderHandler>();

			services.AddHttpClient<IEmailsConnection, EmailsConnection>();
		}
	}
}