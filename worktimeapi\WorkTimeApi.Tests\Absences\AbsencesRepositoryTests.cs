using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Configuration;
using Moq;
using WorkTimeApi.Common.Enums;
using WorkTimeApi.Database;
using WorkTimeApi.Database.Models;
using WorkTimeApi.Database.Repositories.Absences;
using WorkTimeApi.Database.Repositories.Interfaces.Absences;
using WorkTimeApi.Database.Services.Interfaces;

namespace WorkTimeApi.Tests.Absences;

public class AbsencesRepositoryTests
{
    private class InMemoryDbContextFactory(DbContextOptions<WorkTimeApiDbContext> options, IConfiguration configuration)
        : IDbContextFactory<WorkTimeApiDbContext>
    {
        public WorkTimeApiDbContext CreateDbContext() => new(options, configuration);

        public ValueTask<WorkTimeApiDbContext> CreateDbContextAsync(CancellationToken cancellationToken = default) => ValueTask.FromResult(new WorkTimeApiDbContext(options, configuration));
    }

    private static (IDbContextFactory<WorkTimeApiDbContext> Factory, string DbName, IConfiguration Config) CreateFactory()
    {
        var dbName = Guid.NewGuid().ToString();
        var options = new DbContextOptionsBuilder<WorkTimeApiDbContext>()
            .UseInMemoryDatabase(dbName)
            .Options;

        var configuration = new ConfigurationBuilder()
            .AddInMemoryCollection(new Dictionary<string, string?>
            {
                ["WorkTimeDefaultCompanyId"] = Guid.NewGuid().ToString()
            })
            .Build();
        var factory = new InMemoryDbContextFactory(options, configuration);
        return (factory, dbName, configuration);
    }

    [Fact]
    public async Task MarkExportedAsync_UpdatesOnlyAbsencesForSpecifiedCompany()
    {
        var (factory, dbName, config) = CreateFactory();

        using var context = new WorkTimeApiDbContext(new DbContextOptionsBuilder<WorkTimeApiDbContext>().UseInMemoryDatabase(dbName).Options, config);

        var company1 = new Company { Id = Guid.NewGuid(), Name = "C1", Bulstat = "B1" };
        var company2 = new Company { Id = Guid.NewGuid(), Name = "C2", Bulstat = "B2" };

        var user = new User { Id = Guid.NewGuid(), Email = "<EMAIL>" };

        var structureLevel1 = new StructureLevel { Id = Guid.NewGuid(), Name = "SL1", Type = StructureLevelType.Department, CompanyId = company1.Id };
        var structureLevel2 = new StructureLevel { Id = Guid.NewGuid(), Name = "SL2", Type = StructureLevelType.Department, CompanyId = company2.Id };

        var employee1 = new Employee { Id = Guid.NewGuid(), CompanyId = company1.Id, Company = company1, UserId = user.Id, User = user };
        var employee2 = new Employee { Id = Guid.NewGuid(), CompanyId = company2.Id, Company = company2, UserId = user.Id, User = user };

        var payroll1 = new Payroll { Id = Guid.NewGuid(), CompanyId = company1.Id, Company = company1, EmployeeId = employee1.Id, Employee = employee1, StructureLevelId = structureLevel1.Id, StructureLevel = structureLevel1 };
        var payroll2 = new Payroll { Id = Guid.NewGuid(), CompanyId = company2.Id, Company = company2, EmployeeId = employee2.Id, Employee = employee2, StructureLevelId = structureLevel2.Id, StructureLevel = structureLevel2 };

        var absenceCompany1 = new Absence { Id = Guid.NewGuid(), PayrollId = payroll1.Id, Payroll = payroll1, FromDate = DateTime.Today, ToDate = DateTime.Today.AddDays(1), Status = AbsenceStatus.Approved, ExportStatus = AbsenceExportStatus.Pending };
        var absenceCompany2 = new Absence { Id = Guid.NewGuid(), PayrollId = payroll2.Id, Payroll = payroll2, FromDate = DateTime.Today, ToDate = DateTime.Today.AddDays(1), Status = AbsenceStatus.Approved, ExportStatus = AbsenceExportStatus.Pending };

        context.Companies.AddRange(company1, company2);
        context.Users.Add(user);
        context.StructureLevels.AddRange(structureLevel1, structureLevel2);
        context.Employees.AddRange(employee1, employee2);
        context.Payrolls.AddRange(payroll1, payroll2);
        context.Absences.AddRange(absenceCompany1, absenceCompany2);
        await context.SaveChangesAsync();

        var calendarService = new Mock<ICalendarService>(MockBehavior.Strict);
        var repository = new AbsencesRepository(factory, calendarService.Object);

        var updated = await repository.MarkExportedAsync<Absence>(
            companyId: factory.CreateDbContext().Payrolls.First().CompanyId,
            ids: [factory.CreateDbContext().Absences.First().Id, factory.CreateDbContext().Absences.Skip(1).First().Id]
        );

        var updatedList = updated.ToList();
        Assert.Single(updatedList);
        Assert.Equal(AbsenceExportStatus.Exported, updatedList[0].ExportStatus);

        await using var verifyCtx = factory.CreateDbContext();
        var allAbsences = await verifyCtx.Absences.AsNoTracking().OrderBy(a => a.Payroll.CompanyId).ToListAsync();
        Assert.Equal(1, allAbsences.Count(a => a.ExportStatus == AbsenceExportStatus.Exported));
        Assert.Equal(1, allAbsences.Count(a => a.ExportStatus == AbsenceExportStatus.Pending));
    }

    [Fact]
    public async Task MarkExportedAsync_NoAbsencesForCompany_ReturnsEmptyAndDoesNotChangeOthers()
    {
        var (factory, dbName, config) = CreateFactory();

        using var context = new WorkTimeApiDbContext(new DbContextOptionsBuilder<WorkTimeApiDbContext>().UseInMemoryDatabase(dbName).Options, config);
        var company1 = new Company { Id = Guid.NewGuid(), Name = "C1", Bulstat = "B1" };
        var company2 = new Company { Id = Guid.NewGuid(), Name = "C2", Bulstat = "B2" };
        var user = new User { Id = Guid.NewGuid(), Email = "<EMAIL>" };

        var structureLevel1 = new StructureLevel { Id = Guid.NewGuid(), Name = "SL1", Type = StructureLevelType.Department, CompanyId = company1.Id };
        var structureLevel2 = new StructureLevel { Id = Guid.NewGuid(), Name = "SL2", Type = StructureLevelType.Department, CompanyId = company2.Id };

        var employee1 = new Employee { Id = Guid.NewGuid(), CompanyId = company1.Id, Company = company1, UserId = user.Id, User = user };
        var employee2 = new Employee { Id = Guid.NewGuid(), CompanyId = company2.Id, Company = company2, UserId = user.Id, User = user };

        var payroll1 = new Payroll { Id = Guid.NewGuid(), CompanyId = company1.Id, Company = company1, EmployeeId = employee1.Id, Employee = employee1, StructureLevelId = structureLevel1.Id, StructureLevel = structureLevel1 };
        var payroll2 = new Payroll { Id = Guid.NewGuid(), CompanyId = company2.Id, Company = company2, EmployeeId = employee2.Id, Employee = employee2, StructureLevelId = structureLevel2.Id, StructureLevel = structureLevel2 };

        var absenceCompany1 = new Absence { Id = Guid.NewGuid(), PayrollId = payroll1.Id, Payroll = payroll1, FromDate = DateTime.Today, ToDate = DateTime.Today.AddDays(1), Status = AbsenceStatus.Approved, ExportStatus = AbsenceExportStatus.Pending };
        var absenceCompany2 = new Absence { Id = Guid.NewGuid(), PayrollId = payroll2.Id, Payroll = payroll2, FromDate = DateTime.Today, ToDate = DateTime.Today.AddDays(1), Status = AbsenceStatus.Approved, ExportStatus = AbsenceExportStatus.Pending };

        context.Companies.AddRange(company1, company2);
        context.Users.Add(user);
        context.StructureLevels.AddRange(structureLevel1, structureLevel2);
        context.Employees.AddRange(employee1, employee2);
        context.Payrolls.AddRange(payroll1, payroll2);
        context.Absences.AddRange(absenceCompany1, absenceCompany2);
        await context.SaveChangesAsync();

        var calendarService = new Mock<ICalendarService>(MockBehavior.Strict);
        var repository = new AbsencesRepository(factory, calendarService.Object);

        var updated = await repository.MarkExportedAsync<Absence>(
            companyId: company2.Id,
            ids: [absenceCompany1.Id] // This absence belongs to company1, not company2
        );

        Assert.Empty(updated);

        await using var verifyCtx = factory.CreateDbContext();
        var verify = await context.Absences.AsNoTracking().FirstAsync();
        Assert.Equal(AbsenceExportStatus.Pending, verify.ExportStatus);
    }

    [Fact]
    public async Task GetCompanyAbsencesForTRZAsync_ShouldReturnAbsencesForSpecificMonth()
    {
        // Arrange
        var (factory, dbName, config) = CreateFactory();
        using var context = new WorkTimeApiDbContext(new DbContextOptionsBuilder<WorkTimeApiDbContext>().UseInMemoryDatabase(dbName).Options, config);

        var companyId = Guid.NewGuid();
        var employeeId = Guid.NewGuid();
        var monthDate = new DateTime(2025, 8, 15);

        var company = new Company { Id = companyId, Name = "Test Company", Bulstat = "B123" };
        var user = new User { Id = Guid.NewGuid(), Email = "<EMAIL>" };
        var structureLevel = new StructureLevel { Id = Guid.NewGuid(), Name = "SL1", Type = StructureLevelType.Department, CompanyId = companyId };
        var employee = new Employee { Id = employeeId, CompanyId = companyId, Company = company, UserId = user.Id, User = user };
        var payroll = new Payroll { Id = Guid.NewGuid(), CompanyId = companyId, Company = company, EmployeeId = employeeId, Employee = employee, StructureLevelId = structureLevel.Id, StructureLevel = structureLevel };

        var absences = new List<Absence>
        {
            new()
            {
                Id = Guid.NewGuid(),
                FromDate = new DateTime(2025, 8, 10),
                ToDate = new DateTime(2025, 8, 12),
                Status = AbsenceStatus.Approved,
                ExportStatus = AbsenceExportStatus.Pending,
                PayrollId = payroll.Id,
                Payroll = payroll
            },
            new()
            {
                Id = Guid.NewGuid(),
                FromDate = new DateTime(2025, 8, 20),
                ToDate = new DateTime(2025, 8, 22),
                Status = AbsenceStatus.Approved,
                ExportStatus = AbsenceExportStatus.Pending,
                PayrollId = payroll.Id,
                Payroll = payroll
            },
            new()
            {
                Id = Guid.NewGuid(),
                FromDate = new DateTime(2025, 7, 25), // July - should be filtered out
                ToDate = new DateTime(2025, 7, 27),
                Status = AbsenceStatus.Approved,
                ExportStatus = AbsenceExportStatus.Pending,
                PayrollId = payroll.Id,
                Payroll = payroll
            },
            new()
            {
                Id = Guid.NewGuid(),
                FromDate = new DateTime(2025, 9, 5), // September - should be filtered out
                ToDate = new DateTime(2025, 9, 7),
                Status = AbsenceStatus.Approved,
                ExportStatus = AbsenceExportStatus.Pending,
                PayrollId = payroll.Id,
                Payroll = payroll
            }
        };

        context.Companies.Add(company);
        context.Users.Add(user);
        context.StructureLevels.Add(structureLevel);
        context.Employees.Add(employee);
        context.Payrolls.Add(payroll);
        context.Absences.AddRange(absences);
        await context.SaveChangesAsync();

        var calendarService = new Mock<ICalendarService>(MockBehavior.Strict);
        var repository = new AbsencesRepository(factory, calendarService.Object);

        // Act
        var result = await repository.GetCompanyAbsencesForTRZAsync<Absence>(companyId, monthDate);

        // Assert
        Assert.NotNull(result);
        var resultList = result.ToList();
        Assert.Equal(2, resultList.Count); // Only August absences should be returned

        // Verify the correct absences were returned
        Assert.Contains(resultList, r => r.FromDate.Month == 8 && r.FromDate.Year == 2025);
        Assert.DoesNotContain(resultList, r => r.FromDate.Month == 7 || r.FromDate.Month == 9);
    }

    [Fact]
    public async Task GetCompanyAbsencesForTRZAsync_ShouldFilterByCompanyId()
    {
        // Arrange
        var (factory, dbName, config) = CreateFactory();
        using var context = new WorkTimeApiDbContext(new DbContextOptionsBuilder<WorkTimeApiDbContext>().UseInMemoryDatabase(dbName).Options, config);

        var companyId1 = Guid.NewGuid();
        var companyId2 = Guid.NewGuid();
        var employeeId = Guid.NewGuid();
        var monthDate = new DateTime(2025, 8, 15);

        var company1 = new Company { Id = companyId1, Name = "Company 1", Bulstat = "B1" };
        var company2 = new Company { Id = companyId2, Name = "Company 2", Bulstat = "B2" };
        var user = new User { Id = Guid.NewGuid(), Email = "<EMAIL>" };
        var structureLevel1 = new StructureLevel { Id = Guid.NewGuid(), Name = "SL1", Type = StructureLevelType.Department, CompanyId = companyId1 };
        var structureLevel2 = new StructureLevel { Id = Guid.NewGuid(), Name = "SL2", Type = StructureLevelType.Department, CompanyId = companyId2 };
        var employee = new Employee { Id = employeeId, CompanyId = companyId1, Company = company1, UserId = user.Id, User = user };
        var payroll1 = new Payroll { Id = Guid.NewGuid(), CompanyId = companyId1, Company = company1, EmployeeId = employeeId, Employee = employee, StructureLevelId = structureLevel1.Id, StructureLevel = structureLevel1 };
        var payroll2 = new Payroll { Id = Guid.NewGuid(), CompanyId = companyId2, Company = company2, EmployeeId = employeeId, Employee = employee, StructureLevelId = structureLevel2.Id, StructureLevel = structureLevel2 };

        var absences = new List<Absence>
        {
            new()
            {
                Id = Guid.NewGuid(),
                FromDate = new DateTime(2025, 8, 10),
                ToDate = new DateTime(2025, 8, 12),
                Status = AbsenceStatus.Approved,
                ExportStatus = AbsenceExportStatus.Pending,
                PayrollId = payroll1.Id,
                Payroll = payroll1
            },
            new()
            {
                Id = Guid.NewGuid(),
                FromDate = new DateTime(2025, 8, 20),
                ToDate = new DateTime(2025, 8, 22),
                Status = AbsenceStatus.Approved,
                ExportStatus = AbsenceExportStatus.Pending,
                PayrollId = payroll2.Id,
                Payroll = payroll2
            }
        };

        context.Companies.AddRange(company1, company2);
        context.Users.Add(user);
        context.StructureLevels.AddRange(structureLevel1, structureLevel2);
        context.Employees.Add(employee);
        context.Payrolls.AddRange(payroll1, payroll2);
        context.Absences.AddRange(absences);
        await context.SaveChangesAsync();

        var calendarService = new Mock<ICalendarService>(MockBehavior.Strict);
        var repository = new AbsencesRepository(factory, calendarService.Object);

        // Act
        var result = await repository.GetCompanyAbsencesForTRZAsync<Absence>(companyId1, monthDate);

        // Assert
        Assert.NotNull(result);
        var resultList = result.ToList();
        Assert.Single(resultList);
        Assert.Equal(companyId1, resultList[0].Payroll.CompanyId);
    }

    [Fact]
    public async Task GetCompanyAbsencesForTRZAsync_ShouldFilterByStatus()
    {
        // Arrange
        var (factory, dbName, config) = CreateFactory();
        using var context = new WorkTimeApiDbContext(new DbContextOptionsBuilder<WorkTimeApiDbContext>().UseInMemoryDatabase(dbName).Options, config);

        var companyId = Guid.NewGuid();
        var employeeId = Guid.NewGuid();
        var monthDate = new DateTime(2025, 8, 15);

        var company = new Company { Id = companyId, Name = "Test Company", Bulstat = "B123" };
        var user = new User { Id = Guid.NewGuid(), Email = "<EMAIL>" };
        var structureLevel = new StructureLevel { Id = Guid.NewGuid(), Name = "SL1", Type = StructureLevelType.Department, CompanyId = companyId };
        var employee = new Employee { Id = employeeId, CompanyId = companyId, Company = company, UserId = user.Id, User = user };
        var payroll = new Payroll { Id = Guid.NewGuid(), CompanyId = companyId, Company = company, EmployeeId = employeeId, Employee = employee, StructureLevelId = structureLevel.Id, StructureLevel = structureLevel };

        var absences = new List<Absence>
        {
            new()
            {
                Id = Guid.NewGuid(),
                FromDate = new DateTime(2025, 8, 10),
                ToDate = new DateTime(2025, 8, 12),
                Status = AbsenceStatus.Approved, // Should be included
                ExportStatus = AbsenceExportStatus.Pending,
                PayrollId = payroll.Id,
                Payroll = payroll
            },
            new()
            {
                Id = Guid.NewGuid(),
                FromDate = new DateTime(2025, 8, 20),
                ToDate = new DateTime(2025, 8, 22),
                Status = AbsenceStatus.Pending, // Should be filtered out
                ExportStatus = AbsenceExportStatus.Pending,
                PayrollId = payroll.Id,
                Payroll = payroll
            },
            new()
            {
                Id = Guid.NewGuid(),
                FromDate = new DateTime(2025, 8, 25),
                ToDate = new DateTime(2025, 8, 27),
                Status = AbsenceStatus.Declined, // Should be filtered out
                ExportStatus = AbsenceExportStatus.Pending,
                PayrollId = payroll.Id,
                Payroll = payroll
            }
        };

        context.Companies.Add(company);
        context.Users.Add(user);
        context.StructureLevels.Add(structureLevel);
        context.Employees.Add(employee);
        context.Payrolls.Add(payroll);
        context.Absences.AddRange(absences);
        await context.SaveChangesAsync();

        var calendarService = new Mock<ICalendarService>(MockBehavior.Strict);
        var repository = new AbsencesRepository(factory, calendarService.Object);

        // Act
        var result = await repository.GetCompanyAbsencesForTRZAsync<Absence>(companyId, monthDate);

        // Assert
        Assert.NotNull(result);
        var resultList = result.ToList();
        Assert.Equal(1, resultList.Count);

        Assert.Contains(resultList, r => r.Status == AbsenceStatus.Approved);
        Assert.DoesNotContain(resultList, r => r.Status == AbsenceStatus.Declined);
        Assert.DoesNotContain(resultList, r => r.Status == AbsenceStatus.Pending);
    }

    [Fact]
    public async Task GetCompanyAbsencesForTRZAsync_ShouldFilterByExportStatus()
    {
        // Arrange
        var (factory, dbName, config) = CreateFactory();
        using var context = new WorkTimeApiDbContext(new DbContextOptionsBuilder<WorkTimeApiDbContext>().UseInMemoryDatabase(dbName).Options, config);

        var companyId = Guid.NewGuid();
        var employeeId = Guid.NewGuid();
        var monthDate = new DateTime(2025, 8, 15);

        var company = new Company { Id = companyId, Name = "Test Company", Bulstat = "B123" };
        var user = new User { Id = Guid.NewGuid(), Email = "<EMAIL>" };
        var structureLevel = new StructureLevel { Id = Guid.NewGuid(), Name = "SL1", Type = StructureLevelType.Department, CompanyId = companyId };
        var employee = new Employee { Id = employeeId, CompanyId = companyId, Company = company, UserId = user.Id, User = user };
        var payroll = new Payroll { Id = Guid.NewGuid(), CompanyId = companyId, Company = company, EmployeeId = employeeId, Employee = employee, StructureLevelId = structureLevel.Id, StructureLevel = structureLevel };

        var absences = new List<Absence>
        {
            new()
            {
                Id = Guid.NewGuid(),
                FromDate = new DateTime(2025, 8, 10),
                ToDate = new DateTime(2025, 8, 12),
                Status = AbsenceStatus.Approved,
                ExportStatus = AbsenceExportStatus.Pending, // Should be included
                PayrollId = payroll.Id,
                Payroll = payroll
            },
            new()
            {
                Id = Guid.NewGuid(),
                FromDate = new DateTime(2025, 8, 20),
                ToDate = new DateTime(2025, 8, 22),
                Status = AbsenceStatus.Approved,
                ExportStatus = AbsenceExportStatus.Exported, // Should be filtered out
                PayrollId = payroll.Id,
                Payroll = payroll
            },
            new()
            {
                Id = Guid.NewGuid(),
                FromDate = new DateTime(2025, 8, 25),
                ToDate = new DateTime(2025, 8, 27),
                Status = AbsenceStatus.Approved,
                ExportStatus = AbsenceExportStatus.Pending, // Should be included
                PayrollId = payroll.Id,
                Payroll = payroll
            }
        };

        context.Companies.Add(company);
        context.Users.Add(user);
        context.StructureLevels.Add(structureLevel);
        context.Employees.Add(employee);
        context.Payrolls.Add(payroll);
        context.Absences.AddRange(absences);
        await context.SaveChangesAsync();

        var calendarService = new Mock<ICalendarService>(MockBehavior.Strict);
        var repository = new AbsencesRepository(factory, calendarService.Object);

        // Act
        var result = await repository.GetCompanyAbsencesForTRZAsync<Absence>(companyId, monthDate);

        // Assert
        Assert.NotNull(result);
        var resultList = result.ToList();
        Assert.Equal(2, resultList.Count); // Only 2 absences should be returned (excluding Exported)

        Assert.Contains(resultList, r => r.ExportStatus == AbsenceExportStatus.Pending);
        Assert.DoesNotContain(resultList, r => r.ExportStatus == AbsenceExportStatus.Exported);
    }

    [Fact]
    public async Task GetCompanyAbsencesForTRZAsync_ShouldFilterOutTRZExportedByDefault()
    {
        // Arrange
        var (factory, dbName, config) = CreateFactory();
        using var context = new WorkTimeApiDbContext(new DbContextOptionsBuilder<WorkTimeApiDbContext>().UseInMemoryDatabase(dbName).Options, config);

        var companyId = Guid.NewGuid();
        var employeeId = Guid.NewGuid();
        var monthDate = new DateTime(2025, 8, 15);

        var company = new Company { Id = companyId, Name = "Test Company", Bulstat = "B123" };
        var user = new User { Id = Guid.NewGuid(), Email = "<EMAIL>" };
        var structureLevel = new StructureLevel { Id = Guid.NewGuid(), Name = "SL1", Type = StructureLevelType.Department, CompanyId = companyId };
        var employee = new Employee { Id = employeeId, CompanyId = companyId, Company = company, UserId = user.Id, User = user };
        var payroll = new Payroll { Id = Guid.NewGuid(), CompanyId = companyId, Company = company, EmployeeId = employeeId, Employee = employee, StructureLevelId = structureLevel.Id, StructureLevel = structureLevel };

        var absences = new List<Absence>
        {
            new()
            {
                Id = Guid.NewGuid(),
                FromDate = new DateTime(2025, 8, 10),
                ToDate = new DateTime(2025, 8, 12),
                Status = AbsenceStatus.Approved,
                ExportStatus = AbsenceExportStatus.Pending, // Should be included
                PayrollId = payroll.Id,
                Payroll = payroll
            },
            new()
            {
                Id = Guid.NewGuid(),
                FromDate = new DateTime(2025, 8, 20),
                ToDate = new DateTime(2025, 8, 22),
                Status = AbsenceStatus.Approved,
                ExportStatus = AbsenceExportStatus.TRZExported, // Should be filtered out
                PayrollId = payroll.Id,
                Payroll = payroll
            },
            new()
            {
                Id = Guid.NewGuid(),
                FromDate = new DateTime(2025, 8, 25),
                ToDate = new DateTime(2025, 8, 27),
                Status = AbsenceStatus.Approved,
                ExportStatus = AbsenceExportStatus.Exported, // Should be filtered out
                PayrollId = payroll.Id,
                Payroll = payroll
            }
        };

        context.Companies.Add(company);
        context.Users.Add(user);
        context.StructureLevels.Add(structureLevel);
        context.Employees.Add(employee);
        context.Payrolls.Add(payroll);
        context.Absences.AddRange(absences);
        await context.SaveChangesAsync();

        var calendarService = new Mock<ICalendarService>(MockBehavior.Strict);
        var repository = new AbsencesRepository(factory, calendarService.Object);

        // Act
        var result = await repository.GetCompanyAbsencesForTRZAsync<Absence>(companyId, monthDate, includeExported: false);

        // Assert
        Assert.NotNull(result);
        var resultList = result.ToList();
        Assert.Single(resultList); // Only 1 absence should be returned (excluding both Exported and TRZExported)

        Assert.Contains(resultList, r => r.ExportStatus == AbsenceExportStatus.Pending);
        Assert.DoesNotContain(resultList, r => r.ExportStatus == AbsenceExportStatus.Exported);
        Assert.DoesNotContain(resultList, r => r.ExportStatus == AbsenceExportStatus.TRZExported);
    }

    [Fact]
    public async Task GetCompanyAbsencesForTRZAsync_WithIncludeExported_ShouldIncludeAllExportStatuses()
    {
        // Arrange
        var (factory, dbName, config) = CreateFactory();
        using var context = new WorkTimeApiDbContext(new DbContextOptionsBuilder<WorkTimeApiDbContext>().UseInMemoryDatabase(dbName).Options, config);

        var companyId = Guid.NewGuid();
        var employeeId = Guid.NewGuid();
        var monthDate = new DateTime(2025, 8, 15);

        var company = new Company { Id = companyId, Name = "Test Company", Bulstat = "B123" };
        var user = new User { Id = Guid.NewGuid(), Email = "<EMAIL>" };
        var structureLevel = new StructureLevel { Id = Guid.NewGuid(), Name = "SL1", Type = StructureLevelType.Department, CompanyId = companyId };
        var employee = new Employee { Id = employeeId, CompanyId = companyId, Company = company, UserId = user.Id, User = user };
        var payroll = new Payroll { Id = Guid.NewGuid(), CompanyId = companyId, Company = company, EmployeeId = employeeId, Employee = employee, StructureLevelId = structureLevel.Id, StructureLevel = structureLevel };

        var absences = new List<Absence>
        {
            new()
            {
                Id = Guid.NewGuid(),
                FromDate = new DateTime(2025, 8, 10),
                ToDate = new DateTime(2025, 8, 12),
                Status = AbsenceStatus.Approved,
                ExportStatus = AbsenceExportStatus.Pending,
                PayrollId = payroll.Id,
                Payroll = payroll
            },
            new()
            {
                Id = Guid.NewGuid(),
                FromDate = new DateTime(2025, 8, 20),
                ToDate = new DateTime(2025, 8, 22),
                Status = AbsenceStatus.Approved,
                ExportStatus = AbsenceExportStatus.TRZExported,
                PayrollId = payroll.Id,
                Payroll = payroll
            },
            new()
            {
                Id = Guid.NewGuid(),
                FromDate = new DateTime(2025, 8, 25),
                ToDate = new DateTime(2025, 8, 27),
                Status = AbsenceStatus.Approved,
                ExportStatus = AbsenceExportStatus.Exported,
                PayrollId = payroll.Id,
                Payroll = payroll
            }
        };

        context.Companies.Add(company);
        context.Users.Add(user);
        context.StructureLevels.Add(structureLevel);
        context.Employees.Add(employee);
        context.Payrolls.Add(payroll);
        context.Absences.AddRange(absences);
        await context.SaveChangesAsync();

        var calendarService = new Mock<ICalendarService>(MockBehavior.Strict);
        var repository = new AbsencesRepository(factory, calendarService.Object);

        // Act
        var result = await repository.GetCompanyAbsencesForTRZAsync<Absence>(companyId, monthDate, includeExported: true);

        // Assert
        Assert.NotNull(result);
        var resultList = result.ToList();
        Assert.Equal(2, resultList.Count);

        Assert.Contains(resultList, r => r.ExportStatus == AbsenceExportStatus.Pending);
        Assert.Contains(resultList, r => r.ExportStatus == AbsenceExportStatus.Exported);
        Assert.DoesNotContain(resultList, r => r.ExportStatus == AbsenceExportStatus.TRZExported);
    }

    [Fact]
    public async Task GetCompanyAbsencesForTRZAsync_ShouldFilterByDateRange()
    {
        // Arrange
        var (factory, dbName, config) = CreateFactory();
        using var context = new WorkTimeApiDbContext(new DbContextOptionsBuilder<WorkTimeApiDbContext>().UseInMemoryDatabase(dbName).Options, config);

        var companyId = Guid.NewGuid();
        var employeeId = Guid.NewGuid();
        var monthDate = new DateTime(2025, 8, 15);

        var company = new Company { Id = companyId, Name = "Test Company", Bulstat = "B123" };
        var user = new User { Id = Guid.NewGuid(), Email = "<EMAIL>" };
        var structureLevel = new StructureLevel { Id = Guid.NewGuid(), Name = "SL1", Type = StructureLevelType.Department, CompanyId = companyId };
        var employee = new Employee { Id = employeeId, CompanyId = companyId, Company = company, UserId = user.Id, User = user };
        var payroll = new Payroll { Id = Guid.NewGuid(), CompanyId = companyId, Company = company, EmployeeId = employeeId, Employee = employee, StructureLevelId = structureLevel.Id, StructureLevel = structureLevel };

        var absences = new List<Absence>
        {
            new()
            {
                Id = Guid.NewGuid(),
                FromDate = new DateTime(2025, 8, 5),
                ToDate = new DateTime(2025, 8, 7),
                Status = AbsenceStatus.Approved,
                ExportStatus = AbsenceExportStatus.Pending,
                PayrollId = payroll.Id,
                Payroll = payroll
            },
            new()
            {
                Id = Guid.NewGuid(),
                FromDate = new DateTime(2025, 8, 25),
                ToDate = new DateTime(2025, 8, 27),
                Status = AbsenceStatus.Approved,
                ExportStatus = AbsenceExportStatus.Pending,
                PayrollId = payroll.Id,
                Payroll = payroll
            },
            new()
            {
                Id = Guid.NewGuid(),
                FromDate = new DateTime(2025, 8, 25),
                ToDate = new DateTime(2025, 9, 2),
                Status = AbsenceStatus.Approved,
                ExportStatus = AbsenceExportStatus.Pending,
                PayrollId = payroll.Id,
                Payroll = payroll
            },
            new()
            {
                Id = Guid.NewGuid(),
                FromDate = new DateTime(2025, 7, 30),
                ToDate = new DateTime(2025, 8, 2), // Filtered out since FromDate is not in August
                Status = AbsenceStatus.Approved,
                ExportStatus = AbsenceExportStatus.Pending,
                PayrollId = payroll.Id,
                Payroll = payroll
            }
        };

        context.Companies.Add(company);
        context.Users.Add(user);
        context.StructureLevels.Add(structureLevel);
        context.Employees.Add(employee);
        context.Payrolls.Add(payroll);
        context.Absences.AddRange(absences);
        await context.SaveChangesAsync();

        var calendarService = new Mock<ICalendarService>(MockBehavior.Strict);
        var repository = new AbsencesRepository(factory, calendarService.Object);

        // Act
        var result = await repository.GetCompanyAbsencesForTRZAsync<Absence>(companyId, monthDate);

        // Assert
        Assert.NotNull(result);
        var resultList = result.ToList();
        Assert.Equal(3, resultList.Count);

        // Verify date filtering logic
        Assert.Contains(resultList, r => r.FromDate.Month == 8);
        Assert.Contains(resultList, r => r.ToDate.Month == 8);
        Assert.Contains(resultList, r => r.FromDate.Month == 8 && r.ToDate.Month == 9); // Cross-month absence
    }

    [Fact]
    public async Task GetCompanyAbsencesForTRZAsync_ShouldReturnEmptyWhenNoAbsences()
    {
        // Arrange
        var (factory, dbName, config) = CreateFactory();
        using var context = new WorkTimeApiDbContext(new DbContextOptionsBuilder<WorkTimeApiDbContext>().UseInMemoryDatabase(dbName).Options, config);

        var companyId = Guid.NewGuid();
        var monthDate = new DateTime(2025, 8, 15);

        var company = new Company { Id = companyId, Name = "Test Company", Bulstat = "B123" };
        context.Companies.Add(company);
        await context.SaveChangesAsync();

        var calendarService = new Mock<ICalendarService>(MockBehavior.Strict);
        var repository = new AbsencesRepository(factory, calendarService.Object);

        // Act
        var result = await repository.GetCompanyAbsencesForTRZAsync<Absence>(companyId, monthDate);

        // Assert
        Assert.NotNull(result);
        Assert.Empty(result);
    }
}
