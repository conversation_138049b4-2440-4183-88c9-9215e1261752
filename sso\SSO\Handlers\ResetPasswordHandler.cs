﻿using MediatR;
using SSO.Common.Requests;
using SSO.Connections.Interfaces;

namespace SSO.Handlers
{
    public class ResetPasswordHandler : IRequestHandler<ResetPasswordRequest, IResult>
    {
        private readonly IEmailsConnection _emailsConnection;

        public ResetPasswordHandler(IEmailsConnection emailsConnection)
        {
            _emailsConnection = emailsConnection;
        }

        public async Task<IResult> Handle(ResetPasswordRequest request, CancellationToken cancellationToken)
        {
            var response = await _emailsConnection.ResetPasswordAsync(request.ResetPasswordDTO);
            return response is not null && response.IsSuccessStatusCode ? Results.Ok() : Results.BadRequest();
        }
    }
}
