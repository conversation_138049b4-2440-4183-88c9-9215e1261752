import { Routes, Route } from "react-router-dom";
import ConfirmationEmailSent from "./features/authentication/ConfirmationEmailSent";
import ConfirmEmail from "./features/authentication/ConfirmEmail";
import PrivateRoute from "./features/authentication/ProtectedRoute";
import AuthenticationProfile from "./features/authentication/AuthenticationProfile";
import PublicRoute from "./features/authentication/PublicRoute";
import AuthContainer from "./features/authentication/AuthContainer";
import { AuthMode } from "./models/Enums/AuthMode";
import ForgottenPassword from "./features/authentication/ForgottenPassword";
import ChangeForgottenPassword from "./features/authentication/ChangeForgottenPassword";
import ChangePassword from "./features/authentication/ChangePassword";
import { WatchersIndex } from "./features/watchers/WatchersIndex";
import { CreateWatchHealthCheck } from "./features/watchers/CreateWatchHealthCheck";
import { EditWatchHealthCheck } from "./features/watchers/EditWatchHealthCheck";

const AppRouter = () => {
  return (
    <Routes>
      <Route path="/" element={<WatchersIndex />} />
      <Route
        path="/auth/registration"
        element={
          <PublicRoute>
            <AuthContainer initialAuthMode={AuthMode.Registration} />
          </PublicRoute>
        }
      />
      <Route
        path="/auth/login/:email?"
        element={
          <PublicRoute>
            <AuthContainer initialAuthMode={AuthMode.Login} />
          </PublicRoute>
        }
      />
      <Route
        path="/auth/confirmation-email-sent/:email"
        element={
          <PublicRoute>
            <ConfirmationEmailSent />
          </PublicRoute>
        }
      />
      <Route
        path="/auth/change-forgotten-password/:email/:code"
        element={
          <PublicRoute>
            <ChangeForgottenPassword />
          </PublicRoute>
        }
      />
      <Route
        path="/auth/forgotten-password/:email?"
        element={
          <PublicRoute>
            <ForgottenPassword />
          </PublicRoute>
        }
      />
      <Route
        path="/auth/confirm-email/:userId/:code"
        element={
          <PublicRoute>
            <ConfirmEmail />
          </PublicRoute>
        }
      />
      <Route
        path="/auth/change-password"
        element={
          <PrivateRoute>
            <ChangePassword />
          </PrivateRoute>
        }
      />
      <Route
        path="/auth/profile"
        element={
          <PrivateRoute>
            <AuthenticationProfile />
          </PrivateRoute>
        }
      />
      <Route
        path="/watch/health-check/create"
        element={
          <PrivateRoute>
            <CreateWatchHealthCheck />
          </PrivateRoute>
        }
      />
      <Route
        path="/watch/health-check/edit"
        element={
          <PrivateRoute>
            <EditWatchHealthCheck />
          </PrivateRoute>
        }
      />
    </Routes>
  );
};

export default AppRouter;
