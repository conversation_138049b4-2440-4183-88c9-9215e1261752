﻿using SSO.Connections.Interfaces;
using SSO.Models;
using System.Text.Json;

namespace SSO.Connections
{
    public class GoogleValidatorConnection : IGoogleValidatorConnection
    {
        private readonly HttpClient _httpClient;
        private readonly IConfiguration _configuration;
        private const string GOOGLE_API_URL = "https://www.googleapis.com/oauth2/v1/userinfo?access_token=";
        private const string RECAPTCHA_VALIDATION_API_URL = "https://www.google.com/recaptcha/api/siteverify";
        private readonly string _googleClientId;
        private readonly string _googleClientSecret;
        private readonly string _googleRedirectUrl;

        public GoogleValidatorConnection(HttpClient httpClient, IConfiguration configuration)
        {
            _httpClient = httpClient;
            _configuration = configuration;
            _googleClientId = configuration["GoogleAuth:CLIENT_ID"] ?? "";
            _googleClientSecret = configuration["GoogleAuth:CLIENT_SECRET"] ?? "";
            _googleRedirectUrl = configuration["GoogleAuth:REDIRECT_URL"] ?? "";
        }

        public async Task<HttpResponseMessage> ValidateGoogleTokenAsync(string token)
        {
            var accessToken = await ExchangeAuthorizationCodeForTokenAsync(token);

            return await _httpClient.GetAsync($"{GOOGLE_API_URL}{accessToken}");
        }

        public async Task<string?> ExchangeAuthorizationCodeForTokenAsync(string authorizationCode)
        {
            var requestData = new Dictionary<string, string>
            {
                { "code", authorizationCode },
                { "client_id", _googleClientId },
                { "client_secret", _googleClientSecret },
                { "redirect_uri",  _googleRedirectUrl },
                { "grant_type", "authorization_code" }
            };

            var requestBody = new FormUrlEncodedContent(requestData);

            var response = await _httpClient.PostAsync("https://oauth2.googleapis.com/token", requestBody);

            if (!response.IsSuccessStatusCode)
            {
                throw new Exception($"Failed to exchange token. Status code: {response.StatusCode}");
            }

            var responseContent = await response.Content.ReadAsStringAsync();
            var tokenResponse = JsonSerializer.Deserialize<GoogleTokenResponse>(responseContent);
            if (tokenResponse is null)
                return null;

            return tokenResponse.AccessToken;
        }

        public Task<HttpResponseMessage> ValidateRecaptchaToken(string token)
        {
            var recaptchaSecret = _configuration["RecaptchaSecret"] ?? "";

            var validationRequest = new RecaptchaValidationRequestDTO
            {
                Response = token,
                Secret= recaptchaSecret
            };

            return _httpClient.PostAsJsonAsync(RECAPTCHA_VALIDATION_API_URL, validationRequest);
        }
    }
}
