﻿using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Http.HttpResults;
using Moq;
using SSO.Common.DTOs;
using SSO.Common.Requests;
using SSO.Connections.Interfaces;
using SSO.Handlers;
using SSO.Models;
using SSO.Services.Interfaces;
using System.Net;

namespace SSO.Tests.SSO.Handlers
{
    public class GoogleLoginHandlerTests
    {
        private readonly Mock<IUserRegistrationsConnection> _userRegistrationsConnectionMock;
        private readonly Mock<IJwtGeneratorService> _jwtGeneratorServiceMock;
        private readonly Mock<IRefreshTokensService> _refreshTokensServiceMock;
        private readonly Mock<IGoogleService> _googleServiceMock;
        private readonly GoogleLoginHandler _googleLoginHandler;

        public GoogleLoginHandlerTests()
        {
            _userRegistrationsConnectionMock = new Mock<IUserRegistrationsConnection>();
            _jwtGeneratorServiceMock = new Mock<IJwtGeneratorService>();
            _refreshTokensServiceMock = new Mock<IRefreshTokensService>();
            _googleServiceMock = new Mock<IGoogleService>();

            _googleLoginHandler = new GoogleLoginHandler(
                _userRegistrationsConnectionMock.Object,
                _jwtGeneratorServiceMock.Object,
                _refreshTokensServiceMock.Object,
                _googleServiceMock.Object);
        }

        [Fact]
        public async Task Handle_NullHttpRequest_ReturnsBadRequest()
        {
            // Arrange
            var request = new GoogleLoginRequest { HttpRequest = null };

            // Act
            var result = await _googleLoginHandler.Handle(request, default);

            // Assert
            Assert.IsType<BadRequest>(result);
        }

        [Fact]
        public async Task Handle_InvalidGoogleToken_ReturnsBadRequest()
        {
            // Arrange
            var httpRequest = new DefaultHttpContext().Request;
            httpRequest.Headers["Authorization"] = "GoogleToken";
            var request = new GoogleLoginRequest { HttpRequest = httpRequest };

            _googleServiceMock.Setup(x => x.GetGoogleDataAsync(It.IsAny<string>())).ReturnsAsync(null as GoogleDTO);

            // Act
            var result = await _googleLoginHandler.Handle(request, default);

            // Assert
            Assert.IsType<BadRequest>(result);
        }

        [Fact]
        public async Task Handle_NullResponseFromUserRegistrationsConnection_ReturnsBadRequest()
        {
            // Arrange
            var httpRequest = new DefaultHttpContext().Request;
            httpRequest.Headers["Authorization"] = "GoogleToken";
            var request = new GoogleLoginRequest { HttpRequest = httpRequest };

            var googleDTO = new GoogleDTO
            {
                Email = "<EMAIL>",
                GivenName = "John",
                FamilyName = "Doe"
            };

            _googleServiceMock.Setup(x => x.GetGoogleDataAsync(It.IsAny<string>())).ReturnsAsync(googleDTO);
            _userRegistrationsConnectionMock.Setup(x => x.TryGetUserOrCreateItAsync(It.IsAny<UserDTO>())).ReturnsAsync((HttpResponseMessage?)null);

            // Act
            var result = await _googleLoginHandler.Handle(request, default);

            // Assert
            Assert.IsType<BadRequest>(result);
        }

        [Fact]
        public async Task Handle_ValidUserId_AddsAuthorizationAndRefreshTokenHeaders()
        {
            // Arrange
            var userId = Guid.NewGuid().ToString();
            var accessToken = "access_token";
            var refreshToken = "refresh_token";

            var httpRequest = new DefaultHttpContext().Request;
            httpRequest.Headers["Authorization"] = "GoogleToken";
            var request = new GoogleLoginRequest { HttpRequest = httpRequest };

            var googleDTO = new GoogleDTO
            {
                Email = "<EMAIL>",
                GivenName = "John",
                FamilyName = "Doe"
            };

            _googleServiceMock.Setup(x => x.GetGoogleDataAsync(It.IsAny<string>())).ReturnsAsync(googleDTO);

            var httpResponse = new HttpResponseMessage(HttpStatusCode.OK)
            {
                Content = new StringContent(userId)
            };

            _userRegistrationsConnectionMock.Setup(x => x.TryGetUserOrCreateItAsync(It.IsAny<UserDTO>())).ReturnsAsync(httpResponse);
            _jwtGeneratorServiceMock.Setup(x => x.GenerateAccessTokenAsync(It.IsAny<UserDTO>())).ReturnsAsync(accessToken);
            _jwtGeneratorServiceMock.Setup(x => x.GenerateRefreshTokenAsync(It.IsAny<UserDTO>())).ReturnsAsync(refreshToken);

            // Act
            var result = await _googleLoginHandler.Handle(request, default);

            // Assert
            Assert.IsType<Ok<UserDTO>>(result);

            var response = httpRequest.HttpContext.Response;
            Assert.True(response.Headers.ContainsKey("Authorization"));
            Assert.Equal($"Bearer {accessToken}", response.Headers["Authorization"].FirstOrDefault());

            Assert.True(response.Headers.ContainsKey("Refresh-Token"));
            Assert.Equal(refreshToken, response.Headers["Refresh-Token"].FirstOrDefault());
        }
    }
}
