import { render, fireEvent, screen } from "@testing-library/react";
import Datepicker from "../Datepicker";
import "@testing-library/jest-dom";

describe("Datepicker", () => {
  test("renders Textbox with correct label", () => {
    render(<Datepicker onSelectDate={() => {}} />);
    expect(screen.getByTestId("datepicker-input-element")).toBeInTheDocument();
  });

  test("displays the month element when handleDatePickerClick is triggered", () => {
    render(<Datepicker onSelectDate={() => {}} />);
    fireEvent.click(screen.getByRole("button"));
    expect(screen.getByTestId("months-container")).toBeInTheDocument();
  });

  test("transitions to months view when month container is clicked", () => {
    render(<Datepicker onSelectDate={() => {}} />);
    fireEvent.click(screen.getByRole("button"));
    fireEvent.click(screen.getByText("Яну"));
    expect(screen.getByTestId("months-container")).toBeInTheDocument();
  });

  test("navigates through months and years", () => {
    render(<Datepicker onSelectDate={() => {}} />);
    const button = screen.getByRole("button");
    fireEvent.click(button);

    const monthContainer = screen.getByTestId("selected-month");
    const yearContainer = screen.getByTestId("selected-year");

    const prevMonthButton = screen.getAllByText("‹");
    const nextMonthButton = screen.getAllByText("›");

    expect(monthContainer).toBeVisible();

    fireEvent.click(monthContainer);
    expect(yearContainer).toBeVisible();

    fireEvent.click(yearContainer);
    expect(monthContainer).toBeVisible();

    const initialMonthText = monthContainer.textContent;
    fireEvent.click(nextMonthButton[0]);

    expect(monthContainer.textContent).not.toEqual(initialMonthText);

    fireEvent.click(prevMonthButton[0]);
    expect(monthContainer.textContent).toEqual(initialMonthText);
  });

  test("formats input value correctly on enter key press", () => {
    render(<Datepicker onSelectDate={() => {}} />);

    const input = screen.getByTestId("datepicker-input-element");

    fireEvent.change(input, { target: { value: "121122" } });
    fireEvent.keyDown(input, { key: "Enter", code: "Enter" });

    expect((input as unknown as any).value).toBe("12/11/2022");
  });

  test("executes onSelectDate callback with correct date when date selected", () => {
    const mockCallback = jest.fn();
    render(<Datepicker onSelectDate={mockCallback} />);

    const dateElement = screen.getByText("15");
    fireEvent.click(dateElement);

    expect(mockCallback).toHaveBeenCalledWith(expect.any(Date));
    expect(mockCallback).toHaveBeenCalledTimes(1);
  });
});
