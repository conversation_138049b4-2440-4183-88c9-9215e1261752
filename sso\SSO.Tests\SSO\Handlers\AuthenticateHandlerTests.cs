﻿using Microsoft.AspNetCore.Http.HttpResults;
using Moq;
using SSO.Handlers;
using SSO.Common.Requests;
using Microsoft.AspNetCore.Http;
using System.Security.Claims;
using SSO.Common.DTOs;
using SSO.Services.Interfaces;

namespace SSO.Tests.SSO.Handlers
{
    public class AuthenticateHandlerTests
    {
        private const string USER_GUID_STR = "4c34a6a8-4b6a-496c-bcc1-5a249d487b91";

        [Fact]
        public async Task Handle_ShouldReturnUserDto_WhenRequestIsValid()
        {
            // Arrange
            var mockPrincipalExtensionsWrapper = new Mock<IPrincipalExtensionsWrapper>();
            var testEmail = "<EMAIL>";
            mockPrincipalExtensionsWrapper.Setup(x => x.FindFirstValue(It.IsAny<ClaimsPrincipal>(), "UserId")).Returns(USER_GUID_STR);
            mockPrincipalExtensionsWrapper.Setup(x => x.FindFirstValue(It.IsAny<ClaimsPrincipal>(), ClaimTypes.Email)).Returns(testEmail);

            var authenticationService = new AuthenticateHandler(mockPrincipalExtensionsWrapper.Object);

            var mockHttpRequest = new Mock<HttpRequest>();
            var mockHttpContext = new Mock<HttpContext>();
            var mockUser = new Mock<ClaimsPrincipal>();
            mockHttpContext.Setup(x => x.User).Returns(mockUser.Object);
            mockHttpRequest.Setup(x => x.HttpContext).Returns(mockHttpContext.Object);

            var request = new AuthenticateRequest { HttpRequest = mockHttpRequest.Object };

            // Act
            var result = await authenticationService.Handle(request, CancellationToken.None);

            // Assert
            var okResult = (Ok<UserDTO>)result;
            Assert.IsType<Ok<UserDTO>>(okResult);
            Assert.IsType<UserDTO>(okResult.Value);
            var userDto = okResult.Value;
            Assert.Equal(USER_GUID_STR, userDto.Id.ToString());
            Assert.Equal(testEmail, userDto.Email);
        }

        [Fact]
        public async Task Handle_ShouldReturnBadRequest_WhenHttpRequestIsNull()
        {
            // Arrange
            var mockPrincipalExtensionsWrapper = new Mock<IPrincipalExtensionsWrapper>();
            var authenticationService = new AuthenticateHandler(mockPrincipalExtensionsWrapper.Object);

            var request = new AuthenticateRequest { HttpRequest = null };

            // Act
            var result = await authenticationService.Handle(request, CancellationToken.None);

            // Assert
            Assert.IsType<BadRequest>(result);
        }
    }
}
