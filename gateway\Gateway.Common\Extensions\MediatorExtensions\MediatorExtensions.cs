﻿using Gateway.Common.Requests.Interfaces;
using MediatR;
using Microsoft.AspNetCore.Builder;
using Microsoft.AspNetCore.Http;

namespace Gateway.Common.Extenstions.MediatorExtensions
{
    public static class MediatorExtensions
    {
        public static WebApplication MediateGet<TRequest>(
            this WebApplication app,
            string template) where TRequest : IHttpRequest
        {
            app.MapGet(template, async (IMediator mediator,
                HttpRequest httpRequest,
                [AsParameters] TRequest request) =>
            {
                request.HttpRequest = httpRequest;
                return await mediator.Send(request);
            });

            return app;
        }

        public static WebApplication MediatePost<TRequest>(
            this WebApplication app,
            string template) where TRequest : IHttpRequest
        {
            app.MapPost(template, async (IMediator mediator,
                HttpRequest httpRequest,
                TRequest request) =>
            {
                request.HttpRequest = httpRequest;
                return await mediator.Send(request);
            });

            return app;
        }

        public static WebApplication MediatePut<TRequest>(
            this WebApplication app,
            string template) where TRequest : IHttpRequest
        {
            app.MapPut(template, async (IMediator mediator,
                HttpRequest httpRequest,
                TRequest request) =>
            {
                request.HttpRequest = httpRequest;
                return await mediator.Send(request);
            });

            return app;
        }

        public static WebApplication MediateDelete<TRequest>(
            this WebApplication app,
            string template) where TRequest : IHttpRequest
        {
            app.MapDelete(template, async (IMediator mediator,
                HttpRequest httpRequest,
                [AsParameters] TRequest request) =>
            {
                request.HttpRequest = httpRequest;
                return await mediator.Send(request);
            });

            return app;
        }
    }
}
