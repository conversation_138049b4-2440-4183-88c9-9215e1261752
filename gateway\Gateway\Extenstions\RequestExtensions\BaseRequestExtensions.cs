﻿using Gateway.Common.Requests;

namespace Gateway.Extenstions.RequestExtensions
{
    public static class BaseRequestExtensions
    {
        public static void AddResponseAuthorizationHeaders(this BaseRequest request, HttpResponseMessage response)
        {
            if (response.Headers.TryGetValues("Authorization", out var authorizationToken))
                request?.HttpRequest?.HttpContext.Response.Headers.Add("Authorization", authorizationToken?.FirstOrDefault());

            if (response.Headers.TryGetValues("Refresh-Token", out var refreshToken))
                request?.HttpRequest?.HttpContext.Response.Headers.Add("Refresh-Token", refreshToken?.FirstOrDefault());
        }
    }
}
