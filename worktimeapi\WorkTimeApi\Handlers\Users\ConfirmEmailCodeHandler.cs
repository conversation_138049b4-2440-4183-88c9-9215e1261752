using MediatR;
using WorkTimeApi.Common.Requests.Users;
using WorkTimeApi.Services.Interfaces.Users;

namespace WorkTimeApi.Handlers.Users
{
    public class ConfirmEmailCodeHandler(IUserService userService) : IRequestHandler<ConfirmEmailCodeRequest, IResult>
    {
        public async Task<IResult> Handle(ConfirmEmailCodeRequest request, CancellationToken cancellationToken)
        {
            return await userService.ConfirmEmailCodeAsync(request.Email);
        }
    }
}
