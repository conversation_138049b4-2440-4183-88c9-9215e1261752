pool:
  vmImage: 'windows-latest'
  name: 'Jurassic'


variables:
  solution: '**/*.sln'
  buildPlatform: 'Any CPU'
  buildConfiguration: 'Release'

steps:
- task: UseDotNet@2
  inputs:
    packageType: 'sdk'
    version: '8.x'
    installationPath: $(Agent.ToolsDirectory)/dotnet

- task: NuGetToolInstaller@1

# - task: NuGetCommand@2
#   displayName: 'NuGet Restore - First Attempt'
#   inputs:
#     restoreSolution: '$(solution)'
#     command: 'restore'
#     feedsToUse: 'config'
#     nugetConfigPath: 'NuGet.config'
#   continueOnError: true  # Allow the pipeline to continue if this task fails

# - task: PowerShell@2
#   displayName: 'Check NuGet Restore Status'
#   inputs:
#     targetType: 'inline'
#     script: |
#       if ($env:AGENT_JOBSTATUS -ne "Succeeded") {
#         Write-Host "##vso[task.setvariable variable=RestoreRetryRequired;isOutput=true]true"
#       }
#   continueOnError: true
#   env:
#     AGENT_JOBSTATUS: $(Agent.JobStatus)

# - script: nuget locals all -clear
#   displayName: 'Clear NuGet Cache'
#   condition: and(succeededOrFailed(), eq(variables['RestoreRetryRequired'], 'true'))  # Only run if the previous restore failed

# - task: NuGetCommand@2
#   displayName: 'NuGet Restore - Retry after Cache Clear'
#   inputs:
#     restoreSolution: '$(solution)'
#     command: 'restore'
#     feedsToUse: 'config'
#     nugetConfigPath: 'NuGet.config'
#   condition: and(succeededOrFailed(), eq(variables['RestoreRetryRequired'], 'true'))  # Only run if the previous restore failed

- task: MSBuild@1
  inputs:
    solution: '**/*.sln'
    platform: 'Any CPU'
    configuration: 'Release'

- script: dotnet test --configuration $(buildConfiguration) $(Build.Repository.Name).Tests/$(Build.Repository.Name).Tests.csproj
  displayName: 'Run .NET Tests'
