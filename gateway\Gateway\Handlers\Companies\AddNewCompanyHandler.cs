﻿using AutoMapper;
using Gateway.Common.Globals;
using Gateway.Common.Redis.Models;
using Gateway.Common.Redis.Services.Interfaces;
using Gateway.Common.ResponseObjects;
using Gateway.Common.Results;
using Gateway.Connections.Interfaces;
using Gateway.Requests.UserRegistration;
using MediatR;
using Microinvest.TransferFiles.Tools.Models.Companies;
using WorkTimeApi.Common.Enums;
using WorkTimeApi.Common.EnumUtilities.Validation;
using WorkTimeApi.Common.Requests.Companies;
using HR = WorkTimeApi.Common.Requests.Companies;

namespace Gateway.Handlers.Companies
{
    public class AddNewCompanyHandler(IWorkTimeApiConnection workTimeApiConnection, 
        IUserRegistrationConnection userRegistrationConnection,
        IRedisService<RedisCompanyDTO> companiesRedisService,
        IMapper mapper, 
        GlobalUser globalUser) : IRequestHandler<CreateNewCompanyRequest, IResult>
    {
        public async Task<IResult> Handle(CreateNewCompanyRequest request, CancellationToken cancellationToken)
        {
            var response = await userRegistrationConnection.CreateNewCompanyAsync(mapper.Map<CompanyDTO>(request));

            if (!response.IsSuccessStatusCode)
                return Results.StatusCode((int)response.StatusCode);

            var companyIdText = await response.Content.ReadAsStringAsync(cancellationToken);
            if (!int.TryParse(companyIdText, out int companyID) || companyID == -1)
                return Results.Conflict(new ValidationResult([(int)CompanyValidationErrorsEnum.EmployeeCompanyAlreadyExists]));

            var senderaCompany = companiesRedisService.GetByKey(companyID);
            if (senderaCompany is null)
                return Results.NoContent();

            var companyRequest = new CreateCompanyRequest()
            {
                Name = senderaCompany.Name,
                Bulstat = senderaCompany.Bulstat,
                ContactName = string.IsNullOrEmpty(senderaCompany.ContactName) ? request.ContactName : senderaCompany.ContactName,
                UserRegistrationCompanyId = companyID
            };
            var workTimeApiResponse = await workTimeApiConnection.CreateCompanyAsync(companyRequest);
            if (!workTimeApiResponse.IsSuccessStatusCode)
                return Results.BadRequest();

            var companyDTO = await workTimeApiResponse.Content.ReadFromJsonAsync<WorktimeCompanyResponse>();

            if (companyDTO is null)
                return Results.NoContent();

            return Results.Ok(companyDTO);
        }
    }
}
