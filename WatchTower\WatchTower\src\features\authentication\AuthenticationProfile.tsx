import { MouseEvent, useContext } from "react";
import Button from "../../components/Inputs/Button";
import { logout } from "../../services/authentication/authenticationService";
import { AuthContext, User } from "./AuthContext";
import { useNavigate } from "react-router-dom";

const AuthenticationProfile = () => {
  const { setUser } = useContext(AuthContext);
  const navigate = useNavigate();

  const handleLogout = async (e: MouseEvent<HTMLButtonElement>) => {
    e.preventDefault();

    logout();
    setUser({} as User);
    navigate("/");
  };

  return (
    <>
      <Button label="strLogout" onClick={handleLogout} />
    </>
  );
};

export default AuthenticationProfile;
