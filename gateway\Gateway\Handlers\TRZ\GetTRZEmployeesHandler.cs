﻿using Gateway.Common.Results;
using Gateway.Connections.Interfaces;
using MediatR;
using WorkTimeApi.Common.Requests.TRZ;

namespace Gateway.Handlers.TRZ
{
    public class GetTRZEmployeesHandler : IRequestHandler<GetTRZEmployeesRequest, IResult>
    {
        private readonly IWorkTimeApiConnection _workTimeApiConnection;

        public GetTRZEmployeesHandler(IWorkTimeApiConnection workTimeApiConnection)
        {
            _workTimeApiConnection = workTimeApiConnection;
        }

        public async Task<IResult> Handle(GetTRZEmployeesRequest request, CancellationToken cancellationToken)
        {
            var response = await _workTimeApiConnection.GetTRZEmployeesAsync(request);

            return new HttpResponseMessageResult(response);
        }
    }
}
