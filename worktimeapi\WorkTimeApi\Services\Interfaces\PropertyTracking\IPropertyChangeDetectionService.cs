﻿using WorkTimeApi.Common.Enums;
using WorkTimeApi.Database.Models;

namespace WorkTimeApi.Services.Interfaces.PropertyTracking
{
    public interface IPropertyChangeDetectionService
    {
        List<EmployeePropertyEdit> DetectChanges<T>(T originalObject, T modifiedObject, Guid employeeId, Guid editorId, EditSource editSource, string objectName, Guid objectId) where T : class;

        List<EmployeePropertyEdit> DetectEmployeeWithSubObjectChanges(Employee originalEmployee, Employee modifiedEmployee, Guid editorId, EditSource editSource);
    }
}
