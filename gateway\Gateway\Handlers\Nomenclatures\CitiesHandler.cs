﻿using Gateway.Common.Results;
using Gateway.Connections.Interfaces;
using MediatR;
using WorkTimeApi.Common.Requests.Nomenclatures;

namespace Gateway.Handlers.Nomenclatures
{
    public class CitiesHandler : IRequestHandler<GetCitiesRequest, IResult>
    {
        private readonly IWorkTimeApiConnection _workTimeApiConnection;

        public CitiesHandler(IWorkTimeApiConnection workTimeApiConnection)
        {
            _workTimeApiConnection = workTimeApiConnection;
        }

        public async Task<IResult> Handle(GetCitiesRequest request, CancellationToken cancellationToken)
        {
            var cities = await _workTimeApiConnection.GetCitiesAsync();
            return new HttpResponseMessageResult(cities);
        }
    }
}