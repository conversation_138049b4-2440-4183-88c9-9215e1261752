﻿using Microinvest.TransferFiles.Tools.Models.Companies;
using Microinvest.TransferFiles.Tools.Models.Users;

namespace Gateway.Connections.Interfaces
{
	public interface IUserRegistrationConnection
	{
		Task<HttpResponseMessage> RegisterWithTemplateAsync(RegistrationWithTemplateDTO registrationWithTemplateDTO);

		Task<HttpResponseMessage> ConfirmEmailAsync(ConfirmEmailDTO confirmEmailDTO);

		Task<HttpResponseMessage> ApproveEmployeeForCompany(string email, string bulstat);

		Task<HttpResponseMessage> CreateNewCompanyAsync(CompanyDTO companyDTO);

		Task<HttpResponseMessage> ConfirmedRegisterEmployeeByEmailAsync(UserDTO userDTO);

        Task<HttpResponseMessage> GetSenderaCompanyUsersAsync(string email, string companyEIK);

		Task<HttpResponseMessage> GetSenderaUserAsync(string email);

		Task<HttpResponseMessage> EditCompanyAsync(CompanyDTO companyDTO);

		Task<HttpResponseMessage> ChangePasswordAsync(UserDTO userDTO);

		Task<HttpResponseMessage> GetSenderaUserByIdAsync(string userId);

        Task<HttpResponseMessage> EditUserDataAsync(UserDTO userDTO);

		Task<HttpResponseMessage> RegisterUserByCodeAsync(UserDTO userDTO);

		Task<HttpResponseMessage> GetUserByUserNameAsync(string userName);

		Task<HttpResponseMessage> GetIsUserValidAsync(string email, string password);

        Task<HttpResponseMessage> ConfirmEmailByCodeAsync(string email, string code);
    }
}
