﻿using System.Reflection;
using WorkTimeApi.Common.EnumUtilities;

namespace WorkTimeApi.Common.Extensions
{
    public static class EnumExtensions
    {
        public static IEnumerable<object> GetAllDescriptions<TEnum>(string lang = "bg") where TEnum : Enum
        {
            return Enum.GetValues(typeof(TEnum))
                .Cast<Enum>()
                .Select(e => new
                {
                    Identifier = Convert.ToInt32(e),
                    Name = e.GetDescription(lang)
                });
        }

        public static string GetDescription(this Enum value, string lang = "bg")
        {
            var field = value.GetType().GetField(value.ToString());
            var attr = field?.GetCustomAttribute<MultiLangDescriptionAttribute>();
            if (attr == null) return value.ToString();

            return lang.ToLower() switch
            {
                "en" => attr.En,
                "bg" => attr.Bg,
                _ => value.ToString()
            };
        }
    }
}
