﻿using System.Text.Json.Serialization;

namespace SSO.Models
{
    public class GoogleTokenResponse
    {
        [Json<PERSON>ropertyName("access_token")]
        public required string AccessToken { get; set; }

        [JsonPropertyName("token_type")]
        public required string TokenType { get; set; }

        [JsonPropertyName("expires_in")]
        public int ExpiresIn { get; set; }

        [Json<PERSON>ropertyName("refresh_token")]
        public required string RefreshToken { get; set; }

        [JsonPropertyName("scope")]
        public required string Scope { get; set; }
    }
}
