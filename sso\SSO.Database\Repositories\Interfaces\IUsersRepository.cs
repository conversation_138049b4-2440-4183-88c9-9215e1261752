﻿using SSO.Common.DTOs;
using SSO.Common.Responses;
using SSO.Database.Models;

namespace SSO.Database.Repositories.Interfaces
{
    public interface IUsersRepository
    {
        Task<User> AddUserAsync(User user);

        Task<User> UpdateUserAsnyc(User user);

        Task DeactivateAllTokensForUserAsync(User user);

        Task<User?> FindUserByIdAsync(Guid id);

        Task<User?> FindUserByEmailAsync(string email);

        Task<List<UserPermissionsDTO>> GetUserPermissionsAsync(Guid userId);

        Task<AddSupportChatPermissionsResult> AddSupportChatPermissionsAsync(User user);

        Task<List<User>> GetUsersByPermissionAsync(string permission);

        Task<List<string>> GetUserCompanyPermissionsAsync(Guid userId, int companyId);
    }
}
