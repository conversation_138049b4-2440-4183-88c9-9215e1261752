using MediatR;
using WorkTime.Notifications.Services.Interfaces;
using WorkTimeApi.Common;
using WorkTimeApi.Common.Enums;
using WorkTimeApi.Common.Notifications.Absences;
using WorkTimeApi.Database.Repositories.Interfaces.Companies;
using WorkTimeApi.Database.Repositories.Interfaces.Employees;
using WorkTimeApi.Services.Interfaces.Absences;
using WorkTimeApi.Services.Interfaces.Notifications;

namespace WorkTimeApi.Handlers.Notifications.Absences
{
    public class AbsenceEditedByAdminNotificationHandler(ISignalRNotificationService signalRNotificationService,
        INotificationsService notificationsService,
        IEmailsNotificationService emailsNotificationService,
        IEmployeesRepository employeesRepository,
        ICompaniesRepository companiesRepository,
        IAbsencesService absencesService,
        IConfiguration configuration) : INotificationHandler<AbsenceEditedByAdminNotification>
    {
        public async Task Handle(AbsenceEditedByAdminNotification notification, CancellationToken cancellationToken)
       {
            var approver = (await employeesRepository.FindAsync(e => e.UserId == notification.UserId && e.CompanyId == notification.CompanyId)).FirstOrDefault() ?? throw new Exception("Невалидно Employee!");
            var employee = await absencesService.GetEmployeeDTOByAbsenceIdAsync(notification.Payload.Id, notification.Payload.IsHospital) ?? throw new Exception("Íåâàëèäíî Employee!");

            var addNotifications = await notificationsService.AddNotificationByUserIdAndCompanyIdAsync(employee.WorkTimeId, notification);
       
            var emails = new List<string>() { employee.Email };

            var absence = notification.Payload ?? throw new Exception("Няма данни за заявени отсъствия");
            var absenceOld = notification.OldAbsence;
            var company = await companiesRepository.GetByIdAsync(notification.CompanyId) ?? throw new Exception("Невалидна компания!");
            var fullName = string.Join(" ", new[] { employee.FirstName, employee.LastName }
                           .Where(name => !string.IsNullOrWhiteSpace(name)));
            var approverName = string.Join(" ", new[] { approver.FirstName, approver.LastName }
                           .Where(name => !string.IsNullOrWhiteSpace(name)));

            var worktimeUrl = configuration.GetValue<string>("WorkTimeUrl") ?? throw new Exception("Невалидна настройка на WorkTimeUrl в appsettings!");

            var emailRequest = new
            {
                Name = fullName,
                ApproverName = approverName,
                CompanyName = company.Name,
                AbsenceType = absence.TypeIdentifier.GetDescription(),
                StartDate = absence.FromDate.ToString("dd.MM.yyyy"),
                EndDate = absence.ToDate.ToString("dd.MM.yyyy"),
                Comment = absence.Reference,
                AbsenceTypeOld = absenceOld.TypeIdentifier.GetDescription(),
                StartDateOld = absenceOld.FromDate.ToString("dd.MM.yyyy"),
                EndDateOld = absenceOld.ToDate.ToString("dd.MM.yyyy"),
                CommentOld = absenceOld.Reference,

                Url = $"{worktimeUrl}{notification.Url}"
            };

            var emailNotifications = emailsNotificationService.SendEmailsAsync(
                emails.Where(e => approver.Email == null || e != approver.Email),
                "absences/edited/admin",
                emailRequest);

            await signalRNotificationService.NotifyUser(employee.UserId, addNotifications);

            await Task.WhenAll(emailNotifications);
        }
    }
}