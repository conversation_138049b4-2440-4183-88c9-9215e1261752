﻿using Gateway.Common.Results;
using Gateway.Connections.Interfaces;
using MediatR;
using WorkTimeApi.Common.Requests.TRZ;

namespace Gateway.Handlers.TRZ
{
    public class AddTRZPendingEventsHandler : IRequestHandler<AddTRZPendingEventsRequest, IResult>
    {
        private readonly IWorkTimeApiConnection _workTimeApiConnection;

        public AddTRZPendingEventsHandler(IWorkTimeApiConnection workTimeApiConnection)
        {
            _workTimeApiConnection = workTimeApiConnection;
        }

        public async Task<IResult> Handle(AddTRZPendingEventsRequest request, CancellationToken cancellationToken)
        {
            var addеEventsResponse = await _workTimeApiConnection.AddTRZPendingEventsAsync(request);

            return new HttpResponseMessageResult(addеEventsResponse);
        }
    }
}
