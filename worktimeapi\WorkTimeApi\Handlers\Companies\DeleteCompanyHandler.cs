﻿using MediatR;
using WorkTimeApi.Common.DTOs.Companies;
using WorkTimeApi.Services.Interfaces.Companies;

namespace WorkTimeApi.Handlers.Companies
{
    public class DeleteCompanyHandler(ICompaniesService companiesService) : IRequestHandler<DeleteCompanyRequest, IResult>
    {
        public async Task<IResult> Handle(DeleteCompanyRequest request, CancellationToken cancellationToken)
        {
            await companiesService.DeleteCompanyAsync(request.CompanyId);
            return Results.Ok();
        }
    }
}
