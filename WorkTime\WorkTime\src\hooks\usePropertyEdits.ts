import { useCallback } from "react";
import { useMenu } from "../features/MenuContext";
import { ApproveEmployeePropertyEditDTO } from "../models/DTOs/editEmployee/ApproveEmployeeEditDTO";
import { DeclineEmployeePropertyEditDTO } from "../models/DTOs/editEmployee/DeclineEmployeeEditDTO";
import { EmployeePropertyEditDTO } from "../models/DTOs/editEmployee/EmployeePropertyEditDTO";
import {
  approveEmployeePropertyEdit,
  declineEmployeePropertyEdit,
} from "../services/employees/employeesService";
import { PropertyEditService } from "../services/employees/propertyEditService";

export interface UsePropertyEditsProps {
  propertyEdits: EmployeePropertyEditDTO[];
  shouldShowAdminEditButtons: boolean;

  payrollId?: string;
  employeeId?: string;
  onPropertyEditsUpdate?: (updatedEdits: EmployeePropertyEditDTO[]) => void;
  tabName?: string;
}

export interface PropertyEditInfo {
  hasEdit: boolean;
  isReadonly: boolean;
  pendingEdit: EmployeePropertyEditDTO | null;
  oldValue: string | null;
  newValue: string | null;
}

export const usePropertyEdits = ({
  propertyEdits,
  shouldShowAdminEditButtons,

  payrollId,
  employeeId,
  onPropertyEditsUpdate,
  tabName,
}: UsePropertyEditsProps) => {
  const { triggerEvent } = useMenu();
  const getPropertyEditInfo = useCallback(
    (
      objectName: string,
      objectId: string,
      propertyName: string
    ): PropertyEditInfo => {
      const hasEdit = PropertyEditService.hasEditForProperty(
        propertyEdits,
        objectName,
        objectId,
        propertyName
      );

      const pendingEdit = PropertyEditService.getPendingEditForProperty(
        propertyEdits,
        objectName,
        objectId,
        propertyName
      );

      const oldValue = PropertyEditService.getOldValueForProperty(
        propertyEdits,
        objectName,
        objectId,
        propertyName
      );

      return {
        hasEdit,
        isReadonly: hasEdit && shouldShowAdminEditButtons,
        pendingEdit,
        oldValue,
        newValue: pendingEdit?.newValue || null,
      };
    },
    [propertyEdits, shouldShowAdminEditButtons]
  );

  const handleApproveProperty = useCallback(
    async (propertyEditId: string): Promise<void> => {
      try {
        const approveDTO = new ApproveEmployeePropertyEditDTO({
          propertyEditId,
          payrollId: payrollId || null,
          employeeId: employeeId || null,
          tabName: tabName,
        });

        await approveEmployeePropertyEdit(approveDTO);

        if (onPropertyEditsUpdate) {
          const updatedEdits = propertyEdits.filter(
            (edit) => edit.id !== propertyEditId
          );
          onPropertyEditsUpdate(updatedEdits);
        }

        triggerEvent("propertyEditUpdated");
      } catch (error) {
        console.error("Error approving property edit:", error);
        throw error;
      }
    },
    [payrollId, employeeId, triggerEvent, onPropertyEditsUpdate, propertyEdits]
  );

  const handleDeclineProperty = useCallback(
    async (propertyEditId: string): Promise<void> => {
      try {
        const declineDTO = new DeclineEmployeePropertyEditDTO({
          propertyEditId,
          payrollId: payrollId || null,
          employeeId: employeeId || null,
          tabName: tabName,
        });

        await declineEmployeePropertyEdit(declineDTO);

        if (onPropertyEditsUpdate) {
          const updatedEdits = propertyEdits.filter(
            (edit) => edit.id !== propertyEditId
          );
          onPropertyEditsUpdate(updatedEdits);
        }

        triggerEvent("propertyEditUpdated");
      } catch (error) {
        console.error("Error declining property edit:", error);
        throw error;
      }
    },
    [payrollId, employeeId, triggerEvent, onPropertyEditsUpdate, propertyEdits]
  );

  return {
    getPropertyEditInfo,
    handleApproveProperty,
    handleDeclineProperty,
  };
};
