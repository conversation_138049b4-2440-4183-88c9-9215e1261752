﻿using MediatR;
using WorkTime.Notifications.Services.Interfaces;
using WorkTimeApi.Common;
using WorkTimeApi.Common.Notifications.Absences;
using WorkTimeApi.Common.Enums;
using WorkTimeApi.Database.Repositories.Interfaces.Companies;
using WorkTimeApi.Services.Interfaces.Absences;
using WorkTimeApi.Services.Interfaces.Notifications;

namespace WorkTimeApi.Handlers.Notifications.Absences
{
    public class AbsenceConfirmedEditedByEmployeeHandler(ISignalRNotificationService signalRNotificationService,
        INotificationsService notificationsService,
        IEmailsNotificationService emailsNotificationService,
        ICompaniesRepository companiesRepository,
        IAbsencesService absencesService,
        IConfiguration configuration) : INotificationHandler<AbsenceConfirmedEditedByEmployeeNotification>
    {
        public async Task Handle(AbsenceConfirmedEditedByEmployeeNotification notification, CancellationToken cancellationToken)
        {
            var absence = notification.Payload;
            var absenceOld = notification.OldAbsence;

            var employee = await absencesService.GetEmployeeDTOByAbsenceIdAsync(absence.Id, absence.IsHospital)
                           ?? throw new Exception("Невалиден служител!");

            var employeesToNotify = await notificationsService.GetEmployeesToNotifyAsync(
                notification.CompanyId,
                NotificationsName.Absences.EditedConfirmedAbsenceByEmployee.Email
            );

            var company = await companiesRepository.GetByIdAsync(notification.CompanyId) ?? throw new Exception("Невалидна компания!");
            var fullName = string.Join(" ", new[] { employee.FirstName, employee.LastName }
                           .Where(name => !string.IsNullOrWhiteSpace(name)));

            var worktimeUrl = configuration.GetValue<string>("WorkTimeUrl") ?? throw new Exception("Невалидна настройка на WorkTimeUrl в appsettings!");

            var emailRequest = new
            {
                Name = fullName,
                CompanyName = company.Name,
                AbsenceType = absence.TypeIdentifier.GetDescription(),
                StartDate = absence.FromDate.ToString("dd.MM.yyyy"),
                EndDate = absence.ToDate.ToString("dd.MM.yyyy"),
                Comment = absence.Reference,
                AbsenceTypeOld = absenceOld.TypeIdentifier.GetDescription(),
                StartDateOld = absenceOld.FromDate.ToString("dd.MM.yyyy"),
                EndDateOld = absenceOld.ToDate.ToString("dd.MM.yyyy"),
                CommentOld = absenceOld.Reference,
                Url = $"{worktimeUrl}{notification.Url}"
            };

            var emails = employeesToNotify
                .Select(e => e.Email)
                .Where(e => !string.IsNullOrWhiteSpace(e))
                .ToList();

            var notificationTasks = employeesToNotify
                .Select(async emp =>
                {
                    var savedNotification = await notificationsService.AddNotificationByUserIdAndCompanyIdAsync(emp.WorkTimeId, notification);
                    await signalRNotificationService.NotifyUser(emp.UserId, savedNotification);
                });

            var emailNotifications = emailsNotificationService.SendEmailsAsync(emails, "absences/confirm-edit-byemployee", emailRequest);

            await Task.WhenAll(notificationTasks.Append(emailNotifications));
        }
    }
}
