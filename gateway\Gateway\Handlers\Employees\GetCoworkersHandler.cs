﻿using Gateway.Common.Globals;
using Gateway.Connections.Interfaces;
using Gateway.Models;
using Gateway.Models.Responses;
using MediatR;
using WorkTimeApi.Common.DTOs.Employees;
using WorkTimeApi.Common.Enums;
using WorkTimeApi.Common.Requests.Employees;

namespace Gateway.Handlers.Employees
{
    public class GetCoworkersHandler(IWorkTimeApiConnection workTimeApiConnection, GlobalUser globalUser) : IRequestHandler<GetCoworkersRequest, IResult>
    {
        public async Task<IResult> Handle(GetCoworkersRequest request, CancellationToken cancellationToken)
        {
            var employeesResponse = await workTimeApiConnection.LoadCoworkersAsync(new LoadCoworkersRequest { UserId = globalUser.Id });

            var employees = await employeesResponse.Content.ReadFromJsonAsync<List<EmployeeDTO>>(cancellationToken: cancellationToken) ?? [];

            var result = new GetCoworkersResponse();

            foreach (var employeeCompany in employees)
            {
                if (employeeCompany.Status == EmployeeCompanyStatus.CompanyToApprove)
                {
                    result.PendingEmployees.Add(employeeCompany);
                    continue;
                }
            }

            foreach (var companyEmployeesDTO in employees)
            {
                result.Coworkers.Add(new CoworkerDTO
                {
                    CompanyId = companyEmployeesDTO.CompanyId,
                    Employee = companyEmployeesDTO ?? new EmployeeDTO(),
                    Roles = [.. companyEmployeesDTO?.EmployeeRoles.Select(er => er.Role) ?? []]
                });
            }

            return Results.Ok(result);
        }
    }
}
