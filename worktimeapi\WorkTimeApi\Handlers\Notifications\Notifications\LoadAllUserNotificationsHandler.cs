﻿using MediatR;
using WorkTimeApi.Common.Requests.Notifications;
using WorkTimeApi.Services.Interfaces.Notifications;

namespace WorkTimeApi.Handlers.Notifications.Notifications
{
    public class LoadAllUserNotificationsHandler : IRequestHandler<GetAllUserNotifications, IResult>
    {
        private readonly INotificationsService _notificationsService;

        public LoadAllUserNotificationsHandler(INotificationsService notificationsService)
        {
            _notificationsService = notificationsService;
        }

        public async Task<IResult> Handle(GetAllUserNotifications request, CancellationToken cancellationToken)
        {
            return Results.Ok(await _notificationsService.GetAllUserNotifications(request.UserId));
        }
    }
}

