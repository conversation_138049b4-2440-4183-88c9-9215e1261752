import { EventType, EventTypeDescriptions } from "./EventType";

describe("EventType Descriptions", () => {
  describe("200 Range - Платен по други членове (обучение)", () => {
    test("ПлатенОбучениеЧл169Ал1 should have correct description", () => {
      expect(EventTypeDescriptions[EventType.ПлатенОбучениеЧл169Ал1]).toBe(
        "Платен отпуск за обучение по чл. 169, ал. 1 от КТ"
      );
    });

    test("ПлатенДържавенИзпитЧл169Ал3 should have correct description", () => {
      expect(EventTypeDescriptions[EventType.ПлатенДържавенИзпитЧл169Ал3]).toBe(
        "Платен отпуск за държавен изпит по чл. 169, ал. 3 от КТ"
      );
    });

    test("Платен<PERSON><PERSON>учнаСтепенЧл169Ал4 should have correct description", () => {
      expect(EventTypeDescriptions[EventType.ПлатенНаучнаСтепенЧл169Ал4]).toBe(
        "Платен отпуск за научна степен по чл. 169, ал. 4 от КТ"
      );
    });

    test("ПлатенКандидатстванеСредноУчилищеЧл170Ал1Т1 should have correct description", () => {
      expect(
        EventTypeDescriptions[
          EventType.ПлатенКандидатстванеСредноУчилищеЧл170Ал1Т1
        ]
      ).toBe(
        "Платен отпуск за кандидатстване в средно училище по чл. 170, ал. 1, т. 1 от КТ"
      );
    });

    test("ПлатенКандидатстванеУниверситетЧл170Ал1Т2 should have correct description", () => {
      expect(
        EventTypeDescriptions[
          EventType.ПлатенКандидатстванеУниверситетЧл170Ал1Т2
        ]
      ).toBe(
        "Платен отпуск за кандидатстване в университет по чл. 170, ал. 1, т. 2 от КТ"
      );
    });

    test("ПлатенСлужебенТворческиЧл161Ал1 should have correct description", () => {
      expect(
        EventTypeDescriptions[EventType.ПлатенСлужебенТворческиЧл161Ал1]
      ).toBe(
        "Платен отпуск за служебен творчески отпуск по чл. 161, ал. 1 от КТ"
      );
    });
  });

  describe("300 Range - Инцидентен отпуск", () => {
    test("ИнцидентенГражданскиБракЧл157Ал1Т1 should have correct description", () => {
      expect(
        EventTypeDescriptions[EventType.ИнцидентенГражданскиБракЧл157Ал1Т1]
      ).toBe("Платен отпуск за граждански брак по чл. 157, ал. 1, т. 1 от КТ");
    });

    test("ИнцидентенКръводаряванеЧл157Ал1Т2 should have correct description", () => {
      expect(
        EventTypeDescriptions[EventType.ИнцидентенКръводаряванеЧл157Ал1Т2]
      ).toBe("Платен отпуск за кръводаряване по чл. 157, ал. 1, т. 2 от КТ");
    });

    test("ИнцидентенСмъртРоднинаЧл157Ал1Т3 should have correct description", () => {
      expect(
        EventTypeDescriptions[EventType.ИнцидентенСмъртРоднинаЧл157Ал1Т3]
      ).toBe("Платен отпуск за смърт на роднина по чл. 157, ал. 1, т. 3 от КТ");
    });

    test("ИнцидентенЯвяванеВСъдаЧл157Ал1Т4 should have correct description", () => {
      expect(
        EventTypeDescriptions[EventType.ИнцидентенЯвяванеВСъдаЧл157Ал1Т4]
      ).toBe("Платен отпуск за явяване в съда по чл. 157, ал. 1, т. 4 от КТ");
    });

    test("ИнцидентенУчастиеВЗаседанияЧл157Ал1Т5 should have correct description", () => {
      expect(
        EventTypeDescriptions[EventType.ИнцидентенУчастиеВЗаседанияЧл157Ал1Т5]
      ).toBe(
        "Платен отпуск за участие в заседания по чл. 157, ал. 1, т. 5 от КТ"
      );
    });

    test("ИнцидентенДоброволецПриБедствияЧл157Ал1Т7 should have correct description", () => {
      expect(
        EventTypeDescriptions[
          EventType.ИнцидентенДоброволецПриБедствияЧл157Ал1Т7
        ]
      ).toBe(
        "Платен отпуск за доброволец при бедствия по чл. 157, ал. 1, т. 7 от КТ"
      );
    });

    test("ИнцидентенПрегледБременностИнВитроЧл157Ал2 should have correct description", () => {
      expect(
        EventTypeDescriptions[
          EventType.ИнцидентенПрегледБременностИнВитроЧл157Ал2
        ]
      ).toBe(
        "Платен отпуск за преглед бременност/ин-витро по чл. 157, ал. 2 от КТ"
      );
    });

    test("ИнцидентенСлужбаВРезервЧл158Ал1 should have correct description", () => {
      expect(
        EventTypeDescriptions[EventType.ИнцидентенСлужбаВРезервЧл158Ал1]
      ).toBe("Платен отпуск за служба в резерв по чл. 158, ал. 1 от КТ");
    });

    test("ИнцидентенДруг should have correct description", () => {
      expect(EventTypeDescriptions[EventType.ИнцидентенДруг]).toBe(
        "Платен отпуск по други чл от КТ"
      );
    });
  });

  describe("400 Range - Неплатени подвидове", () => {
    test("НеплатенКандидатстванеСредноУчилищеЧл170Ал2 should have correct description", () => {
      expect(
        EventTypeDescriptions[
          EventType.НеплатенКандидатстванеСредноУчилищеЧл170Ал2
        ]
      ).toBe(
        "Неплатен отпуск за кандидатстване в средно училище по чл. 170, ал. 2 от КТ"
      );
    });

    test("НеплатенКандидатстванеУниверситетЧл170Ал2 should have correct description", () => {
      expect(
        EventTypeDescriptions[
          EventType.НеплатенКандидатстванеУниверситетЧл170Ал2
        ]
      ).toBe(
        "Неплатен отпуск за кандидатстване в университет по чл. 170, ал. 2 от КТ"
      );
    });

    test("НеплатенПодготовкаЯвяванеИзпитЧл171Ал1Т1 should have correct description", () => {
      expect(
        EventTypeDescriptions[
          EventType.НеплатенПодготовкаЯвяванеИзпитЧл171Ал1Т1
        ]
      ).toBe(
        "Неплатен отпуск за подготовка явяване изпит по чл. 171, ал. 1, т. 1 от КТ"
      );
    });

    test("НеплатенДържавенИзпитДиплСУЧл171Ал1Т2 should have correct description", () => {
      expect(
        EventTypeDescriptions[EventType.НеплатенДържавенИзпитДиплСУЧл171Ал1Т2]
      ).toBe(
        "Неплатен отпуск за държавен изпит дипл СУ по чл. 171, ал. 1, т. 2 от КТ"
      );
    });

    test("НеплатенДържавенИзпитДиплВУЗЧл171Ал1Т3 should have correct description", () => {
      expect(
        EventTypeDescriptions[EventType.НеплатенДържавенИзпитДиплВУЗЧл171Ал1Т3]
      ).toBe(
        "Неплатен отпуск за държавен изпит дипл ВУЗ по чл. 171, ал. 1, т. 3 от КТ"
      );
    });

    test("НеплатенДисертацияЧл171Ал1Т4 should have correct description", () => {
      expect(
        EventTypeDescriptions[EventType.НеплатенДисертацияЧл171Ал1Т4]
      ).toBe("Неплатен отпуск за дисертация по чл. 171, ал. 1, т. 4 от КТ");
    });

    test("НеплатенСлужебенТворческиЧл161Ал1 should have correct description", () => {
      expect(
        EventTypeDescriptions[EventType.НеплатенСлужебенТворческиЧл161Ал1]
      ).toBe(
        "Неплатен отпуск за служебен творчески отпуск по чл. 161, ал. 1 от КТ"
      );
    });
  });

  describe("Range Validation", () => {
    test("All 200 range values should be between 201-206", () => {
      const range200Values = [
        EventType.ПлатенОбучениеЧл169Ал1,
        EventType.ПлатенДържавенИзпитЧл169Ал3,
        EventType.ПлатенНаучнаСтепенЧл169Ал4,
        EventType.ПлатенКандидатстванеСредноУчилищеЧл170Ал1Т1,
        EventType.ПлатенКандидатстванеУниверситетЧл170Ал1Т2,
        EventType.ПлатенСлужебенТворческиЧл161Ал1,
      ];

      range200Values.forEach((value) => {
        expect(value).toBeGreaterThanOrEqual(201);
        expect(value).toBeLessThanOrEqual(206);
      });
    });

    test("All 300 range values should be between 301-309", () => {
      const range300Values = [
        EventType.ИнцидентенГражданскиБракЧл157Ал1Т1,
        EventType.ИнцидентенКръводаряванеЧл157Ал1Т2,
        EventType.ИнцидентенСмъртРоднинаЧл157Ал1Т3,
        EventType.ИнцидентенЯвяванеВСъдаЧл157Ал1Т4,
        EventType.ИнцидентенУчастиеВЗаседанияЧл157Ал1Т5,
        EventType.ИнцидентенДоброволецПриБедствияЧл157Ал1Т7,
        EventType.ИнцидентенПрегледБременностИнВитроЧл157Ал2,
        EventType.ИнцидентенСлужбаВРезервЧл158Ал1,
        EventType.ИнцидентенДруг,
      ];

      range300Values.forEach((value) => {
        expect(value).toBeGreaterThanOrEqual(301);
        expect(value).toBeLessThanOrEqual(309);
      });
    });

    test("All 400 range values should be between 401-407", () => {
      const range400Values = [
        EventType.НеплатенКандидатстванеСредноУчилищеЧл170Ал2,
        EventType.НеплатенКандидатстванеУниверситетЧл170Ал2,
        EventType.НеплатенПодготовкаЯвяванеИзпитЧл171Ал1Т1,
        EventType.НеплатенДържавенИзпитДиплСУЧл171Ал1Т2,
        EventType.НеплатенДържавенИзпитДиплВУЗЧл171Ал1Т3,
        EventType.НеплатенДисертацияЧл171Ал1Т4,
        EventType.НеплатенСлужебенТворческиЧл161Ал1,
      ];

      range400Values.forEach((value) => {
        expect(value).toBeGreaterThanOrEqual(401);
        expect(value).toBeLessThanOrEqual(407);
      });
    });
  });

  describe("Description Completeness", () => {
    test("All EventType enum values should have corresponding descriptions", () => {
      const enumValues = Object.values(EventType).filter(
        (value) => typeof value === "number"
      ) as EventType[];

      enumValues.forEach((eventType) => {
        expect(EventTypeDescriptions[eventType]).toBeDefined();
        expect(EventTypeDescriptions[eventType]).not.toBe("");
      });
    });

    test("All descriptions should be non-empty strings", () => {
      Object.values(EventTypeDescriptions).forEach((description) => {
        expect(typeof description).toBe("string");
        expect(description.trim()).not.toBe("");
      });
    });
  });
});
