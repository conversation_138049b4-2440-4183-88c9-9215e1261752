﻿using Gateway.Common.Requests;

namespace WorkTimeApi.Common.Requests.Employees
{
    public class DeclineEmployeePropertyEditRequest : BaseRequest
    {
        public Guid? PropertyEditId { get; set; }

        public bool IsDecliningAllForEmployee { get; set; }

        public Guid? EmployeeId { get; set; }

        public Guid? PayrollId { get; set; }

        public string TabName { get; set; }
    }
}
