﻿// <auto-generated />
using System;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Infrastructure;
using Microsoft.EntityFrameworkCore.Metadata;
using Microsoft.EntityFrameworkCore.Migrations;
using Microsoft.EntityFrameworkCore.Storage.ValueConversion;
using SSO.Database;

#nullable disable

namespace SSO.Database.Migrations
{
    [DbContext(typeof(SSODbContext))]
    [Migration("20250507074531_AddedWorkTimeRole")]
    partial class AddedWorkTimeRole
    {
        /// <inheritdoc />
        protected override void BuildTargetModel(ModelBuilder modelBuilder)
        {
#pragma warning disable 612, 618
            modelBuilder
                .HasAnnotation("ProductVersion", "8.0.8")
                .HasAnnotation("Relational:MaxIdentifierLength", 128);

            SqlServerModelBuilderExtensions.UseIdentityColumns(modelBuilder);

            modelBuilder.Entity("PermissionRole", b =>
                {
                    b.Property<Guid>("PermissionsId")
                        .HasColumnType("uniqueidentifier");

                    b.Property<Guid>("RolesId")
                        .HasColumnType("uniqueidentifier");

                    b.HasKey("PermissionsId", "RolesId");

                    b.HasIndex("RolesId");

                    b.ToTable("PermissionRole");
                });

            modelBuilder.Entity("SSO.Database.Models.Permission", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uniqueidentifier");

                    b.Property<string>("Name")
                        .IsRequired()
                        .HasColumnType("nvarchar(max)");

                    b.HasKey("Id");

                    b.ToTable("Permissions");

                    b.HasData(
                        new
                        {
                            Id = new Guid("fa6fdc50-e8b4-0314-84cd-6e6cdcd2700e"),
                            Name = "Structure.Read"
                        },
                        new
                        {
                            Id = new Guid("5053acf7-2718-0f02-592a-1b32addf5972"),
                            Name = "Structure.Write"
                        },
                        new
                        {
                            Id = new Guid("b1815361-f2d2-d704-4129-f7ad010513ea"),
                            Name = "Employees.Read"
                        },
                        new
                        {
                            Id = new Guid("7dd10ffd-497d-c5ee-a3e4-42ca56a5480c"),
                            Name = "Employees.Write"
                        },
                        new
                        {
                            Id = new Guid("8a5e2a7d-6d34-bb72-5fc8-b8aeac9f024b"),
                            Name = "Employees.Delete"
                        },
                        new
                        {
                            Id = new Guid("b207f4fd-afc9-ca30-3900-01f04dd17662"),
                            Name = "Employees.Approve"
                        },
                        new
                        {
                            Id = new Guid("3b801a4a-f37c-8efb-49cf-faa0fbeb9ea9"),
                            Name = "Employees.Coworkerrs.Read"
                        },
                        new
                        {
                            Id = new Guid("269baed5-dd92-36bc-7fa8-f9c547fcec4d"),
                            Name = "Employees.Coworkerrs.Write"
                        },
                        new
                        {
                            Id = new Guid("1340cdab-a99c-ab23-0fb8-7bd4edcaeaf0"),
                            Name = "Attendances.Read"
                        },
                        new
                        {
                            Id = new Guid("79a3e9d2-999c-a677-c304-1054d8d29dc0"),
                            Name = "Attendances.Write"
                        },
                        new
                        {
                            Id = new Guid("8571a7bc-4a39-7b25-98a8-55ca4dcf3eed"),
                            Name = "Documents.Read"
                        },
                        new
                        {
                            Id = new Guid("621e7ab3-d58f-9d92-2b28-fbc5ebcdf6a2"),
                            Name = "Documents.Write"
                        },
                        new
                        {
                            Id = new Guid("bdc4a978-a9f4-66df-cace-520dd9889f13"),
                            Name = "SupportChatService.Chats"
                        },
                        new
                        {
                            Id = new Guid("38e30952-737d-3746-6792-0bea04e26f6d"),
                            Name = "SupportChatService.Users"
                        });
                });

            modelBuilder.Entity("SSO.Database.Models.RefreshToken", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uniqueidentifier");

                    b.Property<DateTime?>("DeactivatedDate")
                        .HasColumnType("datetime2");

                    b.Property<bool>("IsActive")
                        .HasColumnType("bit");

                    b.Property<string>("Token")
                        .IsRequired()
                        .HasColumnType("nvarchar(max)");

                    b.Property<Guid>("UserId")
                        .HasColumnType("uniqueidentifier");

                    b.HasKey("Id");

                    b.HasIndex("UserId");

                    b.ToTable("RefreshTokens");
                });

            modelBuilder.Entity("SSO.Database.Models.Role", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uniqueidentifier");

                    b.Property<string>("Name")
                        .IsRequired()
                        .HasColumnType("nvarchar(max)");

                    b.Property<int>("UserRegistrationCompanyId")
                        .HasColumnType("int");

                    b.HasKey("Id");

                    b.ToTable("Roles");

                    b.HasData(
                        new
                        {
                            Id = new Guid("611a6042-bf7f-f2dc-6213-265d9187bcab"),
                            Name = "SupportChatServiceRole",
                            UserRegistrationCompanyId = -10000
                        });
                });

            modelBuilder.Entity("SSO.Database.Models.RoleUser", b =>
                {
                    b.Property<Guid>("UsersId")
                        .HasColumnType("uniqueidentifier");

                    b.Property<Guid>("RolesId")
                        .HasColumnType("uniqueidentifier");

                    b.Property<int>("UserRegistrationCompanyId")
                        .HasColumnType("int");

                    b.HasKey("UsersId", "RolesId", "UserRegistrationCompanyId");

                    b.HasIndex("RolesId");

                    b.ToTable("RoleUser");
                });

            modelBuilder.Entity("SSO.Database.Models.User", b =>
                {
                    b.Property<Guid>("Id")
                        .HasColumnType("uniqueidentifier");

                    b.Property<string>("Email")
                        .IsRequired()
                        .HasColumnType("nvarchar(max)");

                    b.Property<Guid?>("WorkTimeRoleId")
                        .HasColumnType("uniqueidentifier");

                    b.HasKey("Id");

                    b.HasIndex("WorkTimeRoleId");

                    b.ToTable("Users");
                });

            modelBuilder.Entity("PermissionRole", b =>
                {
                    b.HasOne("SSO.Database.Models.Permission", null)
                        .WithMany()
                        .HasForeignKey("PermissionsId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.HasOne("SSO.Database.Models.Role", null)
                        .WithMany()
                        .HasForeignKey("RolesId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();
                });

            modelBuilder.Entity("SSO.Database.Models.RefreshToken", b =>
                {
                    b.HasOne("SSO.Database.Models.User", "User")
                        .WithMany("RefreshTokens")
                        .HasForeignKey("UserId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("User");
                });

            modelBuilder.Entity("SSO.Database.Models.RoleUser", b =>
                {
                    b.HasOne("SSO.Database.Models.Role", "Role")
                        .WithMany("UserRoles")
                        .HasForeignKey("RolesId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.HasOne("SSO.Database.Models.User", "User")
                        .WithMany("UserRoles")
                        .HasForeignKey("UsersId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("Role");

                    b.Navigation("User");
                });

            modelBuilder.Entity("SSO.Database.Models.User", b =>
                {
                    b.HasOne("SSO.Database.Models.Role", "WorkTimeRole")
                        .WithMany()
                        .HasForeignKey("WorkTimeRoleId");

                    b.Navigation("WorkTimeRole");
                });

            modelBuilder.Entity("SSO.Database.Models.Role", b =>
                {
                    b.Navigation("UserRoles");
                });

            modelBuilder.Entity("SSO.Database.Models.User", b =>
                {
                    b.Navigation("RefreshTokens");

                    b.Navigation("UserRoles");
                });
#pragma warning restore 612, 618
        }
    }
}
