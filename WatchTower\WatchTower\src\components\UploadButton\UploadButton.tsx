import React, { useState } from "react";
import Image from "../Image";
import styled from "styled-components";
import Label from "../Inputs/Label";
import uploadImage from "../../assets/images/button/upload.png";

const Wrapper = styled.div`
  position: relative;
  background-color: var(--upload-button-background-color);
  padding: 1rem 1rem 1rem 3.5rem;
  margin-top: 0.5rem;
  border-radius: 2rem;
`;

const Container = styled.label`
  cursor: pointer;
`;

const StyledInput = styled.input`
  display: none;
`;

const StyledLabel = styled(Label)`
  position: absolute;
  left: 1rem;
`;

const StyledImage = styled(Image)`
  position: absolute;
  left: 1rem;
  top: 20%;
  border-radius: 1rem;
`;

const UploadButton = () => {
  const [image, setImage] = useState({ preview: "", file: {} });

  const handleChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    if (e.target.files?.length) {
      setImage({
        preview: URL.createObjectURL(e.target.files[0]),
        file: e.target.files[0],
      });
    }
  };

  return (
    <Wrapper>
      <Container htmlFor="upload-button">
        <StyledLabel>Company logo</StyledLabel>
        {image.preview ? (
          <StyledImage src={image.preview} size="small" />
        ) : (
          <StyledImage src={uploadImage} size="small" title="Upload" />
        )}
      </Container>
      <StyledInput id="upload-button" type="file" onChange={handleChange} />
    </Wrapper>
  );
};

export default UploadButton;
