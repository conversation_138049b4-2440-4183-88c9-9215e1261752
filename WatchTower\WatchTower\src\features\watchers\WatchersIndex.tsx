import { useEffect, useState } from "react";
import { useAppDispatch, useAppSelector } from "../../app/hooks";
import {
  getActionsConnection,
  onGetAllWatchHealthChecks,
  selectWatchers,
} from "./watchersActions";
import { Accordion, Card, Spinner } from "react-bootstrap";
import { FontAwesomeIcon } from "@fortawesome/react-fontawesome";
import {
  faCheck,
  faX,
  faExclamation,
  faQuestion,
} from "@fortawesome/free-solid-svg-icons";
import { WatchHealthCheckDTO } from "../../models/DTOs/watchers/WatchHealthCheckDTO";
import { HealthStatusType } from "../../models/Enums/HealthStatusType";
import { HubConnection } from "@microsoft/signalr";
import { useNavigate } from "react-router-dom";

export const WatchersIndex = () => {
  const [connection, setConnection] = useState<HubConnection | undefined>();
  const dispatch = useAppDispatch();
  const watchersState = useAppSelector(selectWatchers);
  const navigate = useNavigate();

  const { watchHealthChecks: envWatchers, loading } = watchersState;

  useEffect(() => {
    if (connection === undefined) {
      getActionsConnection(dispatch).then((connection) =>
        setConnection(connection)
      );
    }

    if (envWatchers === undefined || Object.keys(envWatchers).length === 0) {
      dispatch(onGetAllWatchHealthChecks());
    }
  }, [dispatch, envWatchers]);

  const getHealthStatus = (watcher: WatchHealthCheckDTO): HealthStatusType => {
    const lastHealthCheck = watcher.healthChecks[0];

    if (lastHealthCheck === undefined || lastHealthCheck === null)
      return HealthStatusType.Unknown;

    switch (lastHealthCheck.status) {
      case "Healthy":
        return HealthStatusType.Healthy;
      case "Unhealthy":
        return HealthStatusType.Unhealthy;
      default:
        return HealthStatusType.Degrated;
    }
  };

  const getIconByHealthStatus = (healthStatus: HealthStatusType) => {
    switch (healthStatus) {
      case HealthStatusType.Healthy:
        return (
          <FontAwesomeIcon
            icon={faCheck}
            style={{ color: "#37ff00" }}
            size="2xl"
          />
        );
      case HealthStatusType.Unhealthy:
        return (
          <FontAwesomeIcon icon={faX} size="2xl" style={{ color: "#ff0000" }} />
        );
      case HealthStatusType.Degrated:
        return (
          <FontAwesomeIcon
            icon={faExclamation}
            size="2xl"
            style={{ color: "#ffea00" }}
          />
        );
      default:
        return (
          <FontAwesomeIcon
            icon={faQuestion}
            size="2xl"
            style={{ color: "#005eff" }}
          />
        );
    }
  };

  const handleWatcherClicked = (watcher: WatchHealthCheckDTO) => {
    navigate("/watch/health-check/edit", {
      state: {
        watchHealthCheckDTO: watcher,
      },
    });
  };

  if (loading || envWatchers === undefined) {
    return (
      <Spinner animation="border" role="status">
        <span className="visually-hidden">Loading...</span>
      </Spinner>
    );
  }

  return (
    <Accordion defaultActiveKey="0">
      {Object.keys(envWatchers).map((environment, index) => {
        const watcherGroup = [...envWatchers[environment]].sort((a, b) =>
          a.name.localeCompare(b.name)
        );

        return (
          <Accordion.Item eventKey={index.toString()}>
            <Accordion.Header>{environment}</Accordion.Header>
            <Accordion.Body>
              {watcherGroup.map((watcher) => {
                const status = getHealthStatus(watcher);

                return (
                  <Card
                    key={watcher.id}
                    style={{ margin: "1rem" }}
                    onClick={() => handleWatcherClicked(watcher)}
                  >
                    <Card.Header
                      style={{
                        display: "flex",
                        justifyContent: "space-between",
                      }}
                    >
                      <div>{watcher.name}</div>
                      <div>{getIconByHealthStatus(status)}</div>
                    </Card.Header>
                    <Card.Body>
                      <Card.Title>{watcher.url}</Card.Title>
                      <Card.Text>
                        <div>
                          PollingInterval: {watcher.pollingIntervalInSeconds}
                        </div>
                        <div>
                          Status: {watcher.healthChecks[0]?.status ?? "Unknown"}
                        </div>
                      </Card.Text>
                    </Card.Body>
                  </Card>
                );
              })}
            </Accordion.Body>
          </Accordion.Item>
        );
      })}
    </Accordion>
  );
};
