using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Configuration;
using WorkTimeApi.Common.Enums;
using WorkTimeApi.Database;
using WorkTimeApi.Database.Models;
using WorkTimeApi.Database.Models.TRZ;
using WorkTimeApi.Database.Repositories.TRZ;

namespace WorkTimeApi.Tests.Repositories.TRZ
{
    public class TRZRepositoryTests
    {
        private sealed class TestDbContextFactory(DbContextOptions<WorkTimeApiDbContext> options, IConfiguration configuration) : IDbContextFactory<WorkTimeApiDbContext>
        {
            public WorkTimeApiDbContext CreateDbContext() => new(options, configuration);

            public ValueTask<WorkTimeApiDbContext> CreateDbContextAsync(CancellationToken cancellationToken = default)
                => new(CreateDbContext());
        }

        private static IDbContextFactory<WorkTimeApiDbContext> CreateDbFactory(string dbName)
        {
            var configData = new Dictionary<string, string?>
            {
                { "WorkTimeDefaultCompanyId", "99999999-9999-9999-9999-************" }
            };
            var configuration = new ConfigurationBuilder().AddInMemoryCollection(configData).Build();

            var options = new DbContextOptionsBuilder<WorkTimeApiDbContext>()
                .UseInMemoryDatabase(databaseName: dbName)
                .EnableSensitiveDataLogging()
                .Options;

            return new TestDbContextFactory(options, configuration);
        }

        private static async Task SeedRequiredAsync(WorkTimeApiDbContext context, Guid companyId, Guid structureLevelId)
        {
            var company = new Company { Id = companyId, Name = "TestCo", Bulstat = "*********" };
            var user = new User { Id = Guid.NewGuid(), Email = "<EMAIL>" };
            var structureLevel = new StructureLevel { Id = structureLevelId, Name = "Dept", Type = StructureLevelType.Department, CompanyId = companyId };

            context.Companies.Add(company);
            context.Users.Add(user);
            context.StructureLevels.Add(structureLevel);
            await context.SaveChangesAsync();
        }

        [Fact]
        public async Task AddTRZEmployeesAsync_MergesEventsByIdAndAggregatesFields()
        {
            var dbName = Guid.NewGuid().ToString();
            var factory = CreateDbFactory(dbName);

            var companyId = Guid.Parse("11111111-1111-1111-1111-111111111111");
            var structureLevelId = Guid.Parse("*************-2222-2222-************");

            await using (var seedCtx = await factory.CreateDbContextAsync())
            {
                await SeedRequiredAsync(seedCtx, companyId, structureLevelId);

                var existingUserId = Guid.NewGuid();
                var existingUser = new User { Id = existingUserId, Email = "<EMAIL>" };
                seedCtx.Users.Add(existingUser);
                var existingEmployeeId = Guid.Parse("aaaaaaaa-aaaa-aaaa-aaaa-aaaaaaaaaaaa");
                seedCtx.Employees.Add(new Employee
                {
                    Id = existingEmployeeId,
                    CompanyId = companyId,
                    Company = seedCtx.Companies.First(c => c.Id == companyId),
                    UserId = existingUserId,
                    User = existingUser,
                });
                await seedCtx.SaveChangesAsync();
            }

            var repository = new TRZReporistory(factory);

            var employeeId = Guid.Parse("aaaaaaaa-aaaa-aaaa-aaaa-aaaaaaaaaaaa");

            var employee = new Employee
            {
                Id = employeeId,
                CompanyId = companyId,
                Company = null!,
                UserId = Guid.NewGuid(),
                User = null!,
                PendingPayrolls = new List<TRZPayroll>()
            };

            var payrollId = Guid.NewGuid();
            var eventId = Guid.NewGuid();

            var payroll = new TRZPayroll
            {
                Id = payrollId,
                CompanyId = companyId,
                Company = null!,
                EmployeeId = employeeId,
                Employee = null!,
                StructureLevelId = structureLevelId,
                StructureLevel = null!,
                Events = []
            };

            var e1 = new TRZEvent
            {
                Id = eventId,
                WorkTimePayrollId = payrollId,
                Payroll = payroll,
                EventType = TRZEventType.ПлатенПоДругиЧленове,
                StartDate = new DateTime(2025, 1, 10),
                EndDate = new DateTime(2025, 1, 12),
                Duration = 2,
                IsHospital = false
            };
            var e2 = new TRZEvent
            {
                Id = eventId,
                WorkTimePayrollId = payrollId,
                Payroll = payroll,
                EventType = TRZEventType.ПлатенПоДругиЧленове,
                StartDate = new DateTime(2025, 1, 5),
                EndDate = new DateTime(2025, 1, 15),
                Duration = 5,
                IsHospital = false
            };

            payroll.Events.Add(e1);
            payroll.Events.Add(e2);
            employee.PendingPayrolls.Add(payroll);

            var employees = new List<Employee> { employee };

            var result = await repository.AddTRZEmployeesAsync(employees, companyId);

            await using var assertCtx = await factory.CreateDbContextAsync();
            var dbEmployee = assertCtx.Employees
                .Include(e => e.PendingPayrolls)
                .ThenInclude(pp => pp.Events)
                .First(e => e.Id == employeeId);

            var dbPayroll = dbEmployee.PendingPayrolls.First(p => p.Id == payrollId);
            Assert.Single(dbPayroll.Events);

            var merged = dbPayroll.Events.First();
            Assert.Equal(eventId, merged.Id);
            Assert.Equal(new DateTime(2025, 1, 5), merged.StartDate);
            Assert.Equal(new DateTime(2025, 1, 15), merged.EndDate);
            Assert.Equal(7, merged.Duration);
            Assert.Equal(TRZEventType.ПлатенГодишенОтпуск, merged.EventType);
        }

        [Fact]
        public async Task AddTRZEmployeesAsync_MergesOnlyDuplicates_KeepsDistinctEvents()
        {
            var dbName = Guid.NewGuid().ToString();
            var factory = CreateDbFactory(dbName);

            Guid companyId = Guid.Parse("11111111-1111-1111-1111-111111111111");
            Guid structureLevelId = Guid.Parse("*************-2222-2222-************");
            Guid employeeId = Guid.Parse("bbbbbbbb-bbbb-bbbb-bbbb-bbbbbbbbbbbb");

            await using (var seedCtx = await factory.CreateDbContextAsync())
            {
                await SeedRequiredAsync(seedCtx, companyId, structureLevelId);
                var user = new User { Id = Guid.NewGuid(), Email = "<EMAIL>" };
                seedCtx.Users.Add(user);
                seedCtx.Employees.Add(new Employee
                {
                    Id = employeeId,
                    CompanyId = companyId,
                    Company = seedCtx.Companies.First(c => c.Id == companyId),
                    UserId = user.Id,
                    User = user,
                });
                await seedCtx.SaveChangesAsync();
            }

            var repository = new TRZReporistory(factory);

            var payrollId = Guid.NewGuid();
            var duplicateId = Guid.NewGuid();
            var uniqueId = Guid.NewGuid();

            var payroll = new TRZPayroll
            {
                Id = payrollId,
                CompanyId = companyId,
                Company = null!,
                EmployeeId = employeeId,
                Employee = null!,
                StructureLevelId = structureLevelId,
                StructureLevel = null!,
                Events = []
            };

            payroll.Events.Add(new TRZEvent { Id = duplicateId, WorkTimePayrollId = payrollId, Payroll = payroll, StartDate = new DateTime(2025, 2, 2), EndDate = new DateTime(2025, 2, 3), Duration = 2 });
            payroll.Events.Add(new TRZEvent { Id = duplicateId, WorkTimePayrollId = payrollId, Payroll = payroll, StartDate = new DateTime(2025, 2, 1), EndDate = new DateTime(2025, 2, 5), Duration = 4 });
            payroll.Events.Add(new TRZEvent { Id = uniqueId, WorkTimePayrollId = payrollId, Payroll = payroll, StartDate = new DateTime(2025, 3, 1), EndDate = new DateTime(2025, 3, 1), Duration = 1 });

            var pendingEmployee = new Employee
            {
                Id = employeeId,
                CompanyId = companyId,
                Company = null!,
                UserId = Guid.NewGuid(),
                User = null!,
                PendingPayrolls = [payroll]
            };

            await repository.AddTRZEmployeesAsync([pendingEmployee], companyId);

            await using var assertCtx = await factory.CreateDbContextAsync();
            var dbEmployee = assertCtx.Employees.Include(e => e.PendingPayrolls).ThenInclude(pp => pp.Events).First(e => e.Id == employeeId);
            var events = dbEmployee.PendingPayrolls.First().Events.ToList();
            Assert.Equal(2, events.Count);
            Assert.Contains(events, e => e.Id == duplicateId && e.Duration == 6 && e.StartDate == new DateTime(2025, 2, 1) && e.EndDate == new DateTime(2025, 2, 5));
            Assert.Contains(events, e => e.Id == uniqueId && e.Duration == 1);
        }

        [Fact]
        public async Task AddTRZEmployeesAsync_SingleEvent_RemainsUnchanged()
        {
            var dbName = Guid.NewGuid().ToString();
            var factory = CreateDbFactory(dbName);

            Guid companyId = Guid.Parse("11111111-1111-1111-1111-111111111111");
            Guid structureLevelId = Guid.Parse("*************-2222-2222-************");
            Guid employeeId = Guid.Parse("cccccccc-cccc-cccc-cccc-cccccccccccc");

            await using (var seedCtx = await factory.CreateDbContextAsync())
            {
                await SeedRequiredAsync(seedCtx, companyId, structureLevelId);
                var user = new User { Id = Guid.NewGuid(), Email = "<EMAIL>" };
                seedCtx.Users.Add(user);
                seedCtx.Employees.Add(new Employee
                {
                    Id = employeeId,
                    CompanyId = companyId,
                    Company = seedCtx.Companies.First(c => c.Id == companyId),
                    UserId = user.Id,
                    User = user,
                });
                await seedCtx.SaveChangesAsync();
            }

            var repository = new TRZReporistory(factory);

            var payrollId = Guid.NewGuid();
            var eid = Guid.NewGuid();
            var payroll = new TRZPayroll
            {
                Id = payrollId,
                CompanyId = companyId,
                Company = null!,
                EmployeeId = employeeId,
                Employee = null!,
                StructureLevelId = structureLevelId,
                StructureLevel = null!,
                Events = new List<TRZEvent>()
            };

            payroll.Events.Add(new TRZEvent { Id = eid, WorkTimePayrollId = payrollId, Payroll = payroll, StartDate = new DateTime(2025, 4, 10), EndDate = new DateTime(2025, 4, 12), Duration = 3, EventType = TRZEventType.ПлатенПоДругиЧленове });

            var pendingEmployee = new Employee
            {
                Id = employeeId,
                CompanyId = companyId,
                Company = null!,
                UserId = Guid.NewGuid(),
                User = null!,
                PendingPayrolls = [payroll]
            };

            await repository.AddTRZEmployeesAsync(new List<Employee> { pendingEmployee }, companyId);

            await using var assertCtx = await factory.CreateDbContextAsync();
            var dbEmployee = assertCtx.Employees.Include(e => e.PendingPayrolls).ThenInclude(pp => pp.Events).First(e => e.Id == employeeId);
            var evs = dbEmployee.PendingPayrolls.First().Events.ToList();
            Assert.Single(evs);
            var ev = evs.First();
            Assert.Equal(eid, ev.Id);
            Assert.Equal(3, ev.Duration);
            Assert.Equal(TRZEventType.ПлатенПоДругиЧленове, ev.EventType);
            Assert.Equal(new DateTime(2025, 4, 10), ev.StartDate);
            Assert.Equal(new DateTime(2025, 4, 12), ev.EndDate);
        }

        [Fact]
        public async Task AddTRZEmployeesAsync_MergeNullDurations_TreatsNullAsZero()
        {
            var dbName = Guid.NewGuid().ToString();
            var factory = CreateDbFactory(dbName);

            Guid companyId = Guid.Parse("11111111-1111-1111-1111-111111111111");
            Guid structureLevelId = Guid.Parse("*************-2222-2222-************");
            Guid employeeId = Guid.Parse("dddddddd-dddd-dddd-dddd-dddddddddddd");

            await using (var seedCtx = await factory.CreateDbContextAsync())
            {
                await SeedRequiredAsync(seedCtx, companyId, structureLevelId);
                var user = new User { Id = Guid.NewGuid(), Email = "<EMAIL>" };
                seedCtx.Users.Add(user);
                seedCtx.Employees.Add(new Employee
                {
                    Id = employeeId,
                    CompanyId = companyId,
                    Company = seedCtx.Companies.First(c => c.Id == companyId),
                    UserId = user.Id,
                    User = user,
                });
                await seedCtx.SaveChangesAsync();
            }

            var repository = new TRZReporistory(factory);

            var payrollId = Guid.NewGuid();
            var duplicateId = Guid.NewGuid();
            var payroll = new TRZPayroll
            {
                Id = payrollId,
                CompanyId = companyId,
                Company = null!,
                EmployeeId = employeeId,
                Employee = null!,
                StructureLevelId = structureLevelId,
                StructureLevel = null!,
                Events = new List<TRZEvent>()
            };

            payroll.Events.Add(new TRZEvent { Id = duplicateId, WorkTimePayrollId = payrollId, Payroll = payroll, StartDate = new DateTime(2025, 5, 2), EndDate = new DateTime(2025, 5, 3), Duration = null });
            payroll.Events.Add(new TRZEvent { Id = duplicateId, WorkTimePayrollId = payrollId, Payroll = payroll, StartDate = new DateTime(2025, 5, 1), EndDate = new DateTime(2025, 5, 5), Duration = 4 });

            var pendingEmployee = new Employee
            {
                Id = employeeId,
                CompanyId = companyId,
                Company = null!,
                UserId = Guid.NewGuid(),
                User = null!,
                PendingPayrolls = [payroll]
            };

            await repository.AddTRZEmployeesAsync([pendingEmployee], companyId);

            await using var assertCtx = await factory.CreateDbContextAsync();
            var dbEmployee = assertCtx.Employees.Include(e => e.PendingPayrolls).ThenInclude(pp => pp.Events).First(e => e.Id == employeeId);
            var merged = dbEmployee.PendingPayrolls.First().Events.Single();
            Assert.Equal(4, merged.Duration);
            Assert.Equal(new DateTime(2025, 5, 1), merged.StartDate);
            Assert.Equal(new DateTime(2025, 5, 5), merged.EndDate);
        }
    }
}
