trigger:
  - release
pool:
  vmImage: 'windows-latest'
  name: 'Jurassic'

variables:
  - group: prod-variables
  - name: solution
    value: '**/*.sln'
  - name: buildPlatform
    value: 'Any CPU'
  - name: buildConfiguration
    value: 'Release'
  - name: appPoolName
    value: 'gatewayApiPool'

steps:
- task: UseDotNet@2
  inputs:
    packageType: 'sdk'
    version: '8.x'
    installationPath: $(Agent.ToolsDirectory)/dotnet

- task: NuGetToolInstaller@1

- script: echo Setting ASPNETCORE_ENVIRONMENT variable
  env:
    ASPNETCORE_ENVIRONMENT: 'Production'

- script: dotnet build --configuration $(buildConfiguration)
  displayName: 'Dotnet Build'

# 🔻 Stop IIS App Pool BEFORE deployment
- task: PowerShell@2
  displayName: 'Stop IIS Application Pool'
  inputs:
    targetType: 'inline'
    script: |
      Set-Item WSMan:\localhost\Client\TrustedHosts -Value "$(MachineName)" -Force
      $securePassword = ConvertTo-SecureString "$(Password)" -AsPlainText -Force
      $credentials = New-Object System.Management.Automation.PSCredential ("$(Username)", $securePassword)
      $appPoolName = "$(appPoolName)"
      Invoke-Command -ComputerName $(MachineName) -Credential $credentials -ScriptBlock {
        Import-Module WebAdministration
        $state = (Get-WebAppPoolState -Name $using:appPoolName).Value
        if ($state -ne "Stopped") {
          Stop-WebAppPool -Name $using:appPoolName
        }
      }
      Set-Item WSMan:\localhost\Client\TrustedHosts -Value "" -Force

# 🔻 Remove Roslyn DLLs (optional cleanup)
- task: PowerShell@2
  displayName: 'Remove Unwanted Files'
  inputs:
    targetType: 'inline'
    script: |
      Remove-Item -Path "$(Build.SourcesDirectory)/Gateway/Gateway/bin/Release/net8.0/Microsoft.CodeAnalysis.CSharp.dll" -Force -ErrorAction SilentlyContinue
      Remove-Item -Path "$(Build.SourcesDirectory)/Gateway/Gateway/bin/Release/net8.0/Microsoft.CodeAnalysis.CSharp.Workspaces.dll" -Force -ErrorAction SilentlyContinue
      Remove-Item -Path "$(Build.SourcesDirectory)/Gateway/Gateway/bin/Release/net8.0/Microsoft.CodeAnalysis.dll" -Force -ErrorAction SilentlyContinue
      Remove-Item -Path "$(Build.SourcesDirectory)/Gateway/Gateway/bin/Release/net8.0/Microsoft.CodeAnalysis.Workspaces.dll" -Force -ErrorAction SilentlyContinue

# 🔻 File copy
- task: WindowsMachineFileCopy@2
  displayName: 'Copy Files to Windows Machine'
  inputs:
      SourcePath: '$(Build.SourcesDirectory)/Gateway/bin/Release/net8.0' 
      MachineNames: '$(MachineName)' 
      AdminUserName: '$(Username)'
      AdminPassword: '$(Password)'
      TargetPath: 'C:\inetpub\wwwroot\worktime\gateway-api'

# 🔻 Start IIS App Pool AFTER deployment
- task: PowerShell@2
  displayName: 'Start IIS Application Pool'
  inputs:
    targetType: 'inline'
    script: |
      Set-Item WSMan:\localhost\Client\TrustedHosts -Value "$(MachineName)" -Force
      $securePassword = ConvertTo-SecureString "$(Password)" -AsPlainText -Force
      $credentials = New-Object System.Management.Automation.PSCredential ("$(Username)", $securePassword)
      $appPoolName = "$(appPoolName)"
      Invoke-Command -ComputerName $(MachineName) -Credential $credentials -ScriptBlock {
        Import-Module WebAdministration
        $state = (Get-WebAppPoolState -Name $using:appPoolName).Value
        if ($state -ne "Started") {
          Start-WebAppPool -Name $using:appPoolName
        }
      }
      Set-Item WSMan:\localhost\Client\TrustedHosts -Value "" -Force
