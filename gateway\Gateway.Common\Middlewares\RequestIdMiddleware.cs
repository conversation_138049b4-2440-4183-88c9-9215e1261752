﻿using Microsoft.AspNetCore.Http;
using Serilog.Context;

namespace Gateway.Common.Middlewares
{
    public class RequestIdMiddleware
    {
        private readonly RequestDelegate _next;

        public RequestIdMiddleware(RequestDelegate next)
        {
            _next = next;
        }

        public async Task InvokeAsync(HttpContext context)
        {
            var guid = Guid.NewGuid().ToString();
            context.Response.Headers.Append("X-Request-ID", guid);
            using (LogContext.PushProperty("CustomRequestId", guid))
            {
                await _next(context);
            }
        }
    }
}
