﻿using Microsoft.EntityFrameworkCore;
using SSO.Database.Models;
using SSO.Database.Repositories.Interfaces;

namespace SSO.Database.Repositories
{
    public class RefreshTokenRepository : IRefreshTokenRepository
    {
        private readonly IDbContextFactory<SSODbContext> _contextFactory;

        public RefreshTokenRepository(IDbContextFactory<SSODbContext> contextFactory)
        {
            _contextFactory = contextFactory;
        }

        public async Task<RefreshToken> AddRefreshTokenAsync(RefreshToken refreshToken)
        {
            var context = await _contextFactory.CreateDbContextAsync();
            refreshToken = (await context.RefreshTokens.AddAsync(refreshToken)).Entity;
            await context.SaveChangesAsync();
            return refreshToken;
        }

        public async Task<RefreshToken> GetByTokenAsync(string token)
        {
            var context = await _contextFactory.CreateDbContextAsync();
            var refreshToken = await context.RefreshTokens.FirstOrDefaultAsync(rt => rt.Token == token);
            if (refreshToken is null)
                throw new ArgumentException("Не съществува такъв токън");
            return refreshToken;
        }

        public async Task DeactivateTokenAsync(string tokenStr)
        {
            var context = await _contextFactory.CreateDbContextAsync();

            var token = await context.RefreshTokens.FirstOrDefaultAsync(rt => rt.Token == tokenStr);
            if (token is null)
                return;

            token.IsActive = false;
            token.DeactivatedDate = DateTime.Now;
            await context.SaveChangesAsync();
        }
    }
}
