using WorkTimeApi.Common.Enums;

namespace WorkTimeApi.Common.DTOs.Employees
{
    public class EmployeePropertyEditDTO
    {
        public Guid Id { get; set; }

        public EditSource EditSource { get; set; }

        public EditStatus EditStatus { get; set; }

        public Guid EditorId { get; set; }

        public Guid EmployeeId { get; set; }

        public string ObjectName { get; set; } = string.Empty;

        public Guid ObjectId { get; set; }

        public string PropertyName { get; set; } = string.Empty;

        public string? OldValue { get; set; }

        public string? NewValue { get; set; }

        public DateTime CreatedAt { get; set; }

        public DateTime UpdatedAt { get; set; }
    }
}
