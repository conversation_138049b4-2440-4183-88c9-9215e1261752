﻿using MediatR;
using WorkTime.Notifications.Services.Interfaces;
using WorkTimeApi.Common;
using WorkTimeApi.Common.Enums;
using WorkTimeApi.Common.Notifications.Absences;
using WorkTimeApi.Database.Repositories.Interfaces.Companies;
using WorkTimeApi.Services.Interfaces.Employees;
using WorkTimeApi.Services.Interfaces.Notifications;

namespace WorkTimeApi.Handlers.Notifications.Absences
{
    public class AbsenceConfirmDeleteByEmployeeHandler(ISignalRNotificationService signalRNotificationService,
        INotificationsService notificationsService,
        IEmployeesService employeesService,
        IEmailsNotificationService emailsNotificationService,
        ICompaniesRepository companiesRepository,
        IConfiguration configuration) : INotificationHandler<AbsenceDeleteRequestByEmployeeNotification>
    {
        public async Task Handle(AbsenceDeleteRequestByEmployeeNotification notification, CancellationToken cancellationToken)
        {
            var employee = await employeesService.GetUserEmployeeAsync(notification.UserId, notification.CompanyId)
                           ?? throw new Exception("Невалиден служител!");

            var employeesToNotify = await notificationsService.GetEmployeesToNotifyAsync(
                notification.CompanyId,
                NotificationsName.Absences.DeleteRequestByEmployee.Email
            );

            var emails = employeesToNotify.Select(e => e.Email);

            var company = await companiesRepository.GetByIdAsync(notification.CompanyId) ?? throw new Exception("Невалидна компания!");

            var fullName = string.Join(" ", new[] { employee.FirstName, employee.LastName }
            .Where(name => !string.IsNullOrWhiteSpace(name)));

            var worktimeUrl = configuration.GetValue<string>("WorkTimeUrl") ?? throw new Exception("Липсва конфигурация за WorkTimeUrl!");
            var absence = notification.Payload;

            var emailRequest = new
            {
                Name = fullName,
                CompanyName = company.Name,
                AbsenceType = absence.TypeIdentifier.GetDescription(),
                StartDate = absence.FromDate.ToString("dd.MM.yyyy"),
                EndDate = absence.ToDate.ToString("dd.MM.yyyy"),
                Comment = absence.Reference,
                Url = $"{worktimeUrl}{notification.Url}"
            };
            var emailNotifications = emailsNotificationService.SendEmailsAsync(emails, "absences/delete-byemployee-request", emailRequest);

            var notificationTasks = employeesToNotify
            .Select(async emp =>
            {
                var savedNotification = await notificationsService.AddNotificationByUserIdAndCompanyIdAsync(emp.WorkTimeId, notification);
                await signalRNotificationService.NotifyUser(emp.UserId, savedNotification);
            });

            await Task.WhenAll(notificationTasks.Append(emailNotifications));
        }
    }
}