﻿using MediatR;
using WorkTime.Notifications.Services.Interfaces;
using WorkTimeApi.Common.Notifications.Absences;
using WorkTimeApi.Common.Notifications.SignalR;
using WorkTimeApi.Database.Repositories.Interfaces.Employees;

namespace WorkTimeApi.Handlers.Notifications.SignalR
{
    public class AddPayrollsHandler(ISignalRNotificationService signalRNotificationService, IEmployeesRepository employeesRepository)
        : INotificationHandler<AddPayrollsNotification>
    {
        public async Task Handle(AddPayrollsNotification notification, CancellationToken cancellationToken)
        {
            var pushNotifications = signalRNotificationService.BroadcastForCompanyAsync(notification);

            await Task.WhenAll(pushNotifications);
        }
    }
}
