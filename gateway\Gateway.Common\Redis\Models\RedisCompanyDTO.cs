﻿namespace Gateway.Common.Redis.Models
{
    public class RedisCompanyDTO : IRedisRecord
    {
        public int Id { get; set; }

        public required string Name { get; set; }

        public string? ContactName { get; set; }

        public string? Address { get; set; }

        public string? Country { get; set; }

        public required string Bulstat { get; set; }

        public string? VatNumber { get; set; }

        public List<RedisCompanyUserDTO> Users { get; set; } = [];

        public List<string> GetKeys()
        {
            return [Id.ToString()];
        }
    }
}
