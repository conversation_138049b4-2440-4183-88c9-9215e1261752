import { useEffect, useState } from "react";
import { useNavigate, useParams } from "react-router-dom";
import Alert from "../../components/Inputs/Alert";
import { confirmEmail } from "../../services/authentication/authenticationService";

const ConfirmEmail = () => {
  const [isConfirmed, setIsConfirmed] = useState<boolean | undefined>();
  const { userId, code } = useParams();
  const navigate = useNavigate();

  useEffect(() => {
    userId &&
      code &&
      confirmEmail(userId, code)
        .then(() => {
          setIsConfirmed(true);
          setTimeout(() => navigate("/auth/login"), 3000);
        })
        .catch(() => {
          setIsConfirmed(false);
        });
  });

  const handleConfirmation = () => {
    if (isConfirmed === undefined)
      return <Alert type="warning" message="Confirming E-mail" />;
    else if (isConfirmed) {
      navigate("/company/create");
      return <Alert type="success" message="E-mail confirmed successfully" />;
    } else return <Alert type="error" message="E-mail was not confirmed!" />;
  };

  return <>{handleConfirmation()}</>;
};

export default ConfirmEmail;
