﻿using SSO.Common.DTOs;
using SSO.Connections.Interfaces;

namespace SSO.Connections
{
	public class UserRegistrationsConnection : IUserRegistrationsConnection
	{
		private readonly HttpClient _httpClient;
		private readonly string _userRegistrationsApiUrl;

		public UserRegistrationsConnection(HttpClient httpClient, IConfiguration configuration)
		{
			_httpClient = httpClient;
			_userRegistrationsApiUrl = configuration["UserRegistrationsBaseUrl"] + "api/users/";
		}

		public Task<string> TryLoginAsync(string authorizationHeader)
		{
			_httpClient.DefaultRequestHeaders.Add("Authorization", authorizationHeader);

			return _httpClient.GetStringAsync($"{_userRegistrationsApiUrl}userDTO");
		}

		public async Task<HttpResponseMessage> TryLoginWithResponseAsync(string authorizationHeader)
		{
			_httpClient.DefaultRequestHeaders.Add("Authorization", authorizationHeader);

			return await _httpClient.GetAsync($"{_userRegistrationsApiUrl}userDTO");
		}

		public Task<HttpResponseMessage> TryGetUserOrCreateItAsync(UserDTO userDTO)
		{
			return _httpClient.PostAsJsonAsync($"{_userRegistrationsApiUrl}try-get-user-or-create-it", userDTO);
		}

		public Task<HttpResponseMessage> ChangeForgottenPasswordAsync(ChangeForgottenPasswordDTO changeForgottenPasswordDTO)
		{
			return _httpClient.PostAsJsonAsync($"{_userRegistrationsApiUrl}change-forgotten-password", changeForgottenPasswordDTO);
		}
	}
}
