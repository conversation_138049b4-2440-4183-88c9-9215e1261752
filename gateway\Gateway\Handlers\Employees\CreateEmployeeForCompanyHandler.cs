﻿using AutoMapper;
using Gateway.Connections.Interfaces;
using MediatR;
using Microinvest.TransferFiles.Tools.Models.Users;
using WorkTimeApi.Common.DTOs.Employees;
using WorkTimeApi.Common.Requests.Employees;

namespace Gateway.Handlers.Employees
{
    public class CreateEmployeeForCompanyHandler : IRequestHandler<CreateEmployeeForCompanyRequest, IResult>
    {
        private readonly IMapper _mapper;
        private readonly IUserRegistrationConnection _userRegistrationConnection;
        private readonly IWorkTimeApiConnection _workTimeApiConnection;

        public CreateEmployeeForCompanyHandler(IMapper mapper, 
            IUserRegistrationConnection userRegistrationConnection, 
            IWorkTimeApiConnection workTimeApiConnection)
        {
            _mapper = mapper;
            _userRegistrationConnection = userRegistrationConnection;
            _workTimeApiConnection = workTimeApiConnection;
        }

        public async Task<IResult> Handle(CreateEmployeeForCompanyRequest request, CancellationToken cancellationToken)
        {
            if (request.HttpRequest is null)
                return Results.BadRequest();

            var registerResponse = await _userRegistrationConnection.ConfirmedRegisterEmployeeByEmailAsync(_mapper.Map<UserDTO>(request.EmployeeDTO));
            if (registerResponse is null || !registerResponse.IsSuccessStatusCode)
                return Results.BadRequest();

            var userDTO = await registerResponse.Content.ReadFromJsonAsync<UserDTO>();
            if (userDTO is null)
                return Results.BadRequest();

            request.EmployeeDTO.WorkTimeId = new Guid(userDTO.Id);
            var createResponse = await _workTimeApiConnection.CreateEmployeeForCompanyAsync(request);
            if (createResponse is null || !createResponse.IsSuccessStatusCode)
                return Results.BadRequest();

            var createdEmployee = await createResponse.Content.ReadFromJsonAsync<EmployeeDTO>();
            if (createdEmployee is null)
                return Results.BadRequest();

            return Results.Ok(createdEmployee);
        }
    }
}
