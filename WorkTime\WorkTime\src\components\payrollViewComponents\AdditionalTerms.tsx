import React from "react";
import styled from "styled-components";
import Container from "../Container";
import Fieldset from "../Fiedlset/Fieldset";
import Legend from "../Fiedlset/Legend";
import Label from "../Inputs/Label";

const AdditionalTermsContainer = styled(Container)`
  width: 100%;
  display: flex;
  flex-direction: column;
  min-height: calc(100vh - 35rem);
`;

const AdditionalTermsFieldset = styled(Fieldset)`
  border: 0.2rem solid var(--profile-fieldset-border-color);
  margin: 1rem;
  padding: 1rem;
  position: relative;
  flex: 1;
  display: flex;
  flex-direction: column;
`;

const FieldsetRow = styled(Container)`
  display: flex;
  flex-direction: row;
  align-items: center;
  margin: 0.2rem;
  width: 100%;
`;

const ValueColumn = styled(Container)`
  display: flex;
  align-items: center;
  width: 100%;
`;

const ValueLabel = styled(Label)`
  font-size: 1rem;
  text-align: left;
  width: 100%;
`;

interface AdditionalTermsProps {
  data: any;
  tabType: "current" | "annex" | "main" | "additional-terms";
}

const AdditionalTerms = ({ data, tabType }: AdditionalTermsProps) => {
  return (
    <AdditionalTermsContainer>
      <AdditionalTermsFieldset>
        <Legend>Additional terms</Legend>
        <FieldsetRow>
          <ValueColumn>
            <ValueLabel>{data?.additionalTerms || ""}</ValueLabel>
          </ValueColumn>
        </FieldsetRow>
      </AdditionalTermsFieldset>
    </AdditionalTermsContainer>
  );
};

export default AdditionalTerms;
