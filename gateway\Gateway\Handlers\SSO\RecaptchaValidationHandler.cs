﻿using Gateway.Common.Results;
using Gateway.Connections.Interfaces;
using MediatR;
using SSO.Common.Requests;

namespace Gateway.Handlers.SSO
{
    public class RecaptchaValidationHandler : IRequestHandler<RecaptchaValidationRequest, IResult>
    {
        private readonly ISSOConnection _ssoConnection;

        public RecaptchaValidationHandler(ISSOConnection ssoConnection)
        {
            _ssoConnection = ssoConnection;
        }

        public async Task<IResult> Handle(RecaptchaValidationRequest request, CancellationToken cancellationToken)
        {
            return new HttpResponseMessageResult(await _ssoConnection.ValidateRecaptchaToken(request));
        }
    }
}
