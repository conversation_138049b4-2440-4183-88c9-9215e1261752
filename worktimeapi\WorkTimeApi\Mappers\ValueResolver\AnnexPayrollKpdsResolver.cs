﻿using AutoMapper;
using WorkTimeApi.Common.DTOs.Payrolls;
using WorkTimeApi.Database.Models.TRZ;

namespace WorkTimeApi.Mappers.ValueResolver
{
    public class AnnexPayrollKpdsResolver : IValueResolver<AnnexPayrollDTO, TRZAnnexPayroll, ICollection<TRZAnnexPayrollKPD>>
    {
        public ICollection<TRZAnnexPayrollKPD> Resolve(AnnexPayrollDTO src, TRZAnnexPayroll dest, ICollection<TRZAnnexPayrollKPD> destMember, ResolutionContext context)
        {
            var kpds = new List<TRZAnnexPayrollKPD>();

            if (src.Kpds != null)
            {
                kpds.AddRange(src.Kpds
                    .Where(kpd => kpd is not null && kpd.Id != Guid.Empty)
                    .Select(kpd => new TRZAnnexPayrollKPD
                    {
                        AnnexPayroll = dest,
                        KPDId = kpd.Id
                    }));
            }

            return kpds;
        }
    }
}
