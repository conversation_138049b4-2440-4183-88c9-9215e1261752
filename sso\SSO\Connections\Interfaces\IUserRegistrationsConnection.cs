﻿using SSO.Common.DTOs;

namespace SSO.Connections.Interfaces
{
	public interface IUserRegistrationsConnection
	{
		Task<string> TryLoginAsync(string authorizationHeader);

		Task<HttpResponseMessage> TryLoginWithResponseAsync(string authorizationHeader);

		Task<HttpResponseMessage> TryGetUserOrCreateItAsync(UserDTO userDTO);

		Task<HttpResponseMessage> ChangeForgottenPasswordAsync(ChangeForgottenPasswordDTO changeForgottenPasswordDTO);
	}
}
