﻿using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Http.HttpResults;
using Moq;
using SSO.Handlers;
using SSO.Common.Requests;
using SSO.Services.Interfaces;

namespace SSO.Tests.SSO.Handlers
{
    public class LogoutHandlerTests
    {
        [Fact]
        public async Task LogoutReturnsBadRequestWhenHttpContextIsNull()
        {
            // Arrange
            var refreshTokensServiceMock = new Mock<IRefreshTokensService>();
            var logoutRequest = new LogoutRequest
            {
                HttpRequest = null
            };

            var logoutHandler = new LogoutHandler(refreshTokensServiceMock.Object);
            var cancellationToken = new CancellationToken();

            // Act
            var response = await logoutHandler.Handle(logoutRequest, cancellationToken);
            var badRequestResponse = (BadRequest)response;

            // Assert
            Assert.Equal(400, badRequestResponse.StatusCode);
        }

        [Fact]
        public async Task LogoutRemovesAllHeadersFromResponseAndReturnsOk()
        {
            // Arrange
            var refreshTokensServiceMock = new Mock<IRefreshTokensService>();
            var logoutRequest = new LogoutRequest
            {
                HttpRequest = new DefaultHttpContext().Request
            };

            logoutRequest.HttpRequest.Headers.Add("Authorization", "Bearer authorization");
            logoutRequest.HttpRequest.Headers.Add("Refresh-Token", "refreshToken");

            var logoutHandler = new LogoutHandler(refreshTokensServiceMock.Object);
            var cancellationToken = new CancellationToken();

            // Act
            var response = await logoutHandler.Handle(logoutRequest, cancellationToken);
            var okResponse = (Ok)response;

            // Assert
            Assert.Equal(200, okResponse.StatusCode);
            Assert.False(logoutRequest.HttpRequest.HttpContext.Response.Headers.Any());
        }
    }
}
