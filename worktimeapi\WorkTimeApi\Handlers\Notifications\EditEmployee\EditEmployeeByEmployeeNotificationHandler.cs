﻿using MediatR;
using WorkTime.Notifications.Services.Interfaces;
using WorkTimeApi.Common;
using WorkTimeApi.Common.Notifications.EditEmployee;
using WorkTimeApi.Database.Repositories.Interfaces.Companies;
using WorkTimeApi.Services.Interfaces.Notifications;

namespace WorkTimeApi.Handlers.Notifications.EditEmployee
{
    public class EditEmployeeByEmployeeNotificationHandler(ISignalRNotificationService signalRNotificationService,
        INotificationsService notificationsService,
        IEmailsNotificationService emailsNotificationService,
        ICompaniesRepository companiesRepository,
        IConfiguration configuration) : INotificationHandler<EditEmployeeByEmployeeNotification>
    {
        public async Task Handle(EditEmployeeByEmployeeNotification notification, CancellationToken cancellationToken)
        {
            var employeesToNotify = await notificationsService.GetEmployeesToNotifyAsync(notification.CompanyId, NotificationsName.Employees.EditedByEmployee.Email);

            var employee = notification.Payload ?? throw new Exception("Няма данни за заявени отсъствия");
            var company = await companiesRepository.GetByIdAsync(notification.CompanyId) ?? throw new Exception("Невалидна компания!");
            var fullName = string.Join(" ", new[] { employee.FirstName, employee.LastName }
                           .Where(name => !string.IsNullOrWhiteSpace(name)));

            var worktimeUrl = configuration.GetValue<string>("WorkTimeUrl") 
                ?? throw new Exception("Невалидна настройка на WorkTimeUrl в appsettings!");

            var emailRequest = new
            {
                Name = fullName,
                CompanyName = company.Name,
                EditedTabName = "Лични данни",
                Url = $"{worktimeUrl}{notification.Url}"
            };

            var emails = employeesToNotify
               .Select(e => e.Email)
               .Where(e => !string.IsNullOrWhiteSpace(e))
               .ToList();

            var notificationTasks = employeesToNotify
            .Select(async emp =>
            {
                var savedNotification = await notificationsService.AddNotificationByUserIdAndCompanyIdAsync(emp.WorkTimeId, notification);
                await signalRNotificationService.NotifyUser(emp.UserId, savedNotification);
            });

            var emailNotifications = emailsNotificationService.SendEmailsAsync(emails, "notifications/employees/updated", emailRequest);

            await Task.WhenAll(notificationTasks.Append(emailNotifications));
        }
    }
}